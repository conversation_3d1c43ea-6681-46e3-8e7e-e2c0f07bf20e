SERVICE_NAME=catalog
COVERPROFILE=coverage.out
IMAGE=boaweb/$(SERVICE_NAME)

build: 
	GOOS=linux GOARCH=amd64 CGO_ENABLED=0 go build -o ./bin/$(SERVICE_NAME) ./cmd/$(SERVICE_NAME)

clean:
	rm -f "./bin/$(SERVICE_NAME)"

test:
	go test -coverprofile=$(COVERPROFILE) ./...

repotest:
	go test -coverprofile=$(COVERPROFILE) ./repo $(DBTESTARGS)

cov:
	go tool cover -html=$(COVERPROFILE)

image: clean build
	docker build --tag "$(IMAGE)" .

dev:
	go run github.com/joho/godotenv/cmd/godotenv@latest -f .env.local,.env go run ./cmd/$(SERVICE_NAME)

watch:
	ulimit -n 10000
	go run github.com/cespare/reflex@latest -s -r '\.go$$' make dev

# Force-regenerate mocks
mocks:
	find . -name "*mock.go" -type f -exec go generate {} \;

# Bundle and generate openapi spec
ensure-redocly:
	@command -v redocly >/dev/null 2>&1 || { \
		echo "Redocly not found. Installing..."; \
		npm install -g @redocly/cli; \
	}
openapi: ensure-redocly
	redocly bundle open-api/index.yaml --config open-api/redocly.yaml --output open-api/openapi.yaml
	redocly build-docs open-api/openapi.yaml --config open-api/redocly.yaml --output open-api/api_reference.html

# Regenerate meta-meta JSON schema required for data model validation
regen-metameta:
	go run ./cmd/schema-gen


.PHONY: clean test cov image dev watch mocks
