// Package client defines a sample client to serve as an example for future
// services.
package client

import (
	"context"

	"github.com/BackOfficeAssoc/pkg/guac"
)

type Client struct {
	guac.Guac
	BaseURL string
}

func (c *Client) WithTokener(t guac.Tokener) *Client {
	return &Client{
		BaseURL: c.BaseURL,
		Guac:    guac.Guac{Tokens: t},
	}
}

func (c *Client) do(ctx context.Context, method, url string, body, res interface{}) error {
	return c.Do(ctx, method, c.BaseURL+url, body, res)
}
