package client

import (
	"context"
	"fmt"
	"net/http"

	"github.com/BackOfficeAssoc/catalog/models"
)

// CreateSample creates an Sample
func (c *Client) CreateSample(ctx context.Context, req *models.CreateSampleInput) (*models.Sample, error) {
	var res models.Sample
	if err := c.do(ctx, http.MethodPost, "/samples", req, &res); err != nil {
		return nil, err
	}
	return &res, nil
}

// FindSamples fetches a list of samples
func (c *Client) FindSamples(ctx context.Context, query *models.SampleQuery) ([]*models.Sample, error) {
	q, err := query.Query()
	if err != nil {
		return nil, err
	}
	var res []*models.Sample
	if err := c.do(ctx, http.MethodGet, "/samples?"+q.Encode(), nil, &res); err != nil {
		return nil, err
	}
	return res, nil
}

// FetchSample fetches a sample by ID.
func (c *Client) FetchSample(ctx context.Context, id string) (*models.Sample, error) {
	var res models.Sample
	path := fmt.Sprintf("/samples/%s", id)

	if err := c.do(ctx, http.MethodGet, path, nil, &res); err != nil {
		return nil, err
	}
	return &res, nil
}

// UpdateSample updates a sample
func (c *Client) UpdateSample(ctx context.Context, id string, req *models.UpdateSampleInput) (*models.Sample, error) {
	var res models.Sample
	url := fmt.Sprintf("/samples/%s", id)
	if err := c.do(ctx, http.MethodPut, url, req, &res); err != nil {
		return nil, err
	}
	return &res, nil
}

// DeleteSample fetches a sample by ID.
func (c *Client) DeleteSample(ctx context.Context, id string) error {
	return c.do(ctx, http.MethodDelete, "/samples/"+id, nil, nil)
}
