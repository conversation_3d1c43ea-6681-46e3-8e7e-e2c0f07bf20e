package models

import (
	"encoding/json"
	"fmt"
	"strings"
)

// A Component is the information we receive from the scanner file. The
// collection of Components we receive describes a directed graph. Any value
// that could be unspecified in the source JSON should be a pointer value in
// this model, allowing it to be nil the user didn't provide a value.
type Component struct {
	Name              *string                `json:"name"`
	Description       *string                `json:"description"`
	Encoding          *string                `json:"encoding"`
	ComponentType     *string                `json:"component_type"`
	CustomProperties  map[string]interface{} `json:"custom_properties"`
	LocationPath      *string                `json:"location_path"`
	ActionOnDelete    *string                `json:"action_on_delete"`
	ActionOnUpdate    *string                `json:"action_on_update"`
	AllowNull         *bool                  `json:"allow_null"`
	ConstrainedOn     *string                `json:"constrained_on"`
	DataType          *string                `json:"data_type"`
	DefaultValue      *string                `json:"default_value"`
	FilterCondition   *string                `json:"filter_condition"`
	FilterColumn      *string                `json:"filter_column"`
	InputOutput       *string                `json:"input_output"`
	IsDescending      *bool                  `json:"is_descending"`
	IsClustered       *bool                  `json:"is_clustered"`
	IsUnique          *bool                  `json:"is_unique"`
	Key               *bool                  `json:"key"`
	OrdinalPosition   *int                   `json:"ordinal_position"`
	Precision         *string                `json:"precision"`
	ReferenceTo       *string                `json:"reference_to"`
	Scale             *int                   `json:"scale"`
	Size              *string                `json:"size"`
	Source            *string                `json:"source"`
	Target            *string                `json:"target"`
	Usage             *string                `json:"usage"`
	Validate          *bool                  `json:"validate"`
	IsField           *bool                  `json:"is_field"`
	TextColumn        *string                `json:"text_column"`
	LanguageColumn    *string                `json:"language_column"`
	XID               string                 `json:"xid"`
	Components        []string               `json:"components"`
	AllowableValues   []string               `json:"allowable_values"`
	Filters           []string               `json:"filters"`
	IncludedColumns   []string               `json:"included_columns"`
	ReferenceData     []string               `json:"reference_data"`
	ReferenceElements []string               `json:"reference_elements"`
	Tags              []string               `json:"tags"`
	ConsumptionType   ConsumptionType        `json:"consumption_type"`
	// MaybePartialRoot is set when the Component has a Name that matches the
	// specification of a Partial Root. When this is set, the Graph of the
	// System has not been built and so we can't determine if the Component is
	// actually a partial root because we can't see its Parent. We'll use this
	// property later to determine if this Component is indeed a partial root of
	// the System.
	MaybePartialRoot bool `json:"maybe_partial_root"`
}

type ConsumptionType int8

const (
	ConsumptionTypeUnknown ConsumptionType = iota
	ConsumptionTypeConstraint
	ConsumptionTypeIndex
	ConsumptionTypeKey
	ConsumptionTypeLookup
	ConsumptionTypeDatabase
	ConsumptionTypeSchema
	ConsumptionTypeTable
	ConsumptionTypeView
	ConsumptionTypeColumn
)

var (
	consumptionTypes = [...]string{
		ConsumptionTypeConstraint: "constraint",
		ConsumptionTypeIndex:      "index",
		ConsumptionTypeKey:        "key",
		ConsumptionTypeLookup:     "lookup",
		ConsumptionTypeDatabase:   "database",
		ConsumptionTypeSchema:     "schema",
		ConsumptionTypeTable:      "table",
		ConsumptionTypeView:       "view",
		ConsumptionTypeColumn:     "column",
	}
)

// ConsumptionTypes returns a slice of all valid ConsumptionTypes for
// Components.
func ConsumptionTypes() []ConsumptionType {
	var types []ConsumptionType
	for i := int(ConsumptionTypeUnknown + 1); i < len(consumptionTypes); i++ {
		types = append(types, ConsumptionType(i))
	}
	return types
}

func (c *ConsumptionType) String() string {
	if c == nil || *c < 0 || int(*c) >= len(consumptionTypes) {
		return ""
	}
	return consumptionTypes[*c]
}

func (c *ConsumptionType) Parse(input string) error {
	*c = ConsumptionType(0)
	if input == "" {
		return nil
	}
	for i, v := range consumptionTypes {
		if strings.ToLower(input) == v {
			*c = ConsumptionType(i)
			return nil
		}
	}
	return fmt.Errorf("failed to parse '%s' as consumption type", input)
}

func (c ConsumptionType) MarshalJSON() ([]byte, error) {
	return json.Marshal(c.String())
}

func (c *ConsumptionType) UnmarshalJSON(bb []byte) error {
	var val string
	if err := json.Unmarshal(bb, &val); err != nil {
		return err
	}
	val = strings.ToLower(val)
	return c.Parse(val)
}
