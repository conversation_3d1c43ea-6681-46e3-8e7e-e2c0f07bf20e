package models

import (
	"encoding/json"
	"fmt"
	"net/url"
	"strings"

	"github.com/google/go-querystring/query"
)

type Sample struct {
	ID        string     `json:"id"`
	CreatedBy string     `json:"created_by"`
	Name      string     `json:"name"`
	TenantID  string     `json:"tenant_id"`
	Type      SampleType `json:"type"`
}

type CreateSampleInput struct {
	Type SampleType `json:"type"`
	Name string     `json:"name"`
	// TenantID is derived from the auth-token when not provided. If, however,
	// request comes from an internal service TenantID must be specified
	// because service tokens are not scoped to a tenant (i.e. TenantID can't
	// be derived).
	TenantID *string `json:"tenant_id"`
}

type UpdateSampleInput struct {
	Name string `json:"name"`
}

type SampleQuery struct {
	CreatedBy *string `form:"created_by" url:"created_by"`
	Name      *string `form:"name" url:"name"`
	TenantID  *string `form:"tenant_id" url:"tenant_id"`
	Type      *string `form:"type" url:"type"`
}

func (p *SampleQuery) Query() (url.Values, error) {
	return query.Values(p)
}

// SampleType represents the job type
type SampleType int

const (
	SampleTypeUnknown SampleType = iota
	SampleTypeFoo
	SampleTypeBar
)

// SampleTypes returns a slice of all valid SampleTypes.
func SampleTypes() []SampleType {
	var s []SampleType
	for i := SampleTypeUnknown + 1; i <= SampleTypeBar; i++ {
		s = append(s, SampleType(i))
	}
	return s
}

var (
	sampleTypes = [...]string{
		SampleTypeFoo: "Foo",
		SampleTypeBar: "Bar",
	}
)

func (s SampleType) String() string {
	if s < 1 || int(s) >= len(sampleTypes) {
		return ""
	}
	return sampleTypes[s]
}

func (s SampleType) MarshalJSON() ([]byte, error) {
	return json.Marshal(s.String())
}

func (s *SampleType) UnmarshalJSON(bb []byte) error {
	var input string
	if err := json.Unmarshal(bb, &input); err != nil {
		return err
	}
	return s.Parse(input)
}

func (s *SampleType) Parse(input string) error {
	*s = SampleType(0)
	if input == "" {
		return nil
	}
	for k, v := range sampleTypes {
		if strings.ToLower(input) == strings.ToLower(v) {
			*s = SampleType(k)
			return nil
		}
	}
	return fmt.Errorf("failed to parse '%s' as SampleType", input)
}
