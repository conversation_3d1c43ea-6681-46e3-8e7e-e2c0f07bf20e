# Binaries for programs and plugins
go-template-service
*.exe
*.exe~
*.dll
*.so
*.dylib
bin/

# Output of the go coverage tool, specifically when used with LiteIDE
*.out
coverage/

# Environment specific files
.DS_Store

# Dependencies
vendor/

# File that defines sensitive environment variables
.env.local

ci/volume

# revive and gosec scans files
.scans/
revive.toml

# ignore vscode workspace settings
.vscode/

# Ignore PostgreSQL data directory
postgres-data/