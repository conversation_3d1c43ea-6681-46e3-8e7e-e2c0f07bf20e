package metameta

import (
	"encoding/json"
	"os"
	"strings"
	"testing"

	"github.com/stretchr/testify/suite"
	"github.com/xeipuuv/gojsonschema"
)

const (
	// Path to the base schema file
	baseSchemaPath = "json_schema/base_datamodel_schema.json"
	// Path to the data source model file
	relationalModelDefinitionPath = "definitions/relational_meta.json"
)

type SchemaHelperTestSuite struct {
	suite.Suite
	tempDir string
}

func TestSchemaHelperSuite(t *testing.T) {
	suite.Run(t, new(SchemaHelperTestSuite))
}

func (s *SchemaHelperTestSuite) TearDownTest() {
	// Clean up temporary directory after each test
	os.RemoveAll(s.tempDir)
}

func (s *SchemaHelperTestSuite) TestEnhanceEntityDefinition() {
	// Test data
	schema := map[string]any{
		"definitions": map[string]any{
			"entity": map[string]any{
				"properties": map[string]any{
					"type": map[string]any{},
				},
			},
		},
	}

	entityTypes := []EntityType{
		{
			Name: "TestEntity",
			Properties: map[string]any{
				"testProp": map[string]any{
					"type": "string",
				},
			},
			Required: []string{"testProp"},
		},
	}

	err := enhanceEntityDefinition(schema, entityTypes)
	s.Require().NoError(err)

	// Verify the schema was enhanced correctly
	defs := schema["definitions"].(map[string]any)
	entDef := defs["entity"].(map[string]any)
	props := entDef["properties"].(map[string]any)
	tprop := props["type"].(map[string]any)

	s.Contains(tprop["enum"], "TestEntity")
	s.Contains(entDef["required"], "model_name")

	// Verify if/then block for TestEntity
	allOf := entDef["allOf"].([]any)
	found := false
	for _, block := range allOf {
		blockMap := block.(map[string]any)
		if ifBlock, ok := blockMap["if"].(map[string]any); ok {
			if props, ok := ifBlock["properties"].(map[string]any); ok {
				if typeProps, ok := props["type"].(map[string]any); ok {
					if typeProps["const"] == "TestEntity" {
						found = true
						break
					}
				}
			}
		}
	}
	s.True(found, "Expected to find if/then block for TestEntity")
}

func (s *SchemaHelperTestSuite) TestEnhanceEntityElementDefinition() {
	schema := map[string]any{
		"definitions": map[string]any{
			"entity_element": map[string]any{
				"properties": map[string]any{
					"type": map[string]any{},
				},
			},
		},
	}

	elementTypes := []ElementType{
		{
			Name: "TestElement",
			Properties: map[string]any{
				"testProp": map[string]any{
					"type": "string",
				},
			},
			Required: []string{"testProp"},
		},
	}

	err := enhanceEntityElementDefinition(schema, elementTypes)
	s.Require().NoError(err)

	// Verify the schema was enhanced correctly
	defs := schema["definitions"].(map[string]any)
	elemDef := defs["entity_element"].(map[string]any)
	props := elemDef["properties"].(map[string]any)
	tprop := props["type"].(map[string]any)

	s.Contains(tprop["enum"], "TestElement")
	s.Contains(elemDef["required"], "name")
}

func (s *SchemaHelperTestSuite) TestEnhanceRelationshipDefinition() {
	schema := map[string]any{
		"definitions": map[string]any{
			"relationship": map[string]any{
				"properties": map[string]any{
					"type": map[string]any{},
				},
			},
		},
	}

	relationshipTypes := []RelationshipType{
		{
			Name:       "TestRelationship",
			SourceType: "SourceEntity",
			TargetType: "TargetEntity",
			Properties: map[string]any{
				"testProp": map[string]any{
					"type": "string",
				},
			},
		},
	}

	err := enhanceRelationshipDefinition(schema, relationshipTypes)
	s.Require().NoError(err)

	// Verify the schema was enhanced correctly
	defs := schema["definitions"].(map[string]any)
	relDef := defs["relationship"].(map[string]any)
	props := relDef["properties"].(map[string]any)
	tprop := props["type"].(map[string]any)

	s.Contains(tprop["enum"], "TestRelationship")
}

func (s *SchemaHelperTestSuite) TestGenerateDataModelSchema() {
	// 1. Load the "data-source model"
	dsBytes, err := os.ReadFile(relationalModelDefinitionPath)
	s.Require().NoError(err)

	// 2. Load base schema
	baseSchemaBytes, err := os.ReadFile(baseSchemaPath)
	s.Require().NoError(err)

	// 3. Generate final schema in memory
	generatedSchema, err := GenerateDataModelSchema(baseSchemaBytes, dsBytes)
	s.Require().NoError(err, "Failed to generate final schema")

	// 4. Load the data_model_database.json to validate
	dataBytes, err := os.ReadFile("testdata/data_model_database.json")
	s.Require().NoError(err)

	schemaLoader := gojsonschema.NewStringLoader(string(generatedSchema))
	documentLoader := gojsonschema.NewStringLoader(string(dataBytes))

	// 5. Validate
	result, err := gojsonschema.Validate(schemaLoader, documentLoader)
	s.Require().NoError(err, "Schema validation process failed")

	if !result.Valid() {
		var errors []string
		for _, desc := range result.Errors() {
			errors = append(errors, desc.String())
		}
		s.Fail("Schema validation failed:\n" + strings.Join(errors, "\n"))
	}
}

func (s *SchemaHelperTestSuite) TestBuildPropertiesSubschema() {
	props := map[string]any{
		"testProp": map[string]any{
			"type": "string",
		},
	}
	required := []string{"testProp"}

	result := buildPropertiesSubschema(props, required)

	s.Equal("object", result["type"])
	s.Equal(props, result["properties"])
	s.Equal(required, result["required"])
	s.Equal(false, result["additionalProperties"])
}
func (s *SchemaHelperTestSuite) TestGeneratedSchemaMatchesExpected() {
	// 1. Load the unified "data-source model"
	dsBytes, err := os.ReadFile(relationalModelDefinitionPath)
	s.Require().NoError(err, "Failed to load relational_meta.json")

	// 2. Load base schema
	baseSchemaBytes, err := os.ReadFile(baseSchemaPath)
	s.Require().NoError(err, "Failed to load base_datamodel_schema.json")

	// 3. Generate the final schema in memory
	generatedSchema, err := GenerateDataModelSchema(baseSchemaBytes, dsBytes)
	s.Require().NoError(err, "Failed to generate final schema")

	/*
		TODO: Discuss with Jean-Max
		// 4. Write the generated unified schema to file
		err = os.MkdirAll("generated", 0755)
		s.Require().NoError(err, "Failed to create generated directory")

		err = os.WriteFile("generated/relational_schema.json", generatedSchema, 0644)
		s.Require().NoError(err, "Failed to write relational schema to file")
	*/

	// 5. Unmarshal the generated schema so we can verify it's valid JSON
	var generated any
	err = json.Unmarshal(generatedSchema, &generated)
	s.Require().NoError(err, "Failed to unmarshal generated schema")
}

// TestGenerateUnifiedSchema replaces the separate SQL and SAP schema generation tests
func (s *SchemaHelperTestSuite) TestGenerateUnifiedSchema() {
	// 1. Load the unified relational database model
	dsBytes, err := os.ReadFile(relationalModelDefinitionPath)
	s.Require().NoError(err, "Failed to load relational_meta.json")

	// 2. Load base schema
	baseSchemaBytes, err := os.ReadFile(baseSchemaPath)
	s.Require().NoError(err, "Failed to load base_datamodel_schema.json")

	// 3. Generate the unified schema
	generatedSchema, err := GenerateDataModelSchema(baseSchemaBytes, dsBytes)
	s.Require().NoError(err, "Failed to generate unified schema")

	/*
		TODO: Discuss with Jean-Max
		// 4. Write the generated schema to file
		err = os.MkdirAll("generated", 0755)
		s.Require().NoError(err, "Failed to create generated directory")

		err = os.WriteFile("generated/relational_schema.json", generatedSchema, 0644)
		s.Require().NoError(err, "Failed to write unified schema to file")

	*/
	// 5. Verify the schema has the expected entity types
	var schema map[string]any
	err = json.Unmarshal(generatedSchema, &schema)
	s.Require().NoError(err, "Failed to unmarshal generated schema")

	defs := schema["definitions"].(map[string]any)
	entDef := defs["entity"].(map[string]any)
	props := entDef["properties"].(map[string]any)
	tprop := props["type"].(map[string]any)
	enum := tprop["enum"].([]any)

	// Check that both regular SQL and SAP entity types are included
	s.Contains(enum, "TABLE", "Schema should include TABLE entity type")
	s.Contains(enum, "VIEW", "Schema should include VIEW entity type")
	s.Contains(enum, "TRANSPARENT TABLE", "Schema should include TRANSPARENT TABLE entity type")
	s.Contains(enum, "POOLED TABLE", "Schema should include POOLED TABLE entity type")
	s.Contains(enum, "INTTAB", "Schema should include INTTAB entity type")
}
