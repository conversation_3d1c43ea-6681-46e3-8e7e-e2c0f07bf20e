{"$schema": "http://json-schema.org/draft-07/schema#", "type": "object", "properties": {"meta_schema_version": {"type": "string"}, "namespace": {"type": "string"}, "current_version": {"type": "string"}, "entities": {"type": "array", "items": {"$ref": "#/definitions/entity"}}, "relationships": {"type": "array", "items": {"$ref": "#/definitions/relationship"}}}, "required": ["meta_schema_version", "namespace", "entities", "relationships"], "definitions": {"entity": {"type": "object", "properties": {"$ref": {"type": "string"}, "model_name": {"type": "string"}, "base_type": {"type": "string"}, "name": {"type": "string"}, "type": {"type": "string"}, "description": {"type": "string"}, "active": {"type": "boolean"}, "ordinal_position": {"type": "integer"}, "properties": {"type": "object"}, "custom_properties": {"type": "object"}, "tags": {"type": "array", "items": {"type": "string"}}, "entity_elements": {"type": "array", "items": {"$ref": "#/definitions/entity_element"}}}, "required": ["model_name", "name", "type", "entity_elements"]}, "entity_element": {"type": "object", "properties": {"$ref": {"type": "string"}, "name": {"type": "string"}, "type": {"type": "string"}, "description": {"type": "string"}, "active": {"type": "boolean"}, "ordinal_position": {"type": "integer"}, "data_type": {"type": "string"}, "default_value": {"type": "string"}, "enum": {"type": "array", "items": {"type": "string"}}, "precision": {"type": "integer"}, "scale": {"type": "integer"}, "size": {"type": "integer"}, "is_required": {"type": "boolean"}, "is_array": {"type": "boolean"}, "pattern": {"type": "string"}, "properties": {"type": "object"}, "custom_properties": {"type": "object"}, "tags": {"type": "array", "items": {"type": "string"}}}, "required": ["name", "type"]}, "relationship": {"type": "object", "properties": {"name": {"type": "string"}, "type": {"type": "string"}, "description": {"type": "string"}, "active": {"type": "boolean"}, "ordinal_position": {"type": "integer"}, "properties": {"type": "object"}, "custom_properties": {"type": "object"}, "tags": {"type": "array", "items": {"type": "string"}}, "source": {"type": "object", "properties": {"$ref": {"type": "string"}}}, "target": {"type": "object", "properties": {"$ref": {"type": "string"}}}}, "required": ["name", "type", "source", "target"]}}}