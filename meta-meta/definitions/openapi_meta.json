{"model_info": {"type": "openapi", "specification_version": "3.1", "meta_schema_version": "openapi-20250201"}, "entity_model_name_format": ["name"], "entity_types": [{"name": "API_ENDPOINT", "description": "Represents a single API operation", "properties": {"path": {"type": "string"}, "method": {"type": "string"}}, "required": ["path", "method"]}, {"name": "SCHEMAS", "description": "Represents the definition of a reusable resource"}, {"name": "ENUM_TYPE", "description": "Represents the definition of an enumeration"}, {"name": "SECURITY_SCHEMES", "description": "Represents the definition of a reusable resource", "properties": {"type": {"type": "string"}, "flow": {"type": "string"}, "authorization_url": {"type": "string"}, "scopes": {"type": "array", "items": {"types": "string"}}}}], "element_types": [{"name": "HEADER", "description": "Represent a field"}, {"name": "PARAMETER", "description": "Represent a field", "properties": {"in": {"type": "string", "enum": ["path", "query"]}, "format": "int64"}}, {"name": "REQUEST", "description": "Represents an API request body.", "properties": {"content_type": {"type": "string"}}}, {"name": "RESPONSE", "description": "Represents an API response.", "properties": {"http_code": {"type": "integer"}}}, {"name": "SECURITY", "description": "Represents the security appliable to the API endpoint.", "properties": {"scopes": {"type": "array", "items": {"type": "string"}}}}, {"name": "PROPERTY", "description": "Represent a field"}, {"name": "MEMBER", "description": "Represent an enum member value"}], "relationship_types": [], "child_types": {"API_ENDPOINT": ["HEADER", "PARAMETER", "REQUEST", "RESPONSE", "SECURITY"], "SCHEMAS": ["PROPERTY"], "ENUM_TYPE": ["MEMBER"]}, "navigation": {"root": ["API_ENDPOINT", "SCHEMAS", "ENUM_TYPE", "SECURITY_SCHEMES"]}}