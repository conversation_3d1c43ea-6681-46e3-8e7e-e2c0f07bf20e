{"model_info": {"type": "odata", "specification_version": "4.0", "meta_schema_version": "odata-20250201"}, "entity_model_name_format": ["name"], "entity_types": [{"name": "ENUM_TYPE", "description": "Represents the definition of an enumeration"}, {"name": "COMPLEX_TYPE", "description": "Represents the definition of a reusable complex object like Address", "properties": {"base_type": {"type": "string"}, "open_type": {"type": "boolean"}}}, {"name": "ENTITY_TYPE", "description": "Represents the definition of a resource", "properties": {"base_type": {"type": "string"}, "open_type": {"type": "boolean"}, "has_stream": {"type": "boolean"}}}, {"name": "FUNCTION", "description": "Represents the definition of a function", "properties": {"entity_set_path": {"type": "string"}, "is_bound": {"type": "boolean"}, "is_composable": {"type": "boolean"}}}, {"name": "ACTION", "description": "Represents the definition of a action", "properties": {"entity_set_path": {"type": "string"}, "is_bound": {"type": "boolean"}, "is_composable": {"type": "boolean"}}}, {"name": "ENTITY_SET", "description": "Represents the entity endpoint", "properties": {"odata_entity_type": {"type": "string"}}}, {"name": "SINGLETON", "description": "Represents the service level single endpoint access", "properties": {"odata_entity_type": {"type": "string"}}}, {"name": "FUNCTION_IMPORT", "description": "Represents the service level function endpoint", "properties": {"function": {"type": "string"}, "entity_set": {"type": "string"}, "include_in_service_doc": {"type": "boolean"}}}, {"name": "ACTION_IMPORT", "description": "Represents the service level action endpoint", "properties": {"function": {"type": "string"}, "entity_set": {"type": "string"}, "include_in_service_doc": {"type": "boolean"}}}], "element_types": [{"name": "MEMBER", "description": "Represent an enum member value", "properties": {"value": {"type": "integer"}}}, {"name": "KEY", "description": "Represent an entity key"}, {"name": "PROPERTY", "description": "Represent a field", "properties": {"srid": {"type": "integer"}}}, {"name": "NAVIGATION_PROPERTY", "description": "Represent a field that can navigate to another entity", "properties": {"contains_target": {"type": "boolean"}}}, {"name": "PARAMETER", "description": "Represent a function or action parameter"}, {"name": "RETURN_TYPE", "description": "Represent a function or action return type"}, {"name": "NAVIGATION_PROPERTY_BINDING", "description": "Represents the navigation binding for entity set.", "properties": {"path": {"type": "string"}, "target": {"type": "string"}}}], "relationship_types": [{"name": "KEY_REF", "source_type": "PROPERTY", "target_type": "PROPERTY"}], "child_types": {"ENUM_TYPE": ["MEMBER"], "COMPLEX_TYPE": ["PROPERTY"], "ENTITY_TYPE": ["KEY", "PROPERTY", "NAVIGATION_PROPERTY"], "FUNCTION": ["PARAMETER", "RETURN_TYPE"], "ACTION": ["PARAMETER", "RETURN_TYPE"], "ENTITY_SET": ["NAVIGATION_PROPERTY_BINDING"], "SINGLETON": ["NAVIGATION_PROPERTY_BINDING"]}, "navigation": {"root": ["ENTITY_SET", "SINGLETON", "FUNCTION_IMPORT", "ACTION_IMPORT"], "hierarchies": [{"type": "PRIMARY-KEY", "navigable_relations": [{"name": "reference_elements", "types": ["KEY_REF"], "description": "A key references property"}]}]}}