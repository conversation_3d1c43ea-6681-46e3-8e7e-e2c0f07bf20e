{"model_info": {"type": "business_catalog", "meta_schema_version": "bc-20250201"}, "property_groups": [{"name": "Dataset.DataClassification", "properties": {"tier": {"type": "string", "enum": ["Tier 1", "Tier 2", "Tier 3"]}, "complexity": {"type": "string"}, "security_classification": {"type": "string"}, "classification_rationale": {"type": "string"}, "geographic_scope": {"type": "string"}}}, {"name": "DataElement.DataClassification", "properties": {"usage_level": {"type": "string"}, "data_owner": {"type": "string"}, "security_classification": {"type": "string"}, "classification_rationale": {"type": "string"}}}], "entity_model_name_format": ["name"], "entity_types": [{"name": "USER_INTERFACE", "description": "Represents UI dataset defintion", "properties": {"stakeholders": {"type": "array", "items": {"type": "string"}}, "purpose": {"type": "string"}, "ricefw": {"type": "string"}, "record_type_table": {"type": "string"}, "record_type_field": {"type": "string"}, "data_retention_period": {"type": "integer"}}, "property_groups": ["Dataset.DataClassification"], "required": [], "filter": [], "sort": []}, {"name": "QUERY", "description": "Represents query or view dataset defintion", "properties": {"stakeholders": {"type": "array", "items": {"type": "string"}}, "purpose": {"type": "string"}, "ricefw": {"type": "string"}, "record_type_table": {"type": "string"}, "record_type_field": {"type": "string"}, "data_retention_period": {"type": "integer"}}, "property_groups": ["Dataset.DataClassification"], "required": [], "filter": [], "sort": []}, {"name": "REPORT", "description": "Represents report type of dataset defintion", "properties": {"stakeholders": {"type": "array", "items": {"type": "string"}}, "purpose": {"type": "string"}, "ricefw": {"type": "string"}, "record_type_table": {"type": "string"}, "record_type_field": {"type": "string"}, "data_retention_period": {"type": "integer"}}, "property_groups": ["Dataset.DataClassification"], "required": [], "filter": [], "sort": []}, {"name": "RECORD_TYPE", "description": "Represents record type of defintion", "properties": {"stakeholders": {"type": "array", "items": {"type": "string"}}, "record_type_code": {"type": "string"}}, "required": [], "filter": [], "sort": []}], "element_types": [{"name": "PROPERTY", "description": "Represents a field within a business dataset definition", "properties": {"stakeholders": {"type": "array", "items": {"type": "string"}}, "field_status": {"type": "string"}, "is_auto_generated": {"type": "string"}, "business_impact": {"type": "string"}, "impact_details": {"type": "string"}, "governance_required_flag ": {"type": "string"}}, "property_groups": ["Dataset.DataClassification"]}, {"name": "RECORD_ELEMENT", "description": "Represents an element within a record type"}], "relationship_types": [], "child_types": {"USER_INTERFACE": ["PROPERTY"], "QUERY": ["PROPERTY"], "REPORT": ["PROPERTY"], "RECORD_TYPE": ["RECORD_ELEMENT"]}, "navigation": {"root": ["USER_INTERFACE", "QUERY", "REPORT", "RECORD_TYPE"]}}