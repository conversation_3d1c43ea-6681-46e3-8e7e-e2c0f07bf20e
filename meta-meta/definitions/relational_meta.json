{"model_info": {"type": "relational_model", "meta_schema_version": "relational-20250501", "supported_versions": ["relational-20250501", "sap-20250301"]}, "entity_model_name_format": ["schema", "name"], "entity_types": [{"name": "TABLE", "description": "A relational database table", "properties": {"schema": {"type": "string"}}, "required": ["schema"], "filter": ["schema"], "sort": ["schema"]}, {"name": "VIEW", "description": "A relational database view", "properties": {"schema": {"type": "string"}}, "required": ["schema"], "filter": ["schema"], "sort": ["schema"]}, {"name": "TRANSPARENT TABLE", "description": "A type of SAP table that has its own data records", "properties": {"contflag": {"type": "string", "description": "Content flag for SAP tables"}, "schema": {"type": "string"}}, "required": ["contflag"], "filter": ["contflag"], "sort": ["name"]}, {"name": "POOLED TABLE", "description": "A type of SAP table that shares data with other tables in a table pool", "properties": {"contflag": {"type": "string", "description": "Content flag for SAP tables"}, "schema": {"type": "string"}}, "required": ["contflag"], "filter": ["contflag"], "sort": ["name"]}, {"name": "INTTAB", "description": "A type of SAP internal table", "properties": {"schema": {"type": "string"}}, "required": [], "filter": [], "sort": ["name"]}, {"name": "CHECK-TABLE", "description": "A type of SAP check table", "properties": {}, "required": [], "filter": [], "sort": ["name"]}], "element_types": [{"name": "COLUMN", "description": "A column in a table", "properties": {"checktable": {"type": "string", "description": "Reference to a check table"}, "rolltable": {"type": "string"}, "rollname": {"type": "string", "description": "SAP data element name"}, "domname": {"type": "string", "description": "Domain name or SAP domain name"}, "entitytab": {"type": "string", "description": "Entity table name"}, "languflag": {"type": "string", "description": "Language flag"}}}, {"name": "PRIMARY-KEY", "description": "The primary key in a table. Can be composite."}, {"name": "INDEX", "description": "An index in a table", "properties": {"is_clustered": {"type": "boolean"}, "is_unique": {"type": "boolean"}}}, {"name": "FOREIGN-KEY", "description": "A foreign key in a table. Can be composite.", "properties": {"validate": {"type": "boolean"}, "on_update": {"type": "string"}, "on_delete": {"type": "string"}}}, {"name": "CHECK-TABLE", "description": "A check table.", "properties": {}}], "relationship_types": [{"name": "PK_REF", "source_type": "PRIMARY-KEY", "target_type": "COLUMN"}, {"name": "IDX_REF", "source_type": "INDEX", "target_type": "COLUMN", "properties": {"is_ascending": {"type": "boolean"}, "is_included_column": {"type": "boolean"}}}, {"name": "FK_REF_SOURCE", "source_type": "FOREIGN-KEY", "target_type": "COLUMN", "description": "Source column of a foreign key relationship"}, {"name": "FK_REF_TARGET", "source_type": "FOREIGN-KEY", "target_type": "COLUMN", "description": "Target column of a foreign key relationship"}, {"name": "CT_REF_SOURCE", "source_type": "CHECK-TABLE", "target_type": "COLUMN", "description": "Source column of a check table relationship"}, {"name": "CT_REF_TARGET", "source_type": "CHECK-TABLE", "target_type": "COLUMN", "description": "Target column of a check table relationship"}, {"name": "TEXT_COLUMN", "source_type": "CHECK-TABLE", "target_type": "COLUMN", "description": "Target column of a check table relationship"}, {"name": "LANGUAGE_COLUMN", "source_type": "CHECK-TABLE", "target_type": "COLUMN", "description": "Target column of a check table relationship"}, {"name": "CONSTRAINED_ON_SOURCE", "source_type": "CHECK-TABLE", "target_type": "COLUMN", "description": "Target column of a check table relationship"}, {"name": "CONSTRAINED_ON_TARGET", "source_type": "CHECK-TABLE", "target_type": "COLUMN", "description": "Target column of a check table relationship"}], "child_types": {"TABLE": ["COLUMN", "PRIMARY-KEY", "INDEX", "FOREIGN-KEY"], "VIEW": ["COLUMN", "PRIMARY-KEY", "INDEX"], "TRANSPARENT TABLE": ["COLUMN", "PRIMARY-KEY", "CHECK-TABLE"], "POOLED TABLE": ["COLUMN", "PRIMARY-KEY"], "INTTAB": ["COLUMN", "PRIMARY-KEY"]}, "navigation": {"root": ["TABLE", "VIEW", "TRANSPARENT TABLE", "POOLED TABLE", "INTTAB", "CHECK-TABLE"], "hierarchies": [{"type": "TABLE", "navigable_elements": [{"name": "columns", "types": ["COLUMN"], "description": "A table has columns"}, {"name": "constraints", "types": ["PRIMARY-KEY", "INDEX", "FOREIGN-KEY", "CHECK-TABLE"], "description": "A table has constraints"}], "navigable_relations": [{"name": "relations", "types": ["FOREIGN-KEY"], "description": "A table has relations"}]}, {"type": "VIEW", "navigable_elements": [{"name": "columns", "types": ["COLUMN"], "description": "A view has columns"}]}, {"type": "TRANSPARENT TABLE", "navigable_elements": [{"name": "columns", "types": ["COLUMN"], "description": "A table has columns"}, {"name": "constraints", "types": ["PRIMARY-KEY"], "description": "A table has constraints"}]}, {"type": "POOLED TABLE", "navigable_elements": [{"name": "columns", "types": ["COLUMN"], "description": "A table has columns"}, {"name": "constraints", "types": ["PRIMARY-KEY"], "description": "A table has constraints"}]}, {"type": "INTTAB", "navigable_elements": [{"name": "columns", "types": ["COLUMN"], "description": "A table has columns"}, {"name": "constraints", "types": ["PRIMARY-KEY"], "description": "A table has constraints"}]}, {"type": "PRIMARY-KEY", "navigable_relations": [{"name": "reference_elements", "types": ["PK_REF"], "description": "A primary key references columns"}]}, {"type": "INDEX", "navigable_relations": [{"name": "reference_elements", "types": ["IDX_REF"], "description": "An index references columns"}]}, {"type": "FOREIGN-KEY", "navigable_relations": [{"name": "reference_elements", "types": ["FK_REF_SOURCE"], "description": "A foreign key references source columns"}, {"name": "reference_elements", "types": ["FK_REF_TARGET"], "description": "A foreign key references target columns"}]}, {"type": "CHECK-TABLE", "navigable_relations": [{"name": "reference_elements", "types": ["CT_REF_TARGET"], "description": "A check table references target column"}, {"name": "reference_elements", "types": ["CT_REF_SOURCE"], "description": "A check table references source column"}, {"name": "reference_elements", "types": ["TEXT_COLUMN"], "description": "A check table references text column"}, {"name": "reference_elements", "types": ["LANGUAGE_COLUMN"], "description": "A check table references language column"}, {"name": "reference_elements", "types": ["CONSTRAINED_ON_SOURCE"], "description": "A check table references  constrained on target column"}, {"name": "reference_elements", "types": ["CONSTRAINED_ON_TARGET"], "description": "A check table references constrained on source column"}]}]}}