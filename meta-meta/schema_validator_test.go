package metameta

import (
	"encoding/json"
	"errors"
	"fmt"
	"os"
	"path/filepath"
	"testing"

	"github.com/BackOfficeAssoc/catalog/models"
	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"
	"github.com/xeipuuv/gojsonschema"
)

const (
	// Path to the JSON schema file for relational models
	relationalModelSchemaPath = "json_schema/relational_schema.json"
)

func TestValidateAgainstSchema(t *testing.T) {
	// Create a custom validator function that uses relative paths
	validateWithRelativePaths := func(datamodel models.DataModel) error {
		// Read the schema file
		schemaBytes, err := os.ReadFile(relationalModelSchemaPath)
		if err != nil {
			return err
		}

		// Convert the data model to JSON
		dataBytes, err := json.Marshal(datamodel)
		if err != nil {
			return err
		}

		// Create schema loader
		schemaLoader := gojsonschema.NewStringLoader(string(schemaBytes))
		documentLoader := gojsonschema.NewStringLoader(string(dataBytes))

		// Validate
		result, err := gojsonschema.Validate(schemaLoader, documentLoader)
		if err != nil {
			return err
		}

		if !result.Valid() {
			var errMsg string
			for _, desc := range result.Errors() {
				errMsg += fmt.Sprintf("- %s\n", desc)
			}
			return errors.New(errMsg)
		}

		return nil
	}

	t.Run("validates relational schema", func(t *testing.T) {
		// Load a standard SQL database schema for testing
		dataBytes, err := os.ReadFile(filepath.Join("testdata", "data_model_database.json"))
		require.NoError(t, err, "Failed to load test data")

		var dataModel models.DataModel
		err = json.Unmarshal(dataBytes, &dataModel)
		require.NoError(t, err, "Failed to unmarshal test data")

		// Validate
		err = validateWithRelativePaths(dataModel)
		assert.NoError(t, err, "Validation should pass for valid SQL schema")
	})

	t.Run("validates SAP schema", func(t *testing.T) {
		// Load test data
		dataBytes, err := os.ReadFile(filepath.Join("testdata", "test_sap_schema.json"))
		require.NoError(t, err, "Failed to load test data")

		var dataModel models.DataModel
		err = json.Unmarshal(dataBytes, &dataModel)
		require.NoError(t, err, "Failed to unmarshal test data")

		// Validate
		err = validateWithRelativePaths(dataModel)
		assert.NoError(t, err, "Validation should pass for valid SAP schema")
	})

	t.Run("fails for invalid SAP schema", func(t *testing.T) {
		// Create an invalid SAP schema (missing required contflag property)
		dataModel := models.DataModel{
			MetaSchemaVersion: "sap-20250301",
			Namespace:         "sap",
			CurrentVersion:    "20250301-002",
			Entities: []models.Entity{
				{
					ModelName:   "INVALID_TABLE",
					Name:        "INVALID_TABLE",
					Type:        "TRANSPARENT TABLE",
					Description: "Invalid Table",
					Properties:  map[string]interface{}{}, // Missing contflag
					EntityElements: []models.EntityElement{
						{
							Name:        "FIELD1",
							Type:        "COLUMN",
							Description: "Field 1",
							DataType:    "CHAR",
						},
					},
				},
			},
			Relationships: []models.Relationship{},
		}

		// Validate
		err := validateWithRelativePaths(dataModel)
		assert.Error(t, err, "Validation should fail for invalid SAP schema")
	})
}
