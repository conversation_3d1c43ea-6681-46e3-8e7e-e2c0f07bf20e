{"meta_schema_version": "relational-20250501", "namespace": "ns1", "current_version": "20250201-001", "entities": [{"model_name": "ns1.production.product", "name": "Product", "type": "TABLE", "description": "", "active": true, "properties": {"schema": "Production"}, "custom_properties": {}, "tags": [], "entity_elements": [{"model_name": "ns1.production.product.productid", "name": "ProductID", "type": "COLUMN", "description": "", "active": true, "ordinal_position": 1, "data_type": "int", "default_value": "", "precision": 10, "scale": 0, "is_required": false, "is_array": false, "tags": [], "properties": {}, "custom_properties": {}}, {"model_name": "ns1.production.product.name", "name": "Name", "type": "COLUMN", "ordinal_position": 2, "data_type": "Name", "default_value": "", "max_length": 50, "nulls_allowed": false, "properties": {}, "custom_properties": {}}, {"model_name": "ns1.production.product.model", "name": "Model", "type": "COLUMN", "description": "Stores the model GUID that is a foreign key to another table", "ordinal_position": 3, "data_type": "n<PERSON><PERSON><PERSON>", "default_value": "", "max_length": 24, "nulls_allowed": false, "properties": {}, "custom_properties": {}}, {"model_name": "ns1.production.product.description", "name": "Description", "type": "COLUMN", "ordinal_position": 4, "data_type": "n<PERSON><PERSON><PERSON>", "default_value": "", "max_length": 400, "nulls_allowed": false, "properties": {}, "custom_properties": {}}, {"model_name": "ns1.production.product.makeflag", "name": "Makeflag", "type": "COLUMN", "ordinal_position": 5, "data_type": "Flag", "default_value": "", "precision": 1, "scale": 0, "nulls_allowed": false, "properties": {}, "custom_properties": {}}, {"model_name": "ns1.production.product.pk.productid", "name": "PK_ProductID", "type": "PRIMARY-KEY", "properties": {}}, {"model_name": "ns1.production.product.idx.name.makeflag", "name": "IDX_Name_MakeFlag", "type": "INDEX", "properties": {"is_unique": true}}, {"model_name": "ns1.production.product.fk.model", "name": "FK_Model", "type": "FOREIGN-KEY", "$mapped": "#element/ns1.production.product.model", "properties": {"on_update": "no_action", "on_delete": "no_action"}}]}, {"model_name": "ns1.production.productmodel", "name": "ProductModel", "type": "TABLE", "description": "", "active": true, "properties": {"schema": "Production"}, "custom_properties": {}, "tags": [], "entity_elements": [{"model_name": "ns1.production.productmodel.modelid", "name": "ModelID", "type": "COLUMN", "description": "", "active": true, "ordinal_position": 1, "data_type": "int", "default_value": "", "precision": 10, "scale": 0, "is_required": false, "is_array": false, "tags": [], "properties": {}, "custom_properties": {}}, {"model_name": "ns1.production.productmodel.modelname", "name": "Name", "type": "COLUMN", "ordinal_position": 2, "data_type": "Name", "default_value": "", "max_length": 50, "nulls_allowed": false, "properties": {}, "custom_properties": {}}]}, {"model_name": "ns1.production.productview", "name": "ProductView", "type": "VIEW", "properties": {"schema": "Production"}, "tags": [], "entity_elements": [{"model_name": "ns1.production.productview.productid", "name": "ProductID", "type": "COLUMN", "ordinal_position": 1, "data_type": "int", "default_value": "", "precision": 10, "scale": 0, "nulls_allowed": false, "properties": {}}, {"model_name": "ns1.production.productview.name", "name": "Name", "type": "COLUMN", "ordinal_position": 2, "data_type": "Name", "default_value": "", "max_length": 50, "nulls_allowed": false, "properties": {}}]}], "relationships": [{"model_name": "ns1.pk_production_product_productid", "name": "PK_Production_Product_ProductID", "type": "PK_REF", "description": "", "source": {"$ref": "#element/ns1.production.product.pk"}, "target": {"$ref": "#element/ns.production.product.productid"}}, {"model_name": "ns1.idx_production_product_name_makeflag_name", "name": "IDX_Production_Product_Name_MakeFlag_Name", "type": "IDX_REF", "description": "", "ordinal_position": 1, "source": {"$ref": "#element/ns1.production.product.idx.name.makeflag"}, "target": {"$ref": "#element/ns.production.product.name"}}, {"model_name": "ns1.idx_production_product_name_makeflag_makeflag", "name": "IDX_Production_Product_Name_MakeFlag_MakeFlag", "type": "IDX_REF", "description": "", "ordinal_position": 1, "source": {"$ref": "#element/ns1.production.product.idx.name.makeflag"}, "target": {"$ref": "#element/ns.production.product.makeflag"}}, {"model_name": "ns1.fk_production_product_model", "name": "FK_Production_Product_Model", "type": "FK_REF_SOURCE", "description": "", "active": true, "source": {"$ref": "#element/ns1.production.product.fk.model"}, "target": {"$ref": "#element/ns1.production.product.model"}}, {"model_name": "ns1.fk_production_product_model_modelid", "name": "FK_Production_Product_Model_ModelID", "type": "FK_REF_TARGET", "description": "", "source": {"$ref": "#element/ns1.production.product.fk.model"}, "target": {"$ref": "#element/ns1.production.productmodel.modelid"}}]}