{"$schema": "http://json-schema.org/draft-07/schema#", "definitions": {"columnElementProperties": {"additionalProperties": false, "properties": {"checktable": {"description": "Reference to a check table", "type": "string"}, "domname": {"description": "Domain name or SAP domain name", "type": "string"}, "entitytab": {"description": "Entity table name", "type": "string"}, "languflag": {"description": "Language flag", "type": "string"}, "rollname": {"description": "SAP data element name", "type": "string"}, "rolltable": {"type": "string"}}, "type": "object"}, "entity": {"allOf": [{"if": {"properties": {"type": {"const": "TABLE"}}}, "then": {"properties": {"properties": {"$ref": "#/definitions/tableEntityProperties"}}}}, {"if": {"properties": {"type": {"const": "VIEW"}}}, "then": {"properties": {"properties": {"$ref": "#/definitions/viewEntityProperties"}}}}, {"if": {"properties": {"type": {"const": "TRANSPARENT TABLE"}}}, "then": {"properties": {"properties": {"$ref": "#/definitions/transparenttableEntityProperties"}}}}, {"if": {"properties": {"type": {"const": "POOLED TABLE"}}}, "then": {"properties": {"properties": {"$ref": "#/definitions/pooledtableEntityProperties"}}}}, {"if": {"properties": {"type": {"const": "INTTAB"}}}, "then": {"properties": {"properties": {"$ref": "#/definitions/inttabEntityProperties"}}}}], "properties": {"$ref": {"type": "string"}, "active": {"type": "boolean"}, "base_type": {"type": "string"}, "custom_properties": {"type": "object"}, "description": {"type": "string"}, "entity_elements": {"items": {"$ref": "#/definitions/entity_element"}, "type": "array"}, "model_name": {"type": "string"}, "name": {"type": "string"}, "ordinal_position": {"type": "integer"}, "properties": {"type": "object"}, "tags": {"items": {"type": "string"}, "type": "array"}, "type": {"enum": ["TABLE", "VIEW", "TRANSPARENT TABLE", "POOLED TABLE", "INTTAB", "CHECK-TABLE"], "type": "string"}}, "required": ["model_name", "name", "type", "entity_elements"], "type": "object"}, "entity_element": {"allOf": [{"if": {"properties": {"type": {"const": "COLUMN"}}}, "then": {"properties": {"properties": {"$ref": "#/definitions/columnElementProperties"}}}}, {"if": {"properties": {"type": {"const": "INDEX"}}}, "then": {"properties": {"properties": {"$ref": "#/definitions/indexElementProperties"}}}}, {"if": {"properties": {"type": {"const": "FOREIGN-KEY"}}}, "then": {"properties": {"properties": {"$ref": "#/definitions/foreignkeyElementProperties"}}}}], "properties": {"$ref": {"type": "string"}, "active": {"type": "boolean"}, "custom_properties": {"type": "object"}, "data_type": {"type": "string"}, "default_value": {"type": "string"}, "description": {"type": "string"}, "enum": {"items": {"type": "string"}, "type": "array"}, "is_array": {"type": "boolean"}, "is_required": {"type": "boolean"}, "name": {"type": "string"}, "ordinal_position": {"type": "integer"}, "pattern": {"type": "string"}, "precision": {"type": "integer"}, "properties": {"type": "object"}, "scale": {"type": "integer"}, "size": {"type": "integer"}, "tags": {"items": {"type": "string"}, "type": "array"}, "type": {"enum": ["COLUMN", "PRIMARY-KEY", "INDEX", "FOREIGN-KEY", "CHECK-TABLE"], "type": "string"}}, "required": ["name", "type"], "type": "object"}, "foreignkeyElementProperties": {"additionalProperties": false, "properties": {"on_delete": {"type": "string"}, "on_update": {"type": "string"}, "validate": {"type": "boolean"}}, "type": "object"}, "idx_refRelationshipProperties": {"additionalProperties": false, "properties": {"is_ascending": {"type": "boolean"}, "is_included_column": {"type": "boolean"}}, "type": "object"}, "indexElementProperties": {"additionalProperties": false, "properties": {"is_clustered": {"type": "boolean"}, "is_unique": {"type": "boolean"}}, "type": "object"}, "inttabEntityProperties": {"additionalProperties": false, "properties": {"schema": {"type": "string"}}, "type": "object"}, "pooledtableEntityProperties": {"additionalProperties": false, "properties": {"contflag": {"description": "Content flag for SAP tables", "type": "string"}, "schema": {"type": "string"}}, "required": ["contflag"], "type": "object"}, "relationship": {"allOf": [{"if": {"properties": {"type": {"const": "IDX_REF"}}}, "then": {"properties": {"properties": {"$ref": "#/definitions/idx_refRelationshipProperties"}}}}], "properties": {"active": {"type": "boolean"}, "custom_properties": {"type": "object"}, "description": {"type": "string"}, "name": {"type": "string"}, "ordinal_position": {"type": "integer"}, "properties": {"type": "object"}, "source": {"properties": {"$ref": {"type": "string"}}, "type": "object"}, "tags": {"items": {"type": "string"}, "type": "array"}, "target": {"properties": {"$ref": {"type": "string"}}, "type": "object"}, "type": {"enum": ["PK_REF", "IDX_REF", "FK_REF_SOURCE", "FK_REF_TARGET", "CT_REF_SOURCE", "CT_REF_TARGET", "TEXT_COLUMN", "LANGUAGE_COLUMN", "CONSTRAINED_ON_SOURCE", "CONSTRAINED_ON_TARGET"], "type": "string"}}, "required": ["name", "type", "source", "target"], "type": "object"}, "tableEntityProperties": {"additionalProperties": false, "properties": {"schema": {"type": "string"}}, "required": ["schema"], "type": "object"}, "transparenttableEntityProperties": {"additionalProperties": false, "properties": {"contflag": {"description": "Content flag for SAP tables", "type": "string"}, "schema": {"type": "string"}}, "required": ["contflag"], "type": "object"}, "viewEntityProperties": {"additionalProperties": false, "properties": {"schema": {"type": "string"}}, "required": ["schema"], "type": "object"}}, "properties": {"current_version": {"type": "string"}, "entities": {"items": {"$ref": "#/definitions/entity"}, "type": "array"}, "meta_schema_version": {"type": "string"}, "namespace": {"type": "string"}, "relationships": {"items": {"$ref": "#/definitions/relationship"}, "type": "array"}}, "required": ["meta_schema_version", "namespace", "entities", "relationships"], "type": "object"}