{"meta_schema_version": "sap-20250301", "namespace": "sap", "current_version": "20250301-001", "tenant_id": "tnt_111", "entities": [{"model_name": "T006", "name": "T006", "type": "TRANSPARENT TABLE", "description": "Units of Measurement", "properties": {"contflag": "C"}, "tags": [], "entity_elements": [{"name": "MANDT", "type": "COLUMN", "description": "Client", "data_type": "CLNT", "size": 3, "is_required": true, "properties": {"checktable": "T000", "rollname": "MANDT", "domname": "MANDT", "entitytab": "T000"}}, {"name": "MSEHI", "type": "COLUMN", "description": "Unit of Measurement", "data_type": "UNIT", "size": 3, "is_required": true, "properties": {"rollname": "MSEHI", "domname": "MEINS", "entitytab": "T006"}}, {"name": "PK_T006", "type": "PRIMARY-KEY", "description": "Primary Key for T006", "properties": {}}]}, {"model_name": "AT40", "name": "AT40", "type": "POOLED TABLE", "description": "Calculation Categories of Cash Flow Calculator", "properties": {"contflag": "S"}, "tags": [], "entity_elements": [{"name": "SBERFIMA", "type": "COLUMN", "description": "Calculation Category for Cash Flow Calculator", "data_type": "CHAR", "size": 4, "is_required": true, "properties": {"rollname": "SBEWFIMA", "domname": "SBEWFIMA", "entitytab": "AT40"}}, {"name": "SSORTFIMA", "type": "COLUMN", "description": "Sort indicator for financial mathematic calculations", "data_type": "NUMC", "size": 2, "properties": {"rollname": "TFM_SORT", "domname": "NUMC2"}}, {"name": "PK_AT40", "type": "PRIMARY-KEY", "description": "Primary Key for AT40", "properties": {}}]}], "relationships": [{"name": "PK_T006_MANDT", "type": "PK_REF", "source": {"$ref": "T006.PK_T006"}, "target": {"$ref": "T006.MANDT"}}, {"name": "PK_T006_MSEHI", "type": "PK_REF", "source": {"$ref": "T006.PK_T006"}, "target": {"$ref": "T006.MSEHI"}}, {"name": "PK_AT40_SBERFIMA", "type": "PK_REF", "source": {"$ref": "AT40.PK_AT40"}, "target": {"$ref": "AT40.SBERFIMA"}}]}