{"meta_schema_version": "relational-20250501", "namespace": "ns1", "current_version": "20250201-001", "tenant_id": "tnt_1", "entities": [{"model_name": "ns1.production.product", "name": "Product", "type": "TABLE", "description": "Product table", "active": true, "tags": [], "properties": {"schema": "Production"}, "entity_elements": [{"model_name": "ns1.production.product.productid", "name": "ProductID", "type": "COLUMN", "data_type": "int", "precision": 10, "scale": 0, "is_required": true, "properties": {}}, {"model_name": "ns1.production.product.fk.categoryid", "name": "FK_CategoryID", "type": "FOREIGN-KEY", "description": "Foreign key to ProductCategory", "properties": {"on_update": "CASCADE", "on_delete": "NO ACTION", "validate": true}}]}, {"model_name": "ns1.production.productcategory", "name": "ProductCategory", "type": "TABLE", "description": "Product category table", "active": true, "tags": [], "properties": {"schema": "Production"}, "entity_elements": [{"model_name": "ns1.production.productcategory.categoryid", "name": "CategoryID", "type": "COLUMN", "data_type": "int", "precision": 10, "scale": 0, "is_required": true, "properties": {}}]}], "relationships": [{"model_name": "ns1.fk_product_category_source", "name": "FK_Product_Category_Source", "type": "FK_REF_SOURCE", "description": "Foreign key source reference", "source": {"$ref": "#element/ns1.production.product.fk.categoryid"}, "target": {"$ref": "#element/ns1.production.product.categoryid"}}, {"model_name": "ns1.fk_product_category_target", "name": "FK_Product_Category_Target", "type": "FK_REF_TARGET", "description": "Foreign key target reference", "source": {"$ref": "#element/ns1.production.product.fk.categoryid"}, "target": {"$ref": "#element/ns1.production.productcategory.categoryid"}}]}