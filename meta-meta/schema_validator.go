package metameta

import (
	"embed"
	"encoding/json"
	"errors"
	"fmt"
	"path/filepath"

	"github.com/BackOfficeAssoc/catalog/models"
	"github.com/xeipuuv/gojsonschema"
)

//go:embed json_schema
var jsonSchemaValidationFiles embed.FS

type ValidateAgainstSchemaFunc func(datamodel models.DataModel) error

// Function to validate the data model against the schema
func ValidateAgainstSchema(datamodel models.CreateDataModelRequest) error {
	schemaPath := filepath.Join("json_schema", "relational_schema.json")

	// #nosec G304
	schemaBytes, err := jsonSchemaValidationFiles.ReadFile(schemaPath)
	if err != nil {
		return fmt.Errorf("failed to read schema file %s: %w", schemaPath, err)
	}

	// Convert the data model to JSON
	dataBytes, err := json.Marshal(datamodel)
	if err != nil {
		return fmt.Errorf("failed to marshal data model: %w", err)
	}

	// Create schema loader
	schemaLoader := gojsonschema.NewStringLoader(string(schemaBytes))
	documentLoader := gojsonschema.NewStringLoader(string(dataBytes))

	// Validate
	result, err := gojsonschema.Validate(schemaLoader, documentLoader)
	if err != nil {
		return fmt.Errorf("validation error: %w", err)
	}

	if !result.Valid() {
		var errMsg string
		for _, desc := range result.Errors() {
			errMsg += fmt.Sprintf("- %s\n", desc)
		}
		return errors.New(errMsg)
	}

	return nil
}
