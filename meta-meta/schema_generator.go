package metameta

import (
	"encoding/json"
	"fmt"
	"strings"
)

// DataSourceModel representing a meta-meta definition

type DataSourceModel struct {
	ModelInfo             map[string]any      `json:"model_info"`
	EntityModelNameFormat []string            `json:"entity_model_name_format"`
	EntityTypes           []EntityType        `json:"entity_types"`
	ElementTypes          []ElementType       `json:"element_types"`
	RelationshipTypes     []RelationshipType  `json:"relationship_types,omitempty"`
	ChildTypes            map[string][]string `json:"child_types,omitempty"`
	Navigation            any                 `json:"navigation,omitempty"`
}

// EntityType defines a single entity type from the "entity_types" array
type EntityType struct {
	Name        string         `json:"name"`
	Description string         `json:"description"`
	Properties  map[string]any `json:"properties,omitempty"`
	Required    []string       `json:"required,omitempty"`
	Filter      []string       `json:"filter,omitempty"`
	Sort        []string       `json:"sort,omitempty"`
}

// ElementType defines a single element from the "element_types" array
type ElementType struct {
	Name        string         `json:"name"`
	Description string         `json:"description"`
	Properties  map[string]any `json:"properties,omitempty"`
	Required    []string       `json:"required,omitempty"`
}

// RelationshipType defines relationships between entities
type RelationshipType struct {
	Name        string         `json:"name"`
	SourceType  string         `json:"source_type"`
	TargetType  string         `json:"target_type"`
	Properties  map[string]any `json:"properties,omitempty"`
	Description string         `json:"description,omitempty"`
}

// function to build the data model schema in memory
func GenerateDataModelSchema(
	baseDatamodelSchema []byte,
	datasourceModel []byte,
) ([]byte, error) {

	var baseSchema map[string]any
	if err := json.Unmarshal(baseDatamodelSchema, &baseSchema); err != nil {
		return nil, fmt.Errorf("failed to unmarshal base schema: %w", err)
	}

	var ds DataSourceModel
	if err := json.Unmarshal(datasourceModel, &ds); err != nil {
		return nil, fmt.Errorf("failed to unmarshal datasource model: %w", err)
	}

	// Update the base schema to limit entity types to what's defined in our model
	if err := enhanceEntityDefinition(baseSchema, ds.EntityTypes); err != nil {
		return nil, fmt.Errorf("failed to enhance entity definition: %w", err)
	}

	// Do the same for element types
	if err := enhanceEntityElementDefinition(baseSchema, ds.ElementTypes); err != nil {
		return nil, fmt.Errorf("failed to enhance entity_element definition: %w", err)
	}

	// And finally for relationship types
	if err := enhanceRelationshipDefinition(baseSchema, ds.RelationshipTypes); err != nil {
		return nil, fmt.Errorf("failed to enhance relationship definition: %w", err)
	}

	// Setting up the final schema and updating the formatting
	updated, err := json.MarshalIndent(baseSchema, "", "  ")
	if err != nil {
		return nil, fmt.Errorf("failed to marshal updated schema: %w", err)
	}

	return updated, nil
}

// Function getAllOf that can be reused for entity, elements and relations as requested
func getAllOf(def map[string]any) []any {
	if existingAllOf, exists := def["allOf"].([]any); exists {
		return existingAllOf
	}
	return []any{}
}

// Function createIfThenBlock as requested
func createIfThenBlock(typeName, definitionRef string) map[string]any {
	return map[string]any{
		"if": map[string]any{
			"properties": map[string]any{
				"type": map[string]any{
					"const": typeName,
				},
			},
		},
		"then": map[string]any{
			"properties": map[string]any{
				"properties": map[string]any{
					"$ref": definitionRef,
				},
			},
		},
	}
}

// Creates property validation rules based on type definitions
func buildPropertiesSubschema(props map[string]any, required []string) map[string]any {
	subschema := map[string]any{
		"type":                 "object",
		"properties":           props,
		"additionalProperties": false,
	}
	if len(required) > 0 {
		subschema["required"] = required
	}
	return subschema
}

// Enhance entity definition
func enhanceEntityDefinition(schema map[string]any, entityTypes []EntityType) error {
	defs, ok := schema["definitions"].(map[string]any)
	if !ok {
		return fmt.Errorf("base schema missing 'definitions' key")
	}
	entDef, ok := defs["entity"].(map[string]any)
	if !ok {
		return fmt.Errorf("base schema missing 'definitions.entity' key")
	}
	props, ok := entDef["properties"].(map[string]any)
	if !ok {
		return fmt.Errorf("entity definition missing 'properties' key")
	}

	// Limit the 'type' field to only allow the entity names from the DS
	tprop, ok := props["type"].(map[string]any)
	if !ok {
		return fmt.Errorf("entity definition has no 'type' property schema")
	}

	var typeNames []any
	for _, et := range entityTypes {
		typeNames = append(typeNames, et.Name)
	}
	tprop["enum"] = typeNames

	// Insert an if/then for each entity type with special properties
	allOf := getAllOf(entDef)

	for _, et := range entityTypes {
		// If no special properties, skip
		if len(et.Properties) == 0 && len(et.Required) == 0 {
			continue
		}

		// Create a dedicated definition for these special props
		newDefName := makeEntityDefinitionName(et.Name)
		defs[newDefName] = buildPropertiesSubschema(et.Properties, et.Required)

		// Now create the if/then referencing that definition
		refPath := "#/definitions/" + newDefName
		block := createIfThenBlock(et.Name, refPath)
		allOf = append(allOf, block)
	}

	if len(allOf) > 0 {
		entDef["allOf"] = allOf
	}

	// Always required
	entDef["required"] = []string{"model_name", "name", "type", "entity_elements"}
	return nil
}

// Enhance entity_element definition
func enhanceEntityElementDefinition(schema map[string]any, elementTypes []ElementType) error {
	defs, ok := schema["definitions"].(map[string]any)
	if !ok {
		return fmt.Errorf("base schema missing 'definitions' key")
	}
	elemDef, ok := defs["entity_element"].(map[string]any)
	if !ok {
		return fmt.Errorf("base schema missing 'definitions.entity_element' key")
	}
	props, ok := elemDef["properties"].(map[string]any)
	if !ok {
		return fmt.Errorf("entity_element definition missing 'properties' key")
	}

	// Restrict 'type' field to only the element type names
	tprop, ok := props["type"].(map[string]any)
	if !ok {
		return fmt.Errorf("entity_element definition has no 'type' property schema")
	}

	var typeNames []any
	for _, et := range elementTypes {
		typeNames = append(typeNames, et.Name)
	}
	tprop["enum"] = typeNames

	// Insert if/then blocks
	allOf := getAllOf(elemDef)
	for _, et := range elementTypes {
		if len(et.Properties) == 0 && len(et.Required) == 0 {
			continue
		}
		newDefName := makeElementDefinitionName(et.Name)
		defs[newDefName] = buildPropertiesSubschema(et.Properties, et.Required)

		refPath := "#/definitions/" + newDefName
		block := createIfThenBlock(et.Name, refPath)
		allOf = append(allOf, block)
	}

	if len(allOf) > 0 {
		elemDef["allOf"] = allOf
	}

	// Basic required
	elemDef["required"] = []string{"name", "type"}
	return nil
}

// Enhance relationship definition
func enhanceRelationshipDefinition(schema map[string]any, relationshipTypes []RelationshipType) error {
	if len(relationshipTypes) == 0 {
		return nil
	}
	defs, ok := schema["definitions"].(map[string]any)
	if !ok {
		return fmt.Errorf("base schema missing 'definitions' key")
	}
	relDef, ok := defs["relationship"].(map[string]any)
	if !ok {
		return fmt.Errorf("base schema missing 'definitions.relationship' key")
	}
	relProps, ok := relDef["properties"].(map[string]any)
	if !ok {
		return fmt.Errorf("relationship definition missing 'properties' key")
	}

	// Restrict 'type' field
	tprop, ok := relProps["type"].(map[string]any)
	if !ok {
		return fmt.Errorf("relationship definition has no 'type' property schema")
	}

	var typeNames []any
	for _, rt := range relationshipTypes {
		typeNames = append(typeNames, rt.Name)
	}
	tprop["enum"] = typeNames

	// Insert if/then blocks
	allOf := getAllOf(relDef)
	for _, rt := range relationshipTypes {
		if len(rt.Properties) == 0 {
			continue
		}
		newDefName := makeRelationshipDefinitionName(rt.Name)
		defs[newDefName] = buildPropertiesSubschema(rt.Properties, nil)

		refPath := "#/definitions/" + newDefName
		block := createIfThenBlock(rt.Name, refPath)
		allOf = append(allOf, block)
	}

	if len(allOf) > 0 {
		relDef["allOf"] = allOf
	}

	return nil
}

func makeEntityDefinitionName(name string) string {
	return toSafeName(name) + "EntityProperties"
}
func makeElementDefinitionName(name string) string {
	return toSafeName(name) + "ElementProperties"
}
func makeRelationshipDefinitionName(name string) string {
	return toSafeName(name) + "RelationshipProperties"
}
func toSafeName(name string) string {
	tmp := strings.ReplaceAll(name, " ", "")
	tmp = strings.ReplaceAll(tmp, "-", "")
	return strings.ToLower(tmp)
}
