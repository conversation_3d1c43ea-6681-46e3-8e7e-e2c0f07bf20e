# Concepts

This section describes Dataset modeling concepts in the Syniti Knowledge Platform (SKP).

## Technical Dataset

- Database Table
- Database Views
- View
- Sub View
- Rule
  - Update
  - Insert
  - Delete
- Validation

### Dataset: Database Table

A Database Table is created by the scanner and represents a physical object in a database. It essentially captures the metadata of the physical table. Each table is marked as "Protected" (typically at the datastore level) or as a modifiable table (e.g., working tables, construct tables). Ideally, working and construct databases should only contain tables.

### Dataset: Database View

A Database View is created by the scanner when a database view exists in the database. The scanner imports and creates a "Database View" object. Over time, the focus is to transition from creating physical Database Views to logical View Datasets, which are easier to maintain.

To identify an updatable Database View, it is necessary to configure whether the view is updatable and specify the primary key data elements. Since not all views are updatable, this configuration may need to be performed manually after scanning a new view into the datastore.

### Dataset: View

View Datasets represent a virtual view of the data. Previously, physical views were created in working databases to achieve this. The current approach defines a query in the user interface without creating a physical view in the database. This approach retains business logic within the application, eliminates the need to copy scripts and objects between databases, and reduces overhead and object management.

Since queries can be complex and involve multiple tables, it is necessary to document the "Affected Table" of a View Dataset. For example, when using this dataset in a Construct App, any updates to the dataset are performed against the "Affected Table" in the database.

Mapping logical data elements in the View Dataset to the "Affected Column" in the Affected Table is also required. This mapping is explicit and does not rely on matching names, allowing flexibility even when names differ. Data elements without an associated Affected Column are considered read-only, as they cannot be updated.

Version 1 of this model supports only one Affected Table per View Dataset. Future enhancements may allow updates across multiple tables if the mapping of all tables to key values is feasible.

### Dataset: Rule

A Rule Dataset is created to insert, update, or delete data. For example, a Rule Dataset with an "update" intent can be used to set blank payment terms to 'NET30' for all active customers in the Customer table where the PaymentTerm is null or blank.

### Dataset: Validation

A Validation Dataset is created to execute validations against data and identify discrepancies. For example, a Validation Dataset can identify customers missing a PaymentTerm, which is required for invoicing. If the dataset returns records, those records indicate errors. If no records are returned, the data passes validation.
