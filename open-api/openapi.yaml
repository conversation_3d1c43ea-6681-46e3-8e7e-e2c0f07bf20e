openapi: 3.0.0
info:
  version: 0.0.1
  title: Catalog Service API
  description: |
    The Syniti Knowledge Platform (SKP) Catalog is the collection of technical and business data definitions that is used to document and describe the systems and business processes of our clients.
    The data definitons stored in the Catalog is used to enable several functionalities within SKP like data profiling, data migration, data quality, matching, data replication, reporting, etc.
    This catalog exists primarily as a means to enable this functionality, but should also be considered as a knowledge repository for the business users.

    Key features include:

    - Create and Manage Technical Data Definitions retrieved from the MetaScanner (Database Table, Database View)
    - Create and Manage Business Data Definitions (View, Rule, Validation)
    - Capture data profiling information
    - Ablity to version and manage the lifecycle of the Data Definitions
    - Support to catpure the schema of future polyglot Datasources (ODATA, OpenAPI, etc.)
servers:
  - url: http://catalog.govern.svc.cluster.local
  - url: https://ct.mgt.syniti-dev.com
security:
  - bearerAuth: []
tags:
  - name: DataModel
    description: Data Model Operations
  - name: Entity
    description: Entity Operations
  - name: EntityElement
    description: Element Operations
  - name: Import DataModel
    description: Import Data Model Operations
paths:
  /contexts/{context_id}/datamodel:
    post:
      tags:
        - DataModel
      summary: Create Data Model
      operationId: createDataModel
      description: |
        The API allows you to create a new data model with a complete, authoritative representation.


        **How It Works**
        - Creation: Use the POST method to create a new data model. The server stores it as the authoritative version.
        - Full Update: Use the PUT method to replace an existing data model entirely with an updated version.
        - Partial Update: Use the PATCH method for partial updates, specifying only the changes to be made.

        **Key Points for Consumers**
        - Complete Representation: The PUT method requires the entire data model. Missing sections will be removed or reset.
        - Versioning: Each full update creates a new version. Include the current version to avoid conflicts. Updates in progress for the same version will be rejected.
        - Idempotency: Repeating the same PUT request will yield the same result.

        **Data Model Context**
        - The data model is defined within a context like a Datastore.
        - Wihthin a context,
          - The name of the entity and relationship must be unique.
          - The name of an entity element must be unique within the entity.

        **Naming Rules**
        - Names can contain
          - alphanumeric characters
          - case insenstive
          - underscore
          - hyphen
        - Entity / Relationship Name
          - Must be unique within the namespace
        - Entity Element Name
          - Must be unique within the entity.
      parameters:
        - name: context_id
          in: path
          description: |
            The unique identifier for the datastore.
          required: true
          schema:
            type: string
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/DataModelRequest'
            examples:
              example-1:
                summary: Example of a Data Model
                value:
                  meta_schema_version: relational-20250501
                  entities:
                    - model_name: ns1.Product
                      name: Product
                      type: TABLE
                      consumption_type: dataset
                      description: ''
                      active: true
                      index: 1
                      tags: []
                      properties:
                        schema: Production
                      custom_properties: {}
                      entity_elements:
                        - model_name: ns1.Product.ProductID
                          name: ProductID
                          type: COLUMN
                          consumption_type: field
                          description: ''
                          active: true
                          index: 1
                          data_type: int
                          default_value: ''
                          precision: 10
                          scale: 0
                          is_required: false
                          tags: []
                          properties: {}
                          custom_properties: {}
                        - model_name: ns1.Product.Name
                          name: Name
                          type: COLUMN
                          consumption_type: field
                          index: 2
                          data_type: Name
                          default_value: ''
                          max_length: 50
                          is_required: false
                          properties: {}
                          custom_properties: {}
                        - model_name: ns1.Product.Model
                          name: Model
                          type: COLUMN
                          consumption_type: field
                          description: Stores the model GUID that is a foreign key to another table
                          index: 3
                          data_type: nvarchar
                          default_value: ''
                          max_length: 24
                          is_required: false
                          properties: {}
                          custom_properties: {}
                        - model_name: ns1.Product.Description
                          name: Description
                          type: COLUMN
                          consumption_type: field
                          index: 4
                          data_type: nvarchar
                          default_value: ''
                          max_length: 400
                          is_required: false
                          properties: {}
                          custom_properties: {}
                        - model_name: ns1.Product.Makeflag
                          name: Makeflag
                          type: COLUMN
                          consumption_type: field
                          index: 5
                          data_type: Flag
                          default_value: ''
                          precision: 1
                          scale: 0
                          is_required: false
                          properties: {}
                          custom_properties: {}
                        - model_name: ns1.Product.PK_ProductID
                          name: PK_ProductID
                          type: PRIMARY-KEY
                          consumption_type: key
                        - model_name: ns1.Product.IDX_Name_Makeflag
                          name: IDX_Name_MakeFlag
                          type: INDEX
                          consumption_type: index
                          properties:
                            is_unique: true
                        - model_name: ns1.Product.FK_Model
                          name: FK_Model
                          type: FOREIGN-KEY
                          consumption_type: constraint
                          properties:
                            on_update: no_action
                            on_delete: no_action
                    - model_name: ns1.ProductView
                      name: ProductView
                      type: VIEW
                      consumption_type: dataset
                      properties:
                        schema: Production
                      entity_elements:
                        - model_name: ns1.ProductView.ProductID
                          name: ProductID
                          type: COLUMN
                          consumption_type: field
                          index: 1
                          data_type: int
                          default_value: ''
                          precision: 10
                          scale: 0
                          is_required: false
                          properties: {}
                        - model_name: ns1.ProductView.Name
                          name: Name
                          type: COLUMN
                          consumption_type: field
                          index: 2
                          data_type: Name
                          default_value: ''
                          max_length: 50
                          is_required: false
                          properties: {}
                  relationships:
                    - name: PK_Production_Product_ProductID
                      type: PK_REF
                      description: ''
                      source: '#element/ns1.Product.PK_ProductID'
                      target: '#element/ns1.Product.ProductID'
                    - name: IDX_Production_Product_Name_MakeFlag_Name
                      type: IDX_REF
                      description: ''
                      index: 10
                      source: '#element/ns1.Product.IDX_Name_Makeflag'
                      target: '#element/ns1.Product.Name'
                    - name: IDX_Production_Product_Name_MakeFlag_MakeFlag
                      type: IDX_REF
                      description: ''
                      index: 20
                      source: '#element/ns1.Product.IDX_Name_Makeflag'
                      target: '#element/ns1.Product.Makeflag'
                    - name: FK_Production_Product_Model
                      type: FK_REF_SOURCE
                      description: ''
                      source: '#element/ns1.Product.FK_Model'
                      target: '#element/ns1.Product.Model'
                    - name: FK_Production_Product_Model
                      type: FK_REF_TARGET
                      description: ''
                      source: '#element/ns1.Product.FK_Model'
                      target: '#element/ns1.ProductModel.ModelID'
      responses:
        '200':
          description: |
            Created or Updated.
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/DataModelResponse'
              examples:
                example-1:
                  summary: Example of a Data Model Response
                  value:
                    new_version: 20250201-002
        '400':
          description: Bad Request.
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ApiError'
                examples:
                  example-1:
                    summary: Example of a Bad Request
                    value:
                      trace_id: 4567-987653-af456
                      type: https://catalog.syniti.com/errors/unsupported-meta-schema-version
                      title: Invalid Meta Schema Version
                      detail: The provided meta-version in the request is not valid. Use 'relational-20250204'.
        '401':
          $ref: '#/components/responses/UnAuthorizedError'
        '403':
          $ref: '#/components/responses/ForbiddenError'
    delete:
      tags:
        - DataModel
      summary: Delete Data Model
      operationId: deleteDataModel
      description: |
        This is a temporary API that allows synchronous cleanup of the full data model for the given context. It will eventually be changed into an asynchronous process in future.
      parameters:
        - name: context_id
          in: path
          description: |
            The unique identifier for the datastore.
          required: true
          schema:
            type: string
      responses:
        '200':
          description: |
            Sucessfully deleted.
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/DeleteDataModelResponse'
              examples:
                example-1:
                  summary: Example of a Data Model Response
                  value:
                    context_id: dst_222
        '400':
          description: Bad Request.
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ApiError'
                examples:
                  example-1:
                    summary: Example of a Bad Request
                    value:
                      context_id: dst_111
                      errors:
                        - code: CONTEXT_ID_NOT_FOUND
                          message: There exists no data model for the given context id.
        '401':
          $ref: '#/components/responses/UnAuthorizedError'
        '403':
          $ref: '#/components/responses/ForbiddenError'
  /contexts/{context_id}/datamodel/navigation:
    get:
      tags:
        - DataModel
      summary: Navigate Data Model
      operationId: navigateDataModel
      description: |
        Navigate the data model.


        Each data model (database schemas, OpenAPI definitions, or OData models) has its own unique structure and semantics for organizing its contents.
        With this API, you can start at the root of the data model and traverse its nested structure, accessing the contents in their original form.
        The API provides a consistent interface for navigating the data model, no matter its specific semantics or internal organization.


        You begin by providing no parameters and fetching the root contents of the model.
        From there, you can traverse along each of the root elements, exploring their nested structures and accessing the data in its original form.


        **Sample navigation for Database schema**

        `/navigate?context_id=dst_1877e07898eb4581ade0b0db4bd98963111`
        - This query returns the root entities which are tables, and views.

        `/navigate?context_id=dst_1877e07898eb4581ade0b0db4bd98963111&type=table`
        - This query fetches only root level entities of type 'table'.

        `/navigate?context_id=dst_1877e07898eb4581ade0b0db4bd98963111&parent_id=table123`
        - This query fetches all child elements of table with id 'table123'.

        `/navigate?context_id=dst_1877e07898eb4581ade0b0db4bd98963111&parent_id=table123&type=column`
        - This query fetches only column child elements of table with id 'table123'. It automatically resolves all references ($ref) and any relationships where column is the source.

        `/navigate?context_id=dst_1877e07898eb4581ade0b0db4bd98963111&parent_id=table123&type=primary-key&type=foreign-key&type=index`
        - This query fetches only constraint elements of table with id 'table123'. It automatically resolves all relationships where column is the source.

        **Sample navigation for Open API**

        `/navigate`
        - This query returns the root entities which are all operations. For e.g. 'addPet'

        `/navigate?type=schemas`
        - This query fetches all root level entities which are the schemas defined in the Open API. For e.g. 'Pet'

        `/navigate?parent_id=operation1`
        - This query fetches all child elements (parameter, request, response, security) of operation with id 'operation1'.

        `/navigate?parent_id=petschema1`
        - This query fetches all child elements of Pet schema with id 'petschema1'. It automatically resolves all references ($ref).

        **Sample navigation for ODATA**

        `/navigate`
        - This query returns the root entities which are all EntitySet, FunctionImport, ActionImport. For e.g. 'People'.

        `/navigate?type=entity_type`
        - This query fetches all root level entities which are the EntityType defined in the ODATA. For e.g. 'Person'. We can also fetch more that one type : enum_type | complex_type | entity_type | function | action.

        `/navigate?parent_id=entityset1`
        - This query fetches all child elements (navigation bindings) of EntitySet with id 'entityset1'.

        `/navigate?parent_id=entitytype1`
        - This query fetches all child elements (properties) of EntityType with id 'entitytype1'. It automatically resolves all references ($ref).

        **Sample navigation for Business Catalog schema**

        `/navigate`
        - This query returns the root dataset definitions (UI, QUERY, REPORT) and record type definitions

        `/navigate?type=ui`
        - This query fetches only root level dataset definitions of type 'UI'

        `/navigate?parent_id=dataset_def_ui_001`
        - This query fetches all child elements of dataset definition with id 'dataset_def_ui_001'.

        `/navigate?parent_id=dataset_def_ui_001&type=property`
        - This query fetches only property child elements of dataset definition with id 'dataset_def_ui_001'. It automatically resolves all references.
      parameters:
        - name: context_id
          description: |
            The identifier of the context for which the data model needs to be fetched.
            The context can be a Datastore or a Subject Area. This is a mandatory parameter.
          in: path
          schema:
            type: string
          required: true
        - name: parent_id
          description: |
            The identifier of the parent under which the child elements needs to be fetched.
            If not specified, then the root level model elements are fetched.
          in: query
          schema:
            type: string
        - name: type
          in: query
          description: |
            Specify the type of contents to be fetched. Its value is specific to each type of data model being explored. \
            \
            Database: table, view, column, primary-key, index, foreign-key \
            Open API: operation, schema, security_scheme \
            ODATA: entity_set, function_import, action_import, entity_type, enum_type, complex_type, function, action
          schema:
            type: array
            items:
              type: string
        - name: first
          in: query
          description: |
            Number of requested results for paging
          schema:
            type: integer
        - name: offset
          in: query
          description: |
            Encoded value used to indicate where in the result set to begin.
            This is used for paging. To fetch the next page of results, use the value returned in the `next_token` field of the previous response.
          schema:
            type: string
        - name: sort
          in: query
          description: A base64 encoded sort value
          schema:
            type: string
      responses:
        '200':
          description: Model contents list.
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/NavigateResponse'
        '400':
          description: Bad Request
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ApiError'
        '401':
          $ref: '#/components/responses/UnAuthorizedError'
        '403':
          $ref: '#/components/responses/ForbiddenError'
  /contexts/{context_id}/datamodel/entities:
    get:
      tags:
        - Entity
      summary: Retrieve a List of Entities
      operationId: findEntities
      description: |
        The API endpoint allows you to fetch the data model entities.
        The API supports pagination and filtering options to manage large results effectively.
      parameters:
        - name: context_id
          description: |
            Filter entities by the context like a datastore or subject areas identifier.
          in: path
          schema:
            type: string
        - name: type
          in: query
          description: |
            Filter by the type of entity.
            For example `TABLE`.
            Please review the meta-meta information for the data model for the valid types.
          schema:
            type: array
            items:
              type: string
        - name: consumption_type
          in: query
          description: |
            Filter by the consumption_type of entity.
            For example `table`.
          schema:
            type: array
            items:
              type: string
        - name: tags
          in: query
          description: |
            Filter by the tags of entity.
          schema:
            type: array
            items:
              type: string
        - name: name
          description: |
            Filter entities by the matching exact name.
          in: query
          schema:
            type: string
        - name: name_text
          description: |
            Filter entities using contains text search on the name of the entity.
          in: query
          schema:
            type: string
        - name: description_text
          description: |
            Filter entities using contains text search on the description of the entity.
          in: query
          schema:
            type: string
        - name: first
          in: query
          description: Number of requested results for paging
          schema:
            type: integer
        - name: offset
          in: query
          description: Encoded value used to indicate where in the result set to begin
          schema:
            type: string
        - name: sort
          in: query
          description: A base64 encoded sort value
          schema:
            type: string
        - name: filter
          in: query
          description: A base64 encoded filter value
          schema:
            type: string
      responses:
        '200':
          description: Entities list.
          content:
            application/json:
              schema:
                type: object
                properties:
                  next_token:
                    type: string
                    description: |
                      The token to fetch the next set of results.
                  items:
                    description: |
                      The list of entities.
                      The list is paginated and the next_token can be used to fetch the next set of results.
                    type: array
                    items:
                      $ref: '#/components/schemas/Entity'
        '400':
          description: Bad Request
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ApiError'
        '401':
          $ref: '#/components/responses/UnAuthorizedError'
        '403':
          $ref: '#/components/responses/ForbiddenError'
  /contexts/{context_id}/datamodel/elements:
    get:
      tags:
        - EntityElement
      summary: Retrieve a List of Entity Elements
      operationId: findElements
      description: |
        The API endpoint allows you to fetch the data model entity elements.
        The API supports pagination and filtering options to manage large results effectively.
      parameters:
        - name: entity_id
          description: |
            Fetch elements for the parent entity.
          in: path
          required: true
          schema:
            type: string
        - name: type
          in: query
          description: |
            Filter by the type of entity element.
            For example `COLUMN`.
            Please review the meta-meta information for the data model for the valid types.
          schema:
            type: array
            items:
              type: string
        - name: consumption_type
          in: query
          description: |
            Filter by the consumption_type of entity element.
            For example `columns`.
          schema:
            type: array
            items:
              type: string
        - name: tags
          in: query
          description: |
            Filter by the tags of entity element.
          schema:
            type: array
            items:
              type: string
        - name: name
          description: |
            Filter entity elements by the matching exact name.
          in: query
          schema:
            type: string
        - name: name_text
          description: |
            Filter entity elements using contains text search on the name of the entity.
          in: query
          schema:
            type: string
        - name: description_text
          description: |
            Filter entity elements using contains text search on the description of the entity.
          in: query
          schema:
            type: string
        - name: first
          in: query
          description: Number of requested results for paging
          schema:
            type: integer
        - name: offset
          in: query
          description: Encoded value used to indicate where in the result set to begin
          schema:
            type: string
        - name: sort
          in: query
          description: A base64 encoded sort value
          schema:
            type: string
        - name: filter
          in: query
          description: A base64 encoded filter value
          schema:
            type: string
      responses:
        '200':
          description: Entity Elements list.
          content:
            application/json:
              schema:
                type: object
                properties:
                  next_token:
                    type: string
                    description: |
                      The token to fetch the next set of results.
                  items:
                    description: |
                      The list of entity elements.
                      The list is paginated and the next_token can be used to fetch the next set of results.
                    type: array
                    items:
                      $ref: '#/components/schemas/EntityElement'
        '400':
          description: Bad Request
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ApiError'
        '401':
          $ref: '#/components/responses/UnAuthorizedError'
        '403':
          $ref: '#/components/responses/ForbiddenError'
  /datamodel-import:
    post:
      tags:
        - Import DataModel
      summary: Import Data Model
      operationId: importDataModel
      description: |
        The API enables batch import of data models, accommodating scenarios where the MetaScanner scans technical data models with thousands of tables and columns.


        **How It Works**

        1. Logical Transaction Identifier:  
          The entire import process must be handled as a single transaction to ensure the local data model is correctly updated with the scanned data model.
          A `scan_id` is generated as part of a SystemScan entry in the Knowledge Graph.
          This `scan_id` ensures that all batches are imported as part of the same transaction.
        2. Batch Import:  
          Use the import API with the `scan_id` to import all batches of the data model.
        3. Mark Import as Completed:  
          Once all batches are imported, invoke the API to mark the transaction as complete.

        **Key Points for Consumers**

        - Idempotent: The import process is idempotent, ensuring that re-importing the same batch does not create duplicate data.
        - `scan_id`: The `scan_id` is critical for maintaining transactional integrity. It must be provided consistently for all batch imports.
        - Single Import: Only one batch import is allowed at a time for a specific data model context.
      parameters:
        - name: context_id
          description: |
            The identifier of the context for which the data model needs to be imported.
            The context can be a Datastore or a Subject Area. This is a mandatory parameter.
          in: path
          schema:
            type: string
          required: true
        - name: scan_id
          in: path
          description: |
            The unique identifier for the scan.
            This identifier is used to track the import process and ensure that all batches are part of the same transaction.
            The `scan_id` is generated as part of a SystemScan entry in the Knowledge Graph.
            It is critical for maintaining transactional integrity and must be provided consistently for all batch imports.
          required: true
          schema:
            type: string
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/ImportDataModelRequest'
            examples:
              example-1:
                summary: Example of importing a data model
                value:
                  data_model:
                    - model_element_type: entity
                      model_name: ns1.Product
                      name: Product
                      type: TABLE
                      consumption_type: dataset
                      active: true
                      properties:
                        schema: Production
                    - model_element_type: element
                      entity: ns1.Product
                      model_name: ns1.Product.Shipmentid
                      name: ShipmentId
                      type: COLUMN
                      consumption_type: field
                      active: true
                      index: 1
                      data_type: int
                      default_value: ''
                      nulls_allowed: false
                    - model_element_type: element
                      entity: ns1.Product
                      model_name: ns1.Production.Product.PK_Shipmentid
                      name: PK_ShipmentId
                      type: PRIMARY-KEY
                      consumption_type: key
                    - model_element_type: relation
                      type: PK_REF
                      description: ''
                      source: '#element/ns1.Product.PK_Shipmentid'
                      target: '#element/ns1.Product.Shipmentid'
                    - model_element_type: entity
                      operation: delete
                      name: Employee
      responses:
        '200':
          description: |
            Sucessfully imported.
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ImportDataModelResponse'
              examples:
                example-1:
                  summary: Example of a Data Model Response
                  value:
                    new_model_version: 20250201-002
        '400':
          description: Bad Request.
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ApiError'
                examples:
                  example-1:
                    summary: Example of a Bad Request
                    value:
                      request_id: 4567-987653-af456
                      errors:
                        - code: UNSUPPORTED_META_VERSION
                          message: The provided meta-version in the request is not valid. Use 'relational-20250204'.
        '401':
          $ref: '#/components/responses/UnAuthorizedError'
        '403':
          $ref: '#/components/responses/ForbiddenError'
components:
  parameters:
    limit:
      name: limit
      in: query
      description: How many items to return at one time (max 100)
      required: false
      schema:
        type: integer
        format: int32
  schemas:
    EntityElement:
      type: object
      description: |
        Represents the characteristics or parts that make up an entity (e.g., `Columns` in a Table).
      required:
        - name
        - type
        - consumption_type
      properties:
        id:
          type: string
          description: The unique identifier of the element
        model_name:
          type: string
          description: |
            The canonical name of the entity element.
            It is used to identify the entity element in the context of the data model and for cross references.
        name:
          type: string
          description: |
            The name of the entity element.
            It should be unique within the Entity.
            The name should follow the naming conventions and should not contain any special characters or spaces.
        description:
          type: string
          description: A description of the element.
        type:
          type: string
          description: |
            The actual source type of element.
            It can be a Column, Primary Key, or any other object that is relevant to the data model.
            Please refer the meta_schema_version documentation for the list of valid entity types.
        consumption_type:
          type: string
          description: |
            The normalized type of element as consumed by various SKP applications.
            Please refer the meta_schema_version documentation for the list of valid entity element types.
        index:
          type: integer
          description: |
            Indicates the ordinal position of this element.
            Default is 0.
        data_type:
          type: string
          description: |
            The underlying native data type associated with this element.
            It can be a string, integer, float, or any other data type that is relevant to the data model.
            Please refer the meta_schema_version documentation for the list of valid data types.
        default_value:
          type: string
          description: |
            The default value to be used for this element
        enum:
          type: array
          items:
            type: string
          description: |
            A list of possible values for the element.
            This is used to restrict the values that can be assigned to the element.
        is_required:
          type: boolean
          description: |
            Indicates if the element is a required property.
            Default is false.
        is_array:
          type: boolean
          description: |
            Indicates if the element is an array.
            Default is false.
        max_length:
          type: integer
          description: |
            The maximum length of the element.
            This is used to specify the maximum number of characters that can be stored in the element.
        precision:
          type: integer
          description: |
            The precision of the element.
            This is used to specify the number of digits that can be stored in the element.
        scale:
          type: integer
          description: |
            The scale of the element.
            This is used to specify the number of digits that can be stored in the element after the decimal point.
        properties:
          type: object
          description: |
            Type specific properties.
            A map of key value pairs that captures the properties specific to the element type.
            For example, if the type=INDEX, then we capture `is_clustered` key value.
            Please see the meta_schema_version documentation for the list of valid keys.
        custom_properties:
          type: object
          description: |
            Custom properties for the entity element. `Check with John if this is required`.`
            A map of key value pairs that captures the custom properties specific to the element.
            The keys are defined by the consumer and can be used to capture any additional information about the element.
        tags:
          type: array
          items:
            type: string
          description: |
            A list of tags associated with the entity element.
            Tags can be used to categorize or label the element for easier identification and management.
            Tags can be used for filtering and searching elements within the data model.
        created_by:
          type: string
          description: |
            The user who created the entity element.
            This is usually a system generated value and is used for auditing purposes.
    Entity:
      type: object
      description: |
        Each entity represents a distinct object or concept within the data model.
        The entity can be a table, view, or any other object that is relevant to the data model.
        Each entity has a unique name and can have multiple entity elements (fields) associated with it.
      required:
        - name
        - type
        - consumption_type
      properties:
        id:
          type: string
          description: The unique identifier of the Entity
        model_name:
          type: string
          description: |
            The canonical name of the entity.
            It is used to identify the entity in the context of the data model and for cross references.
        name:
          type: string
          description: |
            The name of the entity.
            It should be unique within the context of the data model.
            The name should follow the naming conventions and should not contain any special characters or spaces.
        description:
          type: string
          description: A description of the entity.
        type:
          type: string
          description: |
            The actual source type of entity.
            It can be a table, view, or any other object that is relevant to the data model.
            Please refer the meta_schema_version documentation for the list of valid entity types.
        consumption_type:
          type: string
          description: |
            The normalized type of entity as consumed by various SKP applications.
            Please refer the meta_schema_version documentation for the list of valid entity types.
        index:
          type: integer
          description: |
            Indicates the ordinal position of this entity.
            Default is 0.
        properties:
          type: object
          description: |
            Type specific properties.
            A map of key value pairs that captures the properties specific to the entity type.
            For example, if the type=TABLE, then we capture `schema` key value.
            Please see the meta_schema_version documentation for the list of valid keys.
        custom_properties:
          type: object
          description: |
            Custom properties for the entity.
            A map of key value pairs that captures the custom properties specific to the entity.
            The keys are defined by the consumer and can be used to capture any additional information about the entity.
        tags:
          type: array
          items:
            type: string
          description: |
            A list of tags associated with the entity.
            Tags can be used to categorize or label the entity for easier identification and management.
            Tags can be used for filtering and searching entities within the data model.
        created_by:
          type: string
          description: |
            The user who created the entity.
            This is usually a system generated value and is used for auditing purposes.
        entity_elements:
          type: array
          items:
            $ref: '#/components/schemas/EntityElement'
          description: |
            The list of entity elements (fields) associated with the entity.
            For example, in case of relation database model, a database Column is represented as an entity element.
            Each entity element has a unique name.
    ModelElementRef:
      type: object
      description: An object representing a component that requires metadata scanning.
      properties:
        id:
          type: string
          description: unique identifier of the Component
          example: cpt_xkHRtSJrPh4JLgeZQLfA7VdLsbi
        $ref:
          type: string
          description: the
    Relationship:
      type: object
      description: |
        A relationship represents a connection between entities or entity elements
      required:
        - type
        - source
        - target
      properties:
        id:
          type: string
          description: The unique identifier of the Relationship
        name:
          type: string
          description: |
            The name of the relationship.
            It should be unique within the context of the data model.
            The name should follow the naming conventions and should not contain any special characters or spaces.
        description:
          type: string
          description: |
            A description of the relationship.
            This should provide additional context or information about the relationship.
        type:
          type: string
          description: |
            The type of relationship.
            Please the meta_schema_version documentation for the list of valid relationship types.
        index:
          type: integer
          description: |
            Indicates the ordinal position of this relationship.
            Default is 0.
        source:
          $ref: '#/components/schemas/ModelElementRef'
          description: |
            The source of the relationship.
            It can be an entity or an entity element.
            The source is the starting point of the relationship and is used to identify the origin of the connection.
        target:
          $ref: '#/components/schemas/ModelElementRef'
          description: |
            The target of the relationship.
            It can be an entity or an entity element.
            The target is the endpoint of the relationship and is used to identify the destination of the connection.
        properties:
          type: object
          description: |
            Type specific properties.
            A map of key value pairs that captures the properties specific to the relationship.
        tags:
          type: array
          items:
            type: string
          description: |
            A list of tags associated with the relationship.
            Tags can be used to categorize or label the relationship for easier identification and management.
            Tags can be used for filtering and searching relationships within the data model.
        created_by:
          type: string
          description: |
            The user who created the relationship.
            This is usually a system generated value and is used for auditing purposes.
    DataModelRequest:
      type: object
      required:
        - meta_schema_version
      properties:
        meta_schema_version:
          type: string
          description: |
            The meta-meta schema version.
            It controls the rules and structure of the data model, including the allowed fields, their types, and other constraints
            The value of this field ensures that the system understands how to interpret the data and which validation rules to apply.
            For example, the version `relational-20250501` informs the system that the data model defined in the current API is representing the database schama.
            Hence the system can validate that the allowed entity types are only TABLE and VIEW.
        entities:
          type: array
          items:
            $ref: '#/components/schemas/Entity'
          description: |
            The list of entities in the data model.
            Each entity represents a distinct object or concept within the data model.
            The entity can be a table, view, or any other object that is relevant to the data model.
            Each entity has a unique name and can have multiple entity elements (fields) associated with it.
        relationships:
          type: array
          items:
            $ref: '#/components/schemas/Relationship'
          description: |
            The list of relationships in the data model.
            Each relationship represents a connection between entities or entity elements.
    DataModelResponse:
      type: object
      description: |
        The DataModelResponse schema represents the response structure for a data model request.
        It contains metadata about the data model, including its new version.
      required:
        - new_version
      properties:
        new_version:
          type: string
          description: |
            The new version of the data model.
    ErrorDetail:
      type: object
      description: Error detail information
      required:
        - message
      properties:
        message:
          description: Specific error message.
          type: string
        field:
          description: The field that caused the error (if applicable).
          type: string
      example:
        field: name
        message: name is required.
    ApiError:
      type: object
      description: API error response
      required:
        - trace_id
        - type
        - title
      properties:
        trace_id:
          description: The unique identifier associated with the API request to assist with troubleshooting.
          type: string
        type:
          description: A URI reference [URI] that identifies the error type.
          type: string
        status:
          description: The HTTP status code associated with the error.
          type: int
        title:
          description: A short, human-readable summary of the error type.
          type: string
        detail:
          description: A human-readable explanation specific to this occurrence of the error.
          type: string
        message:
          description: Duplicate of detail field for backward compatibility.
          type: string
        errors:
          description: An optional list of individual errors when multiple errors occur.
          type: array
          items:
            $ref: '#/components/schemas/ErrorDetail'
      example:
        trace_id: 123e4567-e89b-12d3-a456-426614174000
        type: https://catalog.syniti.com/errors/validation-error
        status: 400
        title: Validation Error
        detail: 'One or more validation errors occurred. Errors: name: [name is required], age: [must be a positive integer]'
        errors:
          - field: name
            message: name is required.
          - field: age
            message: must be a positive integer.
    DeleteDataModelResponse:
      type: object
      properties:
        results:
          type: object
          properties:
            context_id:
              type: string
            entities_deleted:
              type: integer
              description: |
                Indicates the number of entities deleted.
            elements_deleted:
              type: integer
              description: |
                Indicates the number of elements deleted.
            relationships_deleted:
              type: integer
              description: |
                Indicates the number of relationships deleted.
            versions_deleted:
              type: integer
              description: |
                Indicates the number of versions deleted.
    NavigateResponse:
      type: object
      properties:
        results:
          type: object
          properties:
            entities:
              type: array
              items:
                $ref: '#/components/schemas/Entity'
              description: |
                The list of entities found in the data model.
            entity_elements:
              type: array
              items:
                $ref: '#/components/schemas/EntityElement'
              description: |
                The list of entity elements found in the data model.
            relationships:
              type: array
              items:
                $ref: '#/components/schemas/Relationship'
              description: |
                The list of relationships found in the data model.
        next_token:
          type: string
          description: |
            The token to be used to fetch the next page of results. This is a base64 encoded value.
            If there are no more results, this field will not be present in the response.
    ImportDataModelRequest:
      type: object
      properties:
        entities:
          type: array
          items:
            $ref: '#/components/schemas/Entity'
          description: |
            The list of entities in the data model.
            Each entity represents a distinct object or concept within the data model.
            The entity can be a table, view, or any other object that is relevant to the data model.
            Each entity has a unique name and can have multiple entity elements (fields) associated with it.
        relationships:
          type: array
          items:
            $ref: '#/components/schemas/Relationship'
          description: |
            The list of relationships in the data model.
            Each relationship represents a connection between entities or entity elements.
    ImportDataModelResponse:
      type: object
      description: |
        The DataModelResponse schema represents the response structure for a data model request.
        It contains metadata about the data model, including its new version.
      required:
        - new_version
      properties:
        new_version:
          type: string
          description: |
            The new version of the data model.
    CatalogError:
      $ref: '#/components/schemas/ApiError'
  securitySchemes:
    bearerAuth:
      description: |
        JWT token for authentication.
        The token should be passed in the `Authorization` header as a Bearer token.
      type: http
      scheme: bearer
      bearerFormat: JWT
  responses:
    UnAuthorizedError:
      description: |
        Authentication information is expired, missing, or invalid.
        Ensure that you have authenticated correctly.
    ForbiddenError:
      description: |
        Authorization failed or access denied error.
        Ensure that you have the necessary permissions.
x-tagGroups:
  - name: API Reference
    tags:
      - DataModel
      - Entity
      - EntityElement
      - Import DataModel
  - name: Models
    tags:
      - Schemas
