openapi: "3.0.0"
info:
  version: "0.0.1"
  title: Catalog Service API
  description: |
    The Syniti Knowledge Platform (SKP) Catalog is the collection of technical and business data definitions that is used to document and describe the systems and business processes of our clients.
    The data definitons stored in the Catalog is used to enable several functionalities within SKP like data profiling, data migration, data quality, matching, data replication, reporting, etc.
    This catalog exists primarily as a means to enable this functionality, but should also be considered as a knowledge repository for the business users.

    Key features include:

    - Create and Manage Technical Data Definitions retrieved from the MetaScanner (Database Table, Database View)
    - Create and Manage Business Data Definitions (View, Rule, Validation)
    - Capture data profiling information
    - Ablity to version and manage the lifecycle of the Data Definitions
    - Support to catpure the schema of future polyglot Datasources (ODATA, OpenAPI, etc.)
x-tagGroups:
  - name: API Reference
    tags:
      - DataModel
      - Entity
      - EntityElement
      - Import DataModel
  - name: Models
    tags:
      - Schemas
tags:
  - name: DataModel
    description: Data Model Operations
  - name: Entity
    description: Entity Operations
  - name: EntityElement
    description: Element Operations
  - name: Import DataModel
    description: Import Data Model Operations
servers:
  - url: http://catalog.govern.svc.cluster.local
  - url: https://ct.mgt.syniti-dev.com
security:
  - bearerAuth: []
paths:
  /contexts/{context_id}/datamodel:
    $ref: "./paths/datamodel/_index.yaml"
  /contexts/{context_id}/datamodel/navigation:
    $ref: "./paths/navigate/_index.yaml"
  /contexts/{context_id}/datamodel/entities:
    $ref: "./paths/entities/_index.yaml"
  /contexts/{context_id}/datamodel/elements:
    $ref: "./paths/elements/_index.yaml"
  /datamodel-import:
    $ref: "./paths/import/_index.yaml"
components:
  parameters:
    $ref: "./parameters/_index.yaml"
  schemas:
    $ref: "./schemas/_index.yaml"
  securitySchemes:
    bearerAuth:
      description: |
        JWT token for authentication.
        The token should be passed in the `Authorization` header as a Bearer token.
      type: http
      scheme: bearer
      bearerFormat: JWT
