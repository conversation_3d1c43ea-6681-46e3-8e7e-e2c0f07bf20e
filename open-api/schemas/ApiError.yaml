type: object
description: API error response
required:
  - trace_id
  - type
  - title
properties:
  trace_id:
    description: The unique identifier associated with the API request to assist with troubleshooting.
    type: string
  type:
    description: A URI reference [URI] that identifies the error type.
    type: string
  status:
    description: The HTTP status code associated with the error.
    type: int
  title:
    description: A short, human-readable summary of the error type.
    type: string
  detail:
    description: A human-readable explanation specific to this occurrence of the error.
    type: string
  message:
    description: Duplicate of detail field for backward compatibility.
    type: string
  errors:
    description: An optional list of individual errors when multiple errors occur.
    type: array
    items:
      $ref: "./ErrorDetail.yaml"
example:
  trace_id: 123e4567-e89b-12d3-a456-426614174000
  type: https://catalog.syniti.com/errors/validation-error
  status: 400
  title: Validation Error
  detail: "One or more validation errors occurred. Errors: name: [name is required], age: [must be a positive integer]"
  errors:
    - field: name
      message: name is required.
    - field: age
      message: must be a positive integer.
