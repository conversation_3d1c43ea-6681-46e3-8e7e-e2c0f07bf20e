type: object
properties:
  results:
    type: object
    properties:
      context_id:
        type: string
      entities_deleted:
        type: integer
        description: |
          Indicates the number of entities deleted.
      elements_deleted:
        type: integer
        description: |
          Indicates the number of elements deleted.
      relationships_deleted:
        type: integer
        description: |
          Indicates the number of relationships deleted.
      versions_deleted:
        type: integer
        description: |
          Indicates the number of versions deleted.
