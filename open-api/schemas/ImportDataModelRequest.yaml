type: object
properties:
  entities:
    type: array
    items:
      $ref: "./Entity.yaml"
    description: |
      The list of entities in the data model.
      Each entity represents a distinct object or concept within the data model.
      The entity can be a table, view, or any other object that is relevant to the data model.
      Each entity has a unique name and can have multiple entity elements (fields) associated with it.
  relationships:
    type: array
    items:
      $ref: "./Relationship.yaml"
    description: |
      The list of relationships in the data model.
      Each relationship represents a connection between entities or entity elements.
