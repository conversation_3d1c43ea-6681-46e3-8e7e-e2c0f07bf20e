type: object
required:
  - meta_schema_version
properties:
  meta_schema_version:
    type: string
    description: |
      The meta-meta schema version.
      It controls the rules and structure of the data model, including the allowed fields, their types, and other constraints
      The value of this field ensures that the system understands how to interpret the data and which validation rules to apply.
      For example, the version `relational-20250501` informs the system that the data model defined in the current API is representing the database schama.
      Hence the system can validate that the allowed entity types are only TABLE and VIEW.
  entities:
    type: array
    items:
      $ref: "./Entity.yaml"
    description: |
      The list of entities in the data model.
      Each entity represents a distinct object or concept within the data model.
      The entity can be a table, view, or any other object that is relevant to the data model.
      Each entity has a unique name and can have multiple entity elements (fields) associated with it.
  relationships:
    type: array
    items:
      $ref: "./Relationship.yaml"
    description: |
      The list of relationships in the data model.
      Each relationship represents a connection between entities or entity elements.
