type: object
description: |
  Represents the characteristics or parts that make up an entity (e.g., `Columns` in a Table).
required:
  - name
  - type
  - consumption_type
properties:
  id:
    type: string
    description: The unique identifier of the element
  model_name:
    type: string
    description: |
      The canonical name of the entity element.
      It is used to identify the entity element in the context of the data model and for cross references.
  name:
    type: string
    description: |
      The name of the entity element.
      It should be unique within the Entity.
      The name should follow the naming conventions and should not contain any special characters or spaces.
  description:
    type: string
    description: A description of the element.
  type:
    type: string
    description: |
      The actual source type of element.
      It can be a Column, Primary Key, or any other object that is relevant to the data model.
      Please refer the meta_schema_version documentation for the list of valid entity types.
  consumption_type:
    type: string
    description: |
      The normalized type of element as consumed by various SKP applications.
      Please refer the meta_schema_version documentation for the list of valid entity element types.
  index:
    type: integer
    description: |
      Indicates the ordinal position of this element.
      Default is 0.
  data_type:
    type: string
    description: |
      The underlying native data type associated with this element.
      It can be a string, integer, float, or any other data type that is relevant to the data model.
      Please refer the meta_schema_version documentation for the list of valid data types.
  default_value:
    type: string
    description: |
      The default value to be used for this element
  enum:
    type: array
    items:
      type: string
    description: |
      A list of possible values for the element.
      This is used to restrict the values that can be assigned to the element.
  is_required:
    type: boolean
    description: |
      Indicates if the element is a required property.
      Default is false.
  is_array:
    type: boolean
    description: |
      Indicates if the element is an array.
      Default is false.
  max_length:
    type: integer
    description: |
      The maximum length of the element.
      This is used to specify the maximum number of characters that can be stored in the element.
  precision:
    type: integer
    description: |
      The precision of the element.
      This is used to specify the number of digits that can be stored in the element.
  scale:
    type: integer
    description: |
      The scale of the element.
      This is used to specify the number of digits that can be stored in the element after the decimal point.
  properties:
    type: object
    description: |
      Type specific properties.
      A map of key value pairs that captures the properties specific to the element type.
      For example, if the type=INDEX, then we capture `is_clustered` key value.
      Please see the meta_schema_version documentation for the list of valid keys.
  custom_properties:
    type: object
    description: |
      Custom properties for the entity element. `Check with John if this is required`.`
      A map of key value pairs that captures the custom properties specific to the element.
      The keys are defined by the consumer and can be used to capture any additional information about the element.
  tags:
    type: array
    items:
      type: string
    description: |
      A list of tags associated with the entity element.
      Tags can be used to categorize or label the element for easier identification and management.
      Tags can be used for filtering and searching elements within the data model.
  created_by:
    type: string
    description: |
      The user who created the entity element.
      This is usually a system generated value and is used for auditing purposes.
