tags:
  - DataModel
summary: Navigate Data Model
operationId: navigateDataModel
description: |
  Navigate the data model.


  Each data model (database schemas, OpenAPI definitions, or OData models) has its own unique structure and semantics for organizing its contents.
  With this API, you can start at the root of the data model and traverse its nested structure, accessing the contents in their original form.
  The API provides a consistent interface for navigating the data model, no matter its specific semantics or internal organization.


  You begin by providing no parameters and fetching the root contents of the model.
  From there, you can traverse along each of the root elements, exploring their nested structures and accessing the data in its original form.


  **Sample navigation for Database schema**

  `/navigate?context_id=dst_1877e07898eb4581ade0b0db4bd98963111`
  - This query returns the root entities which are tables, and views.

  `/navigate?context_id=dst_1877e07898eb4581ade0b0db4bd98963111&type=table`
  - This query fetches only root level entities of type 'table'.

  `/navigate?context_id=dst_1877e07898eb4581ade0b0db4bd98963111&parent_id=table123`
  - This query fetches all child elements of table with id 'table123'.

  `/navigate?context_id=dst_1877e07898eb4581ade0b0db4bd98963111&parent_id=table123&type=column`
  - This query fetches only column child elements of table with id 'table123'. It automatically resolves all references ($ref) and any relationships where column is the source.

  `/navigate?context_id=dst_1877e07898eb4581ade0b0db4bd98963111&parent_id=table123&type=primary-key&type=foreign-key&type=index`
  - This query fetches only constraint elements of table with id 'table123'. It automatically resolves all relationships where column is the source.

  **Sample navigation for Open API**

  `/navigate`
  - This query returns the root entities which are all operations. For e.g. 'addPet'

  `/navigate?type=schemas`
  - This query fetches all root level entities which are the schemas defined in the Open API. For e.g. 'Pet'

  `/navigate?parent_id=operation1`
  - This query fetches all child elements (parameter, request, response, security) of operation with id 'operation1'.

  `/navigate?parent_id=petschema1`
  - This query fetches all child elements of Pet schema with id 'petschema1'. It automatically resolves all references ($ref).

  **Sample navigation for ODATA**

  `/navigate`
  - This query returns the root entities which are all EntitySet, FunctionImport, ActionImport. For e.g. 'People'.

  `/navigate?type=entity_type`
  - This query fetches all root level entities which are the EntityType defined in the ODATA. For e.g. 'Person'. We can also fetch more that one type : enum_type | complex_type | entity_type | function | action.

  `/navigate?parent_id=entityset1`
  - This query fetches all child elements (navigation bindings) of EntitySet with id 'entityset1'.

  `/navigate?parent_id=entitytype1`
  - This query fetches all child elements (properties) of EntityType with id 'entitytype1'. It automatically resolves all references ($ref).

  **Sample navigation for Business Catalog schema**

  `/navigate`
  - This query returns the root dataset definitions (UI, QUERY, REPORT) and record type definitions

  `/navigate?type=ui`
  - This query fetches only root level dataset definitions of type 'UI'

  `/navigate?parent_id=dataset_def_ui_001`
  - This query fetches all child elements of dataset definition with id 'dataset_def_ui_001'.

  `/navigate?parent_id=dataset_def_ui_001&type=property`
  - This query fetches only property child elements of dataset definition with id 'dataset_def_ui_001'. It automatically resolves all references.
parameters:
  - name: context_id
    description: |
      The identifier of the context for which the data model needs to be fetched.
      The context can be a Datastore or a Subject Area. This is a mandatory parameter.
    in: path
    schema:
      type: string
    required: true
  - name: parent_id
    description: |
      The identifier of the parent under which the child elements needs to be fetched.
      If not specified, then the root level model elements are fetched.
    in: query
    schema:
      type: string
  - name: type
    in: query
    description: |
      Specify the type of contents to be fetched. Its value is specific to each type of data model being explored. \
      \
      Database: table, view, column, primary-key, index, foreign-key \
      Open API: operation, schema, security_scheme \
      ODATA: entity_set, function_import, action_import, entity_type, enum_type, complex_type, function, action
    schema:
      type: array
      items:
        type: string
  - name: first
    in: query
    description: |
      Number of requested results for paging
    schema:
      type: integer
  - name: offset
    in: query
    description: |
      Encoded value used to indicate where in the result set to begin.
      This is used for paging. To fetch the next page of results, use the value returned in the `next_token` field of the previous response.
    schema:
      type: string
  - name: sort
    in: query
    description: A base64 encoded sort value
    schema:
      type: string
responses:
  "200":
    description: Model contents list.
    content:
      application/json:
        schema:
          $ref: "../../schemas/NavigateResponse.yaml"
  "400":
    description: Bad Request
    content:
      application/json:
        schema:
          $ref: "../../schemas/CatalogError.yaml"
  "401":
    $ref: "../../schemas/UnAuthorizedError.yaml"
  "403":
    $ref: "../../schemas/ForbiddenError.yaml"
