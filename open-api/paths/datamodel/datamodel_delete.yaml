tags:
  - DataModel
summary: Delete Data Model
operationId: deleteDataModel
description: |
  This is a temporary API that allows synchronous cleanup of the full data model for the given context. It will eventually be changed into an asynchronous process in future.

parameters:
  - name: context_id
    in: path
    description: |
      The unique identifier for the datastore.
    required: true
    schema:
      type: string
responses:
  "200":
    description: |
      Sucessfully deleted.
    content:
      application/json:
        schema:
          $ref: "../../schemas/DeleteDataModelResponse.yaml"
        examples:
          example-1:
            summary: Example of a Data Model Response
            value:
              context_id: "dst_222"
  "400":
    description: Bad Request.
    content:
      application/json:
        schema:
          $ref: "../../schemas/CatalogError.yaml"
          examples:
            example-1:
              summary: Example of a Bad Request
              value:
                context_id: "dst_111"
                errors:
                  - code: "CONTEXT_ID_NOT_FOUND"
                    message: "There exists no data model for the given context id."
  "401":
    $ref: "../../schemas/UnAuthorizedError.yaml"
  "403":
    $ref: "../../schemas/ForbiddenError.yaml"
