tags:
  - DataModel
summary: Create Data Model
operationId: createDataModel
description: |
  The API allows you to create a new data model with a complete, authoritative representation.


  **How It Works**
  - Creation: Use the POST method to create a new data model. The server stores it as the authoritative version.
  - Full Update: Use the PUT method to replace an existing data model entirely with an updated version.
  - Partial Update: Use the PATCH method for partial updates, specifying only the changes to be made.

  **Key Points for Consumers**
  - Complete Representation: The PUT method requires the entire data model. Missing sections will be removed or reset.
  - Versioning: Each full update creates a new version. Include the current version to avoid conflicts. Updates in progress for the same version will be rejected.
  - Idempotency: Repeating the same PUT request will yield the same result.

  **Data Model Context**
  - The data model is defined within a context like a Datastore.
  - Wihthin a context,
    - The name of the entity and relationship must be unique.
    - The name of an entity element must be unique within the entity.

  **Naming Rules**
  - Names can contain
    - alphanumeric characters
    - case insenstive
    - underscore
    - hyphen
  - Entity / Relationship Name
    - Must be unique within the namespace
  - Entity Element Name
    - Must be unique within the entity.
parameters:
  - name: context_id
    in: path
    description: |
      The unique identifier for the datastore.
    required: true
    schema:
      type: string
requestBody:
  content:
    application/json:
      schema:
        $ref: "../../schemas/DataModelRequest.yaml"
      examples:
        example-1:
          summary: Example of a Data Model
          value:
            meta_schema_version: "relational-20250501"
            entities:
              - model_name: "ns1.Product"
                name: "Product"
                type: "TABLE"
                consumption_type: "dataset"
                description: ""
                active: true
                index: 1
                tags: []
                properties:
                  schema: "Production"
                custom_properties: {}
                entity_elements:
                  - model_name: "ns1.Product.ProductID"
                    name: "ProductID"
                    type: "COLUMN"
                    consumption_type: "field"
                    description: ""
                    active: true
                    index: 1
                    data_type: "int"
                    default_value: ""
                    precision: 10
                    scale: 0
                    is_required: false
                    tags: []
                    properties: {}
                    custom_properties: {}
                  - model_name: "ns1.Product.Name"
                    name: "Name"
                    type: "COLUMN"
                    consumption_type: "field"
                    index: 2
                    data_type: "Name"
                    default_value: ""
                    max_length: 50
                    is_required: false
                    properties: {}
                    custom_properties: {}
                  - model_name: "ns1.Product.Model"
                    name: "Model"
                    type: "COLUMN"
                    consumption_type: "field"
                    description: "Stores the model GUID that is a foreign key to another table"
                    index: 3
                    data_type: "nvarchar"
                    default_value: ""
                    max_length: 24
                    is_required: false
                    properties: {}
                    custom_properties: {}
                  - model_name: "ns1.Product.Description"
                    name: "Description"
                    type: "COLUMN"
                    consumption_type: "field"
                    index: 4
                    data_type: "nvarchar"
                    default_value: ""
                    max_length: 400
                    is_required: false
                    properties: {}
                    custom_properties: {}
                  - model_name: "ns1.Product.Makeflag"
                    name: "Makeflag"
                    type: "COLUMN"
                    consumption_type: "field"
                    index: 5
                    data_type: "Flag"
                    default_value: ""
                    precision: 1
                    scale: 0
                    is_required: false
                    properties: {}
                    custom_properties: {}
                  - model_name: "ns1.Product.PK_ProductID"
                    name: "PK_ProductID"
                    type: "PRIMARY-KEY"
                    consumption_type: "key"
                  - model_name: "ns1.Product.IDX_Name_Makeflag"
                    name: "IDX_Name_MakeFlag"
                    type: "INDEX"
                    consumption_type: "index"
                    properties:
                      is_unique: true
                  - model_name: "ns1.Product.FK_Model"
                    name: "FK_Model"
                    type: "FOREIGN-KEY"
                    consumption_type: "constraint"
                    properties:
                      on_update: "no_action"
                      on_delete: "no_action"
              - model_name: "ns1.ProductView"
                name: "ProductView"
                type: "VIEW"
                consumption_type: "dataset"
                properties:
                  schema: "Production"
                entity_elements:
                  - model_name: "ns1.ProductView.ProductID"
                    name: "ProductID"
                    type: "COLUMN"
                    consumption_type: "field"
                    index: 1
                    data_type: "int"
                    default_value: ""
                    precision: 10
                    scale: 0
                    is_required: false
                    properties: {}
                  - model_name: "ns1.ProductView.Name"
                    name: "Name"
                    type: "COLUMN"
                    consumption_type: "field"
                    index: 2
                    data_type: "Name"
                    default_value: ""
                    max_length: 50
                    is_required: false
                    properties: {}
            relationships:
              - name: "PK_Production_Product_ProductID"
                type: "PK_REF"
                description: ""
                source: "#element/ns1.Product.PK_ProductID"
                target: "#element/ns1.Product.ProductID"
              - name: "IDX_Production_Product_Name_MakeFlag_Name"
                type: "IDX_REF"
                description: ""
                index: 10
                source: "#element/ns1.Product.IDX_Name_Makeflag"
                target: "#element/ns1.Product.Name"
              - name: "IDX_Production_Product_Name_MakeFlag_MakeFlag"
                type: "IDX_REF"
                description: ""
                index: 20
                source: "#element/ns1.Product.IDX_Name_Makeflag"
                target: "#element/ns1.Product.Makeflag"
              - name: "FK_Production_Product_Model"
                type: "FK_REF_SOURCE"
                description: ""
                source: "#element/ns1.Product.FK_Model"
                target: "#element/ns1.Product.Model"
              - name: "FK_Production_Product_Model"
                type: "FK_REF_TARGET"
                description: ""
                source: "#element/ns1.Product.FK_Model"
                target: "#element/ns1.ProductModel.ModelID"
responses:
  "200":
    description: |
      Created or Updated.
    content:
      application/json:
        schema:
          $ref: "../../schemas/DataModelResponse.yaml"
        examples:
          example-1:
            summary: Example of a Data Model Response
            value:
              new_version: "20250201-002"
  "400":
    description: Bad Request.
    content:
      application/json:
        schema:
          $ref: "../../schemas/CatalogError.yaml"
          examples:
            example-1:
              summary: Example of a Bad Request
              value:
                trace_id: "4567-987653-af456"
                type: "https://catalog.syniti.com/errors/unsupported-meta-schema-version"
                title: "Invalid Meta Schema Version"
                detail: "The provided meta-version in the request is not valid. Use 'relational-20250204'."
  "401":
    $ref: "../../schemas/UnAuthorizedError.yaml"
  "403":
    $ref: "../../schemas/ForbiddenError.yaml"
