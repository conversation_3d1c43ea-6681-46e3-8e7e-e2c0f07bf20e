tags:
  - Entity
summary: Retrieve a List of Entities
operationId: findEntities
description: |
  The API endpoint allows you to fetch the data model entities.
  The API supports pagination and filtering options to manage large results effectively.
parameters:
  - name: context_id
    description: |
      Filter entities by the context like a datastore or subject areas identifier.
    in: path
    schema:
      type: string
  - name: type
    in: query
    description: |
      Filter by the type of entity.
      For example `TABLE`.
      Please review the meta-meta information for the data model for the valid types.
    schema:
      type: array
      items:
        type: string
  - name: consumption_type
    in: query
    description: |
      Filter by the consumption_type of entity.
      For example `table`.
    schema:
      type: array
      items:
        type: string
  - name: tags
    in: query
    description: |
      Filter by the tags of entity.
    schema:
      type: array
      items:
        type: string
  - name: name
    description: |
      Filter entities by the matching exact name.
    in: query
    schema:
      type: string
  - name: name_text
    description: |
      Filter entities using contains text search on the name of the entity.
    in: query
    schema:
      type: string
  - name: description_text
    description: |
      Filter entities using contains text search on the description of the entity.
    in: query
    schema:
      type: string
  - name: first
    in: query
    description: Number of requested results for paging
    schema:
      type: integer
  - name: offset
    in: query
    description: Encoded value used to indicate where in the result set to begin
    schema:
      type: string
  - name: sort
    in: query
    description: A base64 encoded sort value
    schema:
      type: string
  - name: filter
    in: query
    description: A base64 encoded filter value
    schema:
      type: string
responses:
  "200":
    description: Entities list.
    content:
      application/json:
        schema:
          type: object
          properties:
            next_token:
              type: string
              description: |
                The token to fetch the next set of results.
            items:
              description: |
                The list of entities.
                The list is paginated and the next_token can be used to fetch the next set of results.
              type: array
              items:
                $ref: "../../schemas/Entity.yaml"
  "400":
    description: Bad Request
    content:
      application/json:
        schema:
          $ref: "../../schemas/CatalogError.yaml"
  "401":
    $ref: "../../schemas/UnAuthorizedError.yaml"
  "403":
    $ref: "../../schemas/ForbiddenError.yaml"
