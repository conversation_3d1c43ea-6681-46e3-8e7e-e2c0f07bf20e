tags:
  - Import DataModel
summary: Import Data Model
operationId: importDataModel
description: |
  The API enables batch import of data models, accommodating scenarios where the MetaScanner scans technical data models with thousands of tables and columns.


  **How It Works**

  1. Logical Transaction Identifier:  
    The entire import process must be handled as a single transaction to ensure the local data model is correctly updated with the scanned data model.
    A `scan_id` is generated as part of a SystemScan entry in the Knowledge Graph.
    This `scan_id` ensures that all batches are imported as part of the same transaction.
  2. Batch Import:  
    Use the import API with the `scan_id` to import all batches of the data model.
  3. Mark Import as Completed:  
    Once all batches are imported, invoke the API to mark the transaction as complete.

  **Key Points for Consumers**

  - Idempotent: The import process is idempotent, ensuring that re-importing the same batch does not create duplicate data.
  - `scan_id`: The `scan_id` is critical for maintaining transactional integrity. It must be provided consistently for all batch imports.
  - Single Import: Only one batch import is allowed at a time for a specific data model context.
parameters:
  - name: context_id
    description: |
      The identifier of the context for which the data model needs to be imported.
      The context can be a Datastore or a Subject Area. This is a mandatory parameter.
    in: path
    schema:
      type: string
    required: true
  - name: scan_id
    in: path
    description: |
      The unique identifier for the scan.
      This identifier is used to track the import process and ensure that all batches are part of the same transaction.
      The `scan_id` is generated as part of a SystemScan entry in the Knowledge Graph.
      It is critical for maintaining transactional integrity and must be provided consistently for all batch imports.
    required: true
    schema:
      type: string
requestBody:
  content:
    application/json:
      schema:
        $ref: "../../schemas/ImportDataModelRequest.yaml"
      examples:
        example-1:
          summary: Example of importing a data model
          value:
            data_model:
              - model_element_type: "entity"
                model_name: "ns1.Product"
                name: "Product"
                type: "TABLE"
                consumption_type: "dataset"
                active: true
                properties:
                  schema: "Production"
              - model_element_type: "element"
                entity: "ns1.Product"
                model_name: "ns1.Product.Shipmentid"
                name: "ShipmentId"
                type: "COLUMN"
                consumption_type: "field"
                active: true
                index: 1
                data_type: "int"
                default_value: ""
                nulls_allowed: false
              - model_element_type: "element"
                entity: "ns1.Product"
                model_name: "ns1.Production.Product.PK_Shipmentid"
                name: "PK_ShipmentId"
                type: "PRIMARY-KEY"
                consumption_type: "key"
              - model_element_type: "relation"
                type: "PK_REF"
                description: ""
                source: "#element/ns1.Product.PK_Shipmentid"
                target: "#element/ns1.Product.Shipmentid"
              - model_element_type: "entity"
                operation: "delete"
                name: "Employee"
responses:
  "200":
    description: |
      Sucessfully imported.
    content:
      application/json:
        schema:
          $ref: "../../schemas/ImportDataModelResponse.yaml"
        examples:
          example-1:
            summary: Example of a Data Model Response
            value:
              new_model_version: "20250201-002"
  "400":
    description: Bad Request.
    content:
      application/json:
        schema:
          $ref: "../../schemas/CatalogError.yaml"
          examples:
            example-1:
              summary: Example of a Bad Request
              value:
                request_id: "4567-987653-af456"
                errors:
                  - code: "UNSUPPORTED_META_VERSION"
                    message: "The provided meta-version in the request is not valid. Use 'relational-20250204'."
  "401":
    $ref: "../../schemas/UnAuthorizedError.yaml"
  "403":
    $ref: "../../schemas/ForbiddenError.yaml"
