<!DOCTYPE html>
<html>

<head>
  <meta charset="utf8" />
  <title>Catalog Service API</title>
  <!-- needed for adaptive design -->
  <meta name="viewport" content="width=device-width, initial-scale=1">
  <style>
    body {
      padding: 0;
      margin: 0;
    }
  </style>
  <script src="https://cdn.redocly.com/redoc/v2.5.0/bundles/redoc.standalone.js"></script><style data-styled="true" data-styled-version="6.1.18">.iRoSfe{width:calc(100% - 40%);padding:0 40px;}/*!sc*/
@media print,screen and (max-width: 75rem){.iRoSfe{width:100%;padding:40px 40px;}}/*!sc*/
.ciCSbi{width:calc(100% - 40%);padding:0 40px;}/*!sc*/
@media print,screen and (max-width: 75rem){.ciCSbi{width:100%;padding:0px 40px;}}/*!sc*/
data-styled.g4[id="sc-gwsNht"]{content:"iRoSfe,ciCSbi,"}/*!sc*/
.kDuBQd{padding:40px 0;}/*!sc*/
.kDuBQd:last-child{min-height:calc(100vh + 1px);}/*!sc*/
.kDuBQd>.kDuBQd:last-child{min-height:initial;}/*!sc*/
@media print,screen and (max-width: 75rem){.kDuBQd{padding:0;}}/*!sc*/
.dRVblm{padding:40px 0;position:relative;}/*!sc*/
.dRVblm:last-child{min-height:calc(100vh + 1px);}/*!sc*/
.dRVblm>.dRVblm:last-child{min-height:initial;}/*!sc*/
@media print,screen and (max-width: 75rem){.dRVblm{padding:0;}}/*!sc*/
.dRVblm:not(:last-of-type):after{position:absolute;bottom:0;width:100%;display:block;content:'';border-bottom:1px solid rgba(0, 0, 0, 0.2);}/*!sc*/
data-styled.g5[id="sc-dYOLZc"]{content:"kDuBQd,dRVblm,"}/*!sc*/
.kCcXaS{width:40%;color:#ffffff;background-color:#263238;padding:0 40px;}/*!sc*/
@media print,screen and (max-width: 75rem){.kCcXaS{width:100%;padding:40px 40px;}}/*!sc*/
data-styled.g6[id="sc-jMpmlX"]{content:"kCcXaS,"}/*!sc*/
.jpPJrn{background-color:#263238;}/*!sc*/
data-styled.g7[id="sc-hoLldG"]{content:"jpPJrn,"}/*!sc*/
.tMeMv{display:flex;width:100%;padding:0;}/*!sc*/
@media print,screen and (max-width: 75rem){.tMeMv{flex-direction:column;}}/*!sc*/
data-styled.g8[id="sc-jZhnRx"]{content:"tMeMv,"}/*!sc*/
.dKykVD{font-family:Montserrat,sans-serif;font-weight:400;font-size:1.85714em;line-height:1.6em;color:#333333;}/*!sc*/
data-styled.g9[id="sc-hBDmJg"]{content:"dKykVD,"}/*!sc*/
.jkwlQD{font-family:Montserrat,sans-serif;font-weight:400;font-size:1.57143em;line-height:1.6em;color:#333333;margin:0 0 20px;}/*!sc*/
data-styled.g10[id="sc-kThouk"]{content:"jkwlQD,"}/*!sc*/
.fdkIVZ{color:#ffffff;}/*!sc*/
data-styled.g12[id="sc-llIIlC"]{content:"fdkIVZ,"}/*!sc*/
.eUkINt{border-bottom:1px solid rgba(38, 50, 56, 0.3);margin:1em 0 1em 0;color:rgba(38, 50, 56, 0.5);font-weight:normal;text-transform:uppercase;font-size:0.929em;line-height:20px;}/*!sc*/
data-styled.g13[id="sc-elFkmj"]{content:"eUkINt,"}/*!sc*/
.jOWzfS{cursor:pointer;margin-left:-20px;padding:0;line-height:1;width:20px;display:inline-block;outline:0;}/*!sc*/
.jOWzfS:before{content:'';width:15px;height:15px;background-size:contain;background-image:url('data:image/svg+xml;base64,PHN2ZyB4bWxucz0iaHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmciIHZlcnNpb249IjEuMSIgeD0iMCIgeT0iMCIgd2lkdGg9IjUxMiIgaGVpZ2h0PSI1MTIiIHZpZXdCb3g9IjAgMCA1MTIgNTEyIiBlbmFibGUtYmFja2dyb3VuZD0ibmV3IDAgMCA1MTIgNTEyIiB4bWw6c3BhY2U9InByZXNlcnZlIj48cGF0aCBmaWxsPSIjMDEwMTAxIiBkPSJNNDU5LjcgMjMzLjRsLTkwLjUgOTAuNWMtNTAgNTAtMTMxIDUwLTE4MSAwIC03LjktNy44LTE0LTE2LjctMTkuNC0yNS44bDQyLjEtNDIuMWMyLTIgNC41LTMuMiA2LjgtNC41IDIuOSA5LjkgOCAxOS4zIDE1LjggMjcuMiAyNSAyNSA2NS42IDI0LjkgOTAuNSAwbDkwLjUtOTAuNWMyNS0yNSAyNS02NS42IDAtOTAuNSAtMjQuOS0yNS02NS41LTI1LTkwLjUgMGwtMzIuMiAzMi4yYy0yNi4xLTEwLjItNTQuMi0xMi45LTgxLjYtOC45bDY4LjYtNjguNmM1MC01MCAxMzEtNTAgMTgxIDBDNTA5LjYgMTAyLjMgNTA5LjYgMTgzLjQgNDU5LjcgMjMzLjR6TTIyMC4zIDM4Mi4ybC0zMi4yIDMyLjJjLTI1IDI0LjktNjUuNiAyNC45LTkwLjUgMCAtMjUtMjUtMjUtNjUuNiAwLTkwLjVsOTAuNS05MC41YzI1LTI1IDY1LjUtMjUgOTAuNSAwIDcuOCA3LjggMTIuOSAxNy4yIDE1LjggMjcuMSAyLjQtMS40IDQuOC0yLjUgNi44LTQuNWw0Mi4xLTQyYy01LjQtOS4yLTExLjYtMTgtMTkuNC0yNS44IC01MC01MC0xMzEtNTAtMTgxIDBsLTkwLjUgOTAuNWMtNTAgNTAtNTAgMTMxIDAgMTgxIDUwIDUwIDEzMSA1MCAxODEgMGw2OC42LTY4LjZDMjc0LjYgMzk1LjEgMjQ2LjQgMzkyLjMgMjIwLjMgMzgyLjJ6Ii8+PC9zdmc+Cg==');opacity:0.5;visibility:hidden;display:inline-block;vertical-align:middle;}/*!sc*/
h1:hover>.jOWzfS::before,h2:hover>.jOWzfS::before,.jOWzfS:hover::before{visibility:visible;}/*!sc*/
data-styled.g14[id="sc-kieALA"]{content:"jOWzfS,"}/*!sc*/
.bnalXq{height:18px;width:18px;min-width:18px;vertical-align:middle;float:right;transition:transform 0.2s ease-out;transform:rotateZ(-90deg);}/*!sc*/
.gsIrbT{height:1.3em;width:1.3em;min-width:1.3em;vertical-align:middle;transition:transform 0.2s ease-out;transform:rotateZ(-90deg);}/*!sc*/
.eVNtOe{height:18px;width:18px;min-width:18px;vertical-align:middle;transition:transform 0.2s ease-out;transform:rotateZ(-90deg);}/*!sc*/
.dpIKEE{height:1.5em;width:1.5em;min-width:1.5em;vertical-align:middle;float:left;transition:transform 0.2s ease-out;transform:rotateZ(-90deg);}/*!sc*/
.dpIKEE polygon{fill:#1d8127;}/*!sc*/
.jsRtRU{height:1.5em;width:1.5em;min-width:1.5em;vertical-align:middle;float:left;transition:transform 0.2s ease-out;transform:rotateZ(-90deg);}/*!sc*/
.jsRtRU polygon{fill:#d41f1c;}/*!sc*/
.NOyYc{height:20px;width:20px;min-width:20px;vertical-align:middle;float:right;transition:transform 0.2s ease-out;transform:rotateZ(0);}/*!sc*/
.NOyYc polygon{fill:white;}/*!sc*/
data-styled.g15[id="sc-dibcMh"]{content:"bnalXq,gsIrbT,eVNtOe,dpIKEE,jsRtRU,NOyYc,"}/*!sc*/
.cDFxGr{border-left:1px solid #7c7cbb;box-sizing:border-box;position:relative;padding:10px 10px 10px 0;}/*!sc*/
@media screen and (max-width: 50rem){.cDFxGr{display:block;overflow:hidden;}}/*!sc*/
tr:first-of-type>.cDFxGr,tr.last>.cDFxGr{border-left-width:0;background-position:top left;background-repeat:no-repeat;background-size:1px 100%;}/*!sc*/
tr:first-of-type>.cDFxGr{background-image:linear-gradient(
      to bottom,
      transparent 0%,
      transparent 22px,
      #7c7cbb 22px,
      #7c7cbb 100%
    );}/*!sc*/
tr.last>.cDFxGr{background-image:linear-gradient(
      to bottom,
      #7c7cbb 0%,
      #7c7cbb 22px,
      transparent 22px,
      transparent 100%
    );}/*!sc*/
tr.last+tr>.cDFxGr{border-left-color:transparent;}/*!sc*/
tr.last:first-child>.cDFxGr{background:none;border-left-color:transparent;}/*!sc*/
data-styled.g18[id="sc-kHNKno"]{content:"cDFxGr,"}/*!sc*/
.kNAbAx{vertical-align:top;line-height:20px;white-space:nowrap;font-size:13px;font-family:Courier,monospace;}/*!sc*/
.kNAbAx.deprecated{text-decoration:line-through;color:#707070;}/*!sc*/
data-styled.g20[id="sc-frmfij"]{content:"kNAbAx,"}/*!sc*/
.dzPbxx{border-bottom:1px solid #9fb4be;padding:10px 0;width:75%;box-sizing:border-box;}/*!sc*/
tr.expanded .dzPbxx{border-bottom:none;}/*!sc*/
@media screen and (max-width: 50rem){.dzPbxx{padding:0 20px;border-bottom:none;border-left:1px solid #7c7cbb;}tr.last>.dzPbxx{border-left:none;}}/*!sc*/
data-styled.g21[id="sc-groBii"]{content:"dzPbxx,"}/*!sc*/
.lhCIDE{color:#7c7cbb;font-family:Courier,monospace;margin-right:10px;}/*!sc*/
.lhCIDE::before{content:'';display:inline-block;vertical-align:middle;width:10px;height:1px;background:#7c7cbb;}/*!sc*/
.lhCIDE::after{content:'';display:inline-block;vertical-align:middle;width:1px;background:#7c7cbb;height:7px;}/*!sc*/
data-styled.g22[id="sc-hLyRwt"]{content:"lhCIDE,"}/*!sc*/
.cGiuNU{border-collapse:separate;border-radius:3px;font-size:14px;border-spacing:0;width:100%;}/*!sc*/
.cGiuNU >tr{vertical-align:middle;}/*!sc*/
@media screen and (max-width: 50rem){.cGiuNU{display:block;}.cGiuNU >tr,.cGiuNU >tbody>tr{display:block;}}/*!sc*/
@media screen and (max-width: 50rem) and (-ms-high-contrast:none){.cGiuNU td{float:left;width:100%;}}/*!sc*/
.cGiuNU .sc-iLBnws,.cGiuNU .sc-iLBnws .sc-iLBnws .sc-iLBnws,.cGiuNU .sc-iLBnws .sc-iLBnws .sc-iLBnws .sc-iLBnws .sc-iLBnws{margin:1em;margin-right:0;background:#fafafa;}/*!sc*/
.cGiuNU .sc-iLBnws .sc-iLBnws,.cGiuNU .sc-iLBnws .sc-iLBnws .sc-iLBnws .sc-iLBnws,.cGiuNU .sc-iLBnws .sc-iLBnws .sc-iLBnws .sc-iLBnws .sc-iLBnws .sc-iLBnws{background:#ffffff;}/*!sc*/
data-styled.g24[id="sc-eGjrzz"]{content:"cGiuNU,"}/*!sc*/
.dFpYBv >ul{list-style:none;padding:0;margin:0;margin:0 -5px;}/*!sc*/
.dFpYBv >ul >li{padding:5px 10px;display:inline-block;background-color:#11171a;border-bottom:1px solid rgba(0, 0, 0, 0.5);cursor:pointer;text-align:center;outline:none;color:#ccc;margin:0 5px 5px 5px;border:1px solid #07090b;border-radius:5px;min-width:60px;font-size:0.9em;font-weight:bold;}/*!sc*/
.dFpYBv >ul >li.react-tabs__tab--selected{color:#333333;background:#ffffff;}/*!sc*/
.dFpYBv >ul >li.react-tabs__tab--selected:focus{outline:auto;}/*!sc*/
.dFpYBv >ul >li:only-child{flex:none;min-width:100px;}/*!sc*/
.dFpYBv >ul >li.tab-success{color:#1d8127;}/*!sc*/
.dFpYBv >ul >li.tab-redirect{color:#ffa500;}/*!sc*/
.dFpYBv >ul >li.tab-info{color:#87ceeb;}/*!sc*/
.dFpYBv >ul >li.tab-error{color:#d41f1c;}/*!sc*/
.dFpYBv >.react-tabs__tab-panel{background:#11171a;}/*!sc*/
.dFpYBv >.react-tabs__tab-panel>div,.dFpYBv >.react-tabs__tab-panel>pre{padding:20px;margin:0;}/*!sc*/
.dFpYBv >.react-tabs__tab-panel>div>pre{padding:0;}/*!sc*/
data-styled.g30[id="sc-cTIdZS"]{content:"dFpYBv,"}/*!sc*/
.kOqeZB code[class*='language-'],.kOqeZB pre[class*='language-']{text-shadow:0 -0.1em 0.2em black;text-align:left;white-space:pre;word-spacing:normal;word-break:normal;word-wrap:normal;line-height:1.5;-moz-tab-size:4;-o-tab-size:4;tab-size:4;-webkit-hyphens:none;-moz-hyphens:none;-ms-hyphens:none;hyphens:none;}/*!sc*/
@media print{.kOqeZB code[class*='language-'],.kOqeZB pre[class*='language-']{text-shadow:none;}}/*!sc*/
.kOqeZB pre[class*='language-']{padding:1em;margin:0.5em 0;overflow:auto;}/*!sc*/
.kOqeZB .token.comment,.kOqeZB .token.prolog,.kOqeZB .token.doctype,.kOqeZB .token.cdata{color:hsl(30, 20%, 50%);}/*!sc*/
.kOqeZB .token.punctuation{opacity:0.7;}/*!sc*/
.kOqeZB .namespace{opacity:0.7;}/*!sc*/
.kOqeZB .token.property,.kOqeZB .token.tag,.kOqeZB .token.number,.kOqeZB .token.constant,.kOqeZB .token.symbol{color:#4a8bb3;}/*!sc*/
.kOqeZB .token.boolean{color:#e64441;}/*!sc*/
.kOqeZB .token.selector,.kOqeZB .token.attr-name,.kOqeZB .token.string,.kOqeZB .token.char,.kOqeZB .token.builtin,.kOqeZB .token.inserted{color:#a0fbaa;}/*!sc*/
.kOqeZB .token.selector+a,.kOqeZB .token.attr-name+a,.kOqeZB .token.string+a,.kOqeZB .token.char+a,.kOqeZB .token.builtin+a,.kOqeZB .token.inserted+a,.kOqeZB .token.selector+a:visited,.kOqeZB .token.attr-name+a:visited,.kOqeZB .token.string+a:visited,.kOqeZB .token.char+a:visited,.kOqeZB .token.builtin+a:visited,.kOqeZB .token.inserted+a:visited{color:#4ed2ba;text-decoration:underline;}/*!sc*/
.kOqeZB .token.property.string{color:white;}/*!sc*/
.kOqeZB .token.operator,.kOqeZB .token.entity,.kOqeZB .token.url,.kOqeZB .token.variable{color:hsl(40, 90%, 60%);}/*!sc*/
.kOqeZB .token.atrule,.kOqeZB .token.attr-value,.kOqeZB .token.keyword{color:hsl(350, 40%, 70%);}/*!sc*/
.kOqeZB .token.regex,.kOqeZB .token.important{color:#e90;}/*!sc*/
.kOqeZB .token.important,.kOqeZB .token.bold{font-weight:bold;}/*!sc*/
.kOqeZB .token.italic{font-style:italic;}/*!sc*/
.kOqeZB .token.entity{cursor:help;}/*!sc*/
.kOqeZB .token.deleted{color:red;}/*!sc*/
data-styled.g32[id="sc-faJlkc"]{content:"kOqeZB,"}/*!sc*/
.itwxyW{opacity:0.7;transition:opacity 0.3s ease;text-align:right;}/*!sc*/
.itwxyW:focus-within{opacity:1;}/*!sc*/
.itwxyW >button{background-color:transparent;border:0;color:inherit;padding:2px 10px;font-family:Roboto,sans-serif;font-size:14px;line-height:1.5em;cursor:pointer;outline:0;}/*!sc*/
.itwxyW >button :hover,.itwxyW >button :focus{background:rgba(255, 255, 255, 0.1);}/*!sc*/
data-styled.g33[id="sc-VILhF"]{content:"itwxyW,"}/*!sc*/
.dkdAtf{position:relative;}/*!sc*/
data-styled.g37[id="sc-epGxBs"]{content:"dkdAtf,"}/*!sc*/
.iFdOsg{margin-left:10px;text-transform:none;font-size:0.929em;color:black;}/*!sc*/
data-styled.g41[id="sc-edaYAx"]{content:"iFdOsg,"}/*!sc*/
.kEOvim{font-family:Roboto,sans-serif;font-weight:400;line-height:1.5em;}/*!sc*/
.kEOvim p:last-child{margin-bottom:0;}/*!sc*/
.kEOvim h1{font-family:Montserrat,sans-serif;font-weight:400;font-size:1.85714em;line-height:1.6em;color:#32329f;margin-top:0;}/*!sc*/
.kEOvim h2{font-family:Montserrat,sans-serif;font-weight:400;font-size:1.57143em;line-height:1.6em;color:#333333;}/*!sc*/
.kEOvim code{color:#e53935;background-color:rgba(38, 50, 56, 0.05);font-family:Courier,monospace;border-radius:2px;border:1px solid rgba(38, 50, 56, 0.1);padding:0 5px;font-size:13px;font-weight:400;word-break:break-word;}/*!sc*/
.kEOvim pre{font-family:Courier,monospace;white-space:pre;background-color:#11171a;color:white;padding:20px;overflow-x:auto;line-height:normal;border-radius:0;border:1px solid rgba(38, 50, 56, 0.1);}/*!sc*/
.kEOvim pre code{background-color:transparent;color:white;padding:0;}/*!sc*/
.kEOvim pre code:before,.kEOvim pre code:after{content:none;}/*!sc*/
.kEOvim blockquote{margin:0;margin-bottom:1em;padding:0 15px;color:#777;border-left:4px solid #ddd;}/*!sc*/
.kEOvim img{max-width:100%;box-sizing:content-box;}/*!sc*/
.kEOvim ul,.kEOvim ol{padding-left:2em;margin:0;margin-bottom:1em;}/*!sc*/
.kEOvim ul ul,.kEOvim ol ul,.kEOvim ul ol,.kEOvim ol ol{margin-bottom:0;margin-top:0;}/*!sc*/
.kEOvim table{display:block;width:100%;overflow:auto;word-break:normal;word-break:keep-all;border-collapse:collapse;border-spacing:0;margin-top:1.5em;margin-bottom:1.5em;}/*!sc*/
.kEOvim table tr{background-color:#fff;border-top:1px solid #ccc;}/*!sc*/
.kEOvim table tr:nth-child(2n){background-color:#fafafa;}/*!sc*/
.kEOvim table th,.kEOvim table td{padding:6px 13px;border:1px solid #ddd;}/*!sc*/
.kEOvim table th{text-align:left;font-weight:bold;}/*!sc*/
.kEOvim .share-link{cursor:pointer;margin-left:-20px;padding:0;line-height:1;width:20px;display:inline-block;outline:0;}/*!sc*/
.kEOvim .share-link:before{content:'';width:15px;height:15px;background-size:contain;background-image:url('data:image/svg+xml;base64,PHN2ZyB4bWxucz0iaHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmciIHZlcnNpb249IjEuMSIgeD0iMCIgeT0iMCIgd2lkdGg9IjUxMiIgaGVpZ2h0PSI1MTIiIHZpZXdCb3g9IjAgMCA1MTIgNTEyIiBlbmFibGUtYmFja2dyb3VuZD0ibmV3IDAgMCA1MTIgNTEyIiB4bWw6c3BhY2U9InByZXNlcnZlIj48cGF0aCBmaWxsPSIjMDEwMTAxIiBkPSJNNDU5LjcgMjMzLjRsLTkwLjUgOTAuNWMtNTAgNTAtMTMxIDUwLTE4MSAwIC03LjktNy44LTE0LTE2LjctMTkuNC0yNS44bDQyLjEtNDIuMWMyLTIgNC41LTMuMiA2LjgtNC41IDIuOSA5LjkgOCAxOS4zIDE1LjggMjcuMiAyNSAyNSA2NS42IDI0LjkgOTAuNSAwbDkwLjUtOTAuNWMyNS0yNSAyNS02NS42IDAtOTAuNSAtMjQuOS0yNS02NS41LTI1LTkwLjUgMGwtMzIuMiAzMi4yYy0yNi4xLTEwLjItNTQuMi0xMi45LTgxLjYtOC45bDY4LjYtNjguNmM1MC01MCAxMzEtNTAgMTgxIDBDNTA5LjYgMTAyLjMgNTA5LjYgMTgzLjQgNDU5LjcgMjMzLjR6TTIyMC4zIDM4Mi4ybC0zMi4yIDMyLjJjLTI1IDI0LjktNjUuNiAyNC45LTkwLjUgMCAtMjUtMjUtMjUtNjUuNiAwLTkwLjVsOTAuNS05MC41YzI1LTI1IDY1LjUtMjUgOTAuNSAwIDcuOCA3LjggMTIuOSAxNy4yIDE1LjggMjcuMSAyLjQtMS40IDQuOC0yLjUgNi44LTQuNWw0Mi4xLTQyYy01LjQtOS4yLTExLjYtMTgtMTkuNC0yNS44IC01MC01MC0xMzEtNTAtMTgxIDBsLTkwLjUgOTAuNWMtNTAgNTAtNTAgMTMxIDAgMTgxIDUwIDUwIDEzMSA1MCAxODEgMGw2OC42LTY4LjZDMjc0LjYgMzk1LjEgMjQ2LjQgMzkyLjMgMjIwLjMgMzgyLjJ6Ii8+PC9zdmc+Cg==');opacity:0.5;visibility:hidden;display:inline-block;vertical-align:middle;}/*!sc*/
.kEOvim h1:hover>.share-link::before,.kEOvim h2:hover>.share-link::before,.kEOvim .share-link:hover::before{visibility:visible;}/*!sc*/
.kEOvim a{text-decoration:auto;color:#32329f;}/*!sc*/
.kEOvim a:visited{color:#32329f;}/*!sc*/
.kEOvim a:hover{color:#6868cf;text-decoration:auto;}/*!sc*/
.csbUjU{font-family:Roboto,sans-serif;font-weight:400;line-height:1.5em;}/*!sc*/
.csbUjU p:last-child{margin-bottom:0;}/*!sc*/
.csbUjU p:first-child{margin-top:0;}/*!sc*/
.csbUjU p:last-child{margin-bottom:0;}/*!sc*/
.csbUjU h1{font-family:Montserrat,sans-serif;font-weight:400;font-size:1.85714em;line-height:1.6em;color:#32329f;margin-top:0;}/*!sc*/
.csbUjU h2{font-family:Montserrat,sans-serif;font-weight:400;font-size:1.57143em;line-height:1.6em;color:#333333;}/*!sc*/
.csbUjU code{color:#e53935;background-color:rgba(38, 50, 56, 0.05);font-family:Courier,monospace;border-radius:2px;border:1px solid rgba(38, 50, 56, 0.1);padding:0 5px;font-size:13px;font-weight:400;word-break:break-word;}/*!sc*/
.csbUjU pre{font-family:Courier,monospace;white-space:pre;background-color:#11171a;color:white;padding:20px;overflow-x:auto;line-height:normal;border-radius:0;border:1px solid rgba(38, 50, 56, 0.1);}/*!sc*/
.csbUjU pre code{background-color:transparent;color:white;padding:0;}/*!sc*/
.csbUjU pre code:before,.csbUjU pre code:after{content:none;}/*!sc*/
.csbUjU blockquote{margin:0;margin-bottom:1em;padding:0 15px;color:#777;border-left:4px solid #ddd;}/*!sc*/
.csbUjU img{max-width:100%;box-sizing:content-box;}/*!sc*/
.csbUjU ul,.csbUjU ol{padding-left:2em;margin:0;margin-bottom:1em;}/*!sc*/
.csbUjU ul ul,.csbUjU ol ul,.csbUjU ul ol,.csbUjU ol ol{margin-bottom:0;margin-top:0;}/*!sc*/
.csbUjU table{display:block;width:100%;overflow:auto;word-break:normal;word-break:keep-all;border-collapse:collapse;border-spacing:0;margin-top:1.5em;margin-bottom:1.5em;}/*!sc*/
.csbUjU table tr{background-color:#fff;border-top:1px solid #ccc;}/*!sc*/
.csbUjU table tr:nth-child(2n){background-color:#fafafa;}/*!sc*/
.csbUjU table th,.csbUjU table td{padding:6px 13px;border:1px solid #ddd;}/*!sc*/
.csbUjU table th{text-align:left;font-weight:bold;}/*!sc*/
.csbUjU .share-link{cursor:pointer;margin-left:-20px;padding:0;line-height:1;width:20px;display:inline-block;outline:0;}/*!sc*/
.csbUjU .share-link:before{content:'';width:15px;height:15px;background-size:contain;background-image:url('data:image/svg+xml;base64,PHN2ZyB4bWxucz0iaHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmciIHZlcnNpb249IjEuMSIgeD0iMCIgeT0iMCIgd2lkdGg9IjUxMiIgaGVpZ2h0PSI1MTIiIHZpZXdCb3g9IjAgMCA1MTIgNTEyIiBlbmFibGUtYmFja2dyb3VuZD0ibmV3IDAgMCA1MTIgNTEyIiB4bWw6c3BhY2U9InByZXNlcnZlIj48cGF0aCBmaWxsPSIjMDEwMTAxIiBkPSJNNDU5LjcgMjMzLjRsLTkwLjUgOTAuNWMtNTAgNTAtMTMxIDUwLTE4MSAwIC03LjktNy44LTE0LTE2LjctMTkuNC0yNS44bDQyLjEtNDIuMWMyLTIgNC41LTMuMiA2LjgtNC41IDIuOSA5LjkgOCAxOS4zIDE1LjggMjcuMiAyNSAyNSA2NS42IDI0LjkgOTAuNSAwbDkwLjUtOTAuNWMyNS0yNSAyNS02NS42IDAtOTAuNSAtMjQuOS0yNS02NS41LTI1LTkwLjUgMGwtMzIuMiAzMi4yYy0yNi4xLTEwLjItNTQuMi0xMi45LTgxLjYtOC45bDY4LjYtNjguNmM1MC01MCAxMzEtNTAgMTgxIDBDNTA5LjYgMTAyLjMgNTA5LjYgMTgzLjQgNDU5LjcgMjMzLjR6TTIyMC4zIDM4Mi4ybC0zMi4yIDMyLjJjLTI1IDI0LjktNjUuNiAyNC45LTkwLjUgMCAtMjUtMjUtMjUtNjUuNiAwLTkwLjVsOTAuNS05MC41YzI1LTI1IDY1LjUtMjUgOTAuNSAwIDcuOCA3LjggMTIuOSAxNy4yIDE1LjggMjcuMSAyLjQtMS40IDQuOC0yLjUgNi44LTQuNWw0Mi4xLTQyYy01LjQtOS4yLTExLjYtMTgtMTkuNC0yNS44IC01MC01MC0xMzEtNTAtMTgxIDBsLTkwLjUgOTAuNWMtNTAgNTAtNTAgMTMxIDAgMTgxIDUwIDUwIDEzMSA1MCAxODEgMGw2OC42LTY4LjZDMjc0LjYgMzk1LjEgMjQ2LjQgMzkyLjMgMjIwLjMgMzgyLjJ6Ii8+PC9zdmc+Cg==');opacity:0.5;visibility:hidden;display:inline-block;vertical-align:middle;}/*!sc*/
.csbUjU h1:hover>.share-link::before,.csbUjU h2:hover>.share-link::before,.csbUjU .share-link:hover::before{visibility:visible;}/*!sc*/
.csbUjU a{text-decoration:auto;color:#32329f;}/*!sc*/
.csbUjU a:visited{color:#32329f;}/*!sc*/
.csbUjU a:hover{color:#6868cf;text-decoration:auto;}/*!sc*/
.kkltVg{font-family:Roboto,sans-serif;font-weight:400;line-height:1.5em;}/*!sc*/
.kkltVg p:last-child{margin-bottom:0;}/*!sc*/
.kkltVg p:first-child{margin-top:0;}/*!sc*/
.kkltVg p:last-child{margin-bottom:0;}/*!sc*/
.kkltVg p{display:inline-block;}/*!sc*/
.kkltVg h1{font-family:Montserrat,sans-serif;font-weight:400;font-size:1.85714em;line-height:1.6em;color:#32329f;margin-top:0;}/*!sc*/
.kkltVg h2{font-family:Montserrat,sans-serif;font-weight:400;font-size:1.57143em;line-height:1.6em;color:#333333;}/*!sc*/
.kkltVg code{color:#e53935;background-color:rgba(38, 50, 56, 0.05);font-family:Courier,monospace;border-radius:2px;border:1px solid rgba(38, 50, 56, 0.1);padding:0 5px;font-size:13px;font-weight:400;word-break:break-word;}/*!sc*/
.kkltVg pre{font-family:Courier,monospace;white-space:pre;background-color:#11171a;color:white;padding:20px;overflow-x:auto;line-height:normal;border-radius:0;border:1px solid rgba(38, 50, 56, 0.1);}/*!sc*/
.kkltVg pre code{background-color:transparent;color:white;padding:0;}/*!sc*/
.kkltVg pre code:before,.kkltVg pre code:after{content:none;}/*!sc*/
.kkltVg blockquote{margin:0;margin-bottom:1em;padding:0 15px;color:#777;border-left:4px solid #ddd;}/*!sc*/
.kkltVg img{max-width:100%;box-sizing:content-box;}/*!sc*/
.kkltVg ul,.kkltVg ol{padding-left:2em;margin:0;margin-bottom:1em;}/*!sc*/
.kkltVg ul ul,.kkltVg ol ul,.kkltVg ul ol,.kkltVg ol ol{margin-bottom:0;margin-top:0;}/*!sc*/
.kkltVg table{display:block;width:100%;overflow:auto;word-break:normal;word-break:keep-all;border-collapse:collapse;border-spacing:0;margin-top:1.5em;margin-bottom:1.5em;}/*!sc*/
.kkltVg table tr{background-color:#fff;border-top:1px solid #ccc;}/*!sc*/
.kkltVg table tr:nth-child(2n){background-color:#fafafa;}/*!sc*/
.kkltVg table th,.kkltVg table td{padding:6px 13px;border:1px solid #ddd;}/*!sc*/
.kkltVg table th{text-align:left;font-weight:bold;}/*!sc*/
.kkltVg .share-link{cursor:pointer;margin-left:-20px;padding:0;line-height:1;width:20px;display:inline-block;outline:0;}/*!sc*/
.kkltVg .share-link:before{content:'';width:15px;height:15px;background-size:contain;background-image:url('data:image/svg+xml;base64,PHN2ZyB4bWxucz0iaHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmciIHZlcnNpb249IjEuMSIgeD0iMCIgeT0iMCIgd2lkdGg9IjUxMiIgaGVpZ2h0PSI1MTIiIHZpZXdCb3g9IjAgMCA1MTIgNTEyIiBlbmFibGUtYmFja2dyb3VuZD0ibmV3IDAgMCA1MTIgNTEyIiB4bWw6c3BhY2U9InByZXNlcnZlIj48cGF0aCBmaWxsPSIjMDEwMTAxIiBkPSJNNDU5LjcgMjMzLjRsLTkwLjUgOTAuNWMtNTAgNTAtMTMxIDUwLTE4MSAwIC03LjktNy44LTE0LTE2LjctMTkuNC0yNS44bDQyLjEtNDIuMWMyLTIgNC41LTMuMiA2LjgtNC41IDIuOSA5LjkgOCAxOS4zIDE1LjggMjcuMiAyNSAyNSA2NS42IDI0LjkgOTAuNSAwbDkwLjUtOTAuNWMyNS0yNSAyNS02NS42IDAtOTAuNSAtMjQuOS0yNS02NS41LTI1LTkwLjUgMGwtMzIuMiAzMi4yYy0yNi4xLTEwLjItNTQuMi0xMi45LTgxLjYtOC45bDY4LjYtNjguNmM1MC01MCAxMzEtNTAgMTgxIDBDNTA5LjYgMTAyLjMgNTA5LjYgMTgzLjQgNDU5LjcgMjMzLjR6TTIyMC4zIDM4Mi4ybC0zMi4yIDMyLjJjLTI1IDI0LjktNjUuNiAyNC45LTkwLjUgMCAtMjUtMjUtMjUtNjUuNiAwLTkwLjVsOTAuNS05MC41YzI1LTI1IDY1LjUtMjUgOTAuNSAwIDcuOCA3LjggMTIuOSAxNy4yIDE1LjggMjcuMSAyLjQtMS40IDQuOC0yLjUgNi44LTQuNWw0Mi4xLTQyYy01LjQtOS4yLTExLjYtMTgtMTkuNC0yNS44IC01MC01MC0xMzEtNTAtMTgxIDBsLTkwLjUgOTAuNWMtNTAgNTAtNTAgMTMxIDAgMTgxIDUwIDUwIDEzMSA1MCAxODEgMGw2OC42LTY4LjZDMjc0LjYgMzk1LjEgMjQ2LjQgMzkyLjMgMjIwLjMgMzgyLjJ6Ii8+PC9zdmc+Cg==');opacity:0.5;visibility:hidden;display:inline-block;vertical-align:middle;}/*!sc*/
.kkltVg h1:hover>.share-link::before,.kkltVg h2:hover>.share-link::before,.kkltVg .share-link:hover::before{visibility:visible;}/*!sc*/
.kkltVg a{text-decoration:auto;color:#32329f;}/*!sc*/
.kkltVg a:visited{color:#32329f;}/*!sc*/
.kkltVg a:hover{color:#6868cf;text-decoration:auto;}/*!sc*/
data-styled.g42[id="sc-fdduAw"]{content:"kEOvim,csbUjU,kkltVg,"}/*!sc*/
.fbuMAW{display:inline;}/*!sc*/
data-styled.g43[id="sc-eINXvP"]{content:"fbuMAW,"}/*!sc*/
.indwFD{position:relative;}/*!sc*/
data-styled.g44[id="sc-fIQtvO"]{content:"indwFD,"}/*!sc*/
.iAOzgg:hover>.sc-VILhF{opacity:1;}/*!sc*/
data-styled.g49[id="sc-dRHutB"]{content:"iAOzgg,"}/*!sc*/
.eudsMA{font-family:Courier,monospace;font-size:13px;white-space:pre;contain:content;overflow-x:auto;}/*!sc*/
.eudsMA .redoc-json code>.collapser{display:none;pointer-events:none;}/*!sc*/
.eudsMA .callback-function{color:gray;}/*!sc*/
.eudsMA .collapser:after{content:'-';cursor:pointer;}/*!sc*/
.eudsMA .collapsed>.collapser:after{content:'+';cursor:pointer;}/*!sc*/
.eudsMA .ellipsis:after{content:' … ';}/*!sc*/
.eudsMA .collapsible{margin-left:2em;}/*!sc*/
.eudsMA .hoverable{padding-top:1px;padding-bottom:1px;padding-left:2px;padding-right:2px;border-radius:2px;}/*!sc*/
.eudsMA .hovered{background-color:rgba(235, 238, 249, 1);}/*!sc*/
.eudsMA .collapser{background-color:transparent;border:0;color:#fff;font-family:Courier,monospace;font-size:13px;padding-right:6px;padding-left:6px;padding-top:0;padding-bottom:0;display:flex;align-items:center;justify-content:center;width:15px;height:15px;position:absolute;top:4px;left:-1.5em;cursor:default;user-select:none;-webkit-user-select:none;padding:2px;}/*!sc*/
.eudsMA .collapser:focus{outline-color:#fff;outline-style:dotted;outline-width:1px;}/*!sc*/
.eudsMA ul{list-style-type:none;padding:0px;margin:0px 0px 0px 26px;}/*!sc*/
.eudsMA li{position:relative;display:block;}/*!sc*/
.eudsMA .hoverable{display:inline-block;}/*!sc*/
.eudsMA .selected{outline-style:solid;outline-width:1px;outline-style:dotted;}/*!sc*/
.eudsMA .collapsed>.collapsible{display:none;}/*!sc*/
.eudsMA .ellipsis{display:none;}/*!sc*/
.eudsMA .collapsed>.ellipsis{display:inherit;}/*!sc*/
data-styled.g50[id="sc-eRJQtA"]{content:"eudsMA,"}/*!sc*/
.caaBgn{padding:0.9em;background-color:rgba(38,50,56,0.4);margin:0 0 10px 0;display:block;font-family:Montserrat,sans-serif;font-size:0.929em;line-height:1.5em;}/*!sc*/
data-styled.g51[id="sc-bFwXsg"]{content:"caaBgn,"}/*!sc*/
.TqlOY{font-family:Montserrat,sans-serif;font-size:12px;position:absolute;z-index:1;top:-11px;left:12px;font-weight:600;color:rgba(255,255,255,0.7);}/*!sc*/
data-styled.g52[id="sc-fUPiRJ"]{content:"TqlOY,"}/*!sc*/
.fpFYoG{position:relative;}/*!sc*/
data-styled.g53[id="sc-bXYrjy"]{content:"fpFYoG,"}/*!sc*/
.kuvBhH{margin-top:15px;}/*!sc*/
data-styled.g56[id="sc-bgpKpp"]{content:"kuvBhH,"}/*!sc*/
.fmWEAp.deprecated span.property-name{text-decoration:line-through;color:#707070;}/*!sc*/
.fmWEAp button{background-color:transparent;border:0;outline:0;font-size:13px;font-family:Courier,monospace;cursor:pointer;padding:0;color:#333333;}/*!sc*/
.fmWEAp button:focus{font-weight:600;}/*!sc*/
.fmWEAp .sc-dibcMh{height:1.1em;width:1.1em;}/*!sc*/
.fmWEAp .sc-dibcMh polygon{fill:#666;}/*!sc*/
data-styled.g57[id="sc-iyUCga"]{content:"fmWEAp,"}/*!sc*/
.eyIabj{vertical-align:middle;font-size:13px;line-height:20px;}/*!sc*/
data-styled.g58[id="sc-byRegH"]{content:"eyIabj,"}/*!sc*/
.jGdyyu{color:rgba(102,102,102,0.9);}/*!sc*/
data-styled.g59[id="sc-budtlk"]{content:"jGdyyu,"}/*!sc*/
.fcQkkm{color:#666;}/*!sc*/
data-styled.g60[id="sc-fJvEKN"]{content:"fcQkkm,"}/*!sc*/
.iZVMho{color:#666;word-break:break-word;}/*!sc*/
data-styled.g61[id="sc-hsXxFb"]{content:"iZVMho,"}/*!sc*/
.iUhYwd{color:#d41f1c;font-size:0.9em;font-weight:normal;margin-left:20px;line-height:1;}/*!sc*/
data-styled.g62[id="sc-isZTFa"]{content:"iUhYwd,"}/*!sc*/
.eJpgoQ{background:#11171a;}/*!sc*/
.eJpgoQ>div,.eJpgoQ>pre{padding:20px;margin:0;}/*!sc*/
.eJpgoQ>div>pre{padding:0;}/*!sc*/
data-styled.g77[id="sc-cnVfeA"]{content:"eJpgoQ,"}/*!sc*/
.eSQudw:after{content:' and ';font-weight:normal;}/*!sc*/
.eSQudw:last-child:after{content:none;}/*!sc*/
.eSQudw a{text-decoration:auto;color:#32329f;}/*!sc*/
.eSQudw a:visited{color:#32329f;}/*!sc*/
.eSQudw a:hover{color:#6868cf;text-decoration:auto;}/*!sc*/
data-styled.g81[id="sc-hFPzkB"]{content:"eSQudw,"}/*!sc*/
.WPZDV{white-space:nowrap;}/*!sc*/
.WPZDV:after{content:' or ';white-space:pre;}/*!sc*/
.WPZDV:last-child:after,.WPZDV:only-child:after{content:none;}/*!sc*/
.WPZDV a{text-decoration:auto;color:#32329f;}/*!sc*/
.WPZDV a:visited{color:#32329f;}/*!sc*/
.WPZDV a:hover{color:#6868cf;text-decoration:auto;}/*!sc*/
data-styled.g82[id="sc-iFRVkA"]{content:"WPZDV,"}/*!sc*/
.gqqgtC{flex:1 1 auto;cursor:pointer;}/*!sc*/
data-styled.g83[id="sc-ilCyfT"]{content:"gqqgtC,"}/*!sc*/
.dNXpfB{width:75%;text-overflow:ellipsis;border-radius:4px;overflow:hidden;}/*!sc*/
@media screen and (max-width: 50rem){.dNXpfB{margin-top:10px;}}/*!sc*/
data-styled.g84[id="sc-jlEUfS"]{content:"dNXpfB,"}/*!sc*/
.jefPco{display:inline-block;margin:0;}/*!sc*/
data-styled.g85[id="sc-gUMLBR"]{content:"jefPco,"}/*!sc*/
.iaIgja{width:100%;display:flex;margin:1em 0;flex-direction:row;}/*!sc*/
@media screen and (max-width: 50rem){.iaIgja{flex-direction:column;}}/*!sc*/
data-styled.g86[id="sc-hUPhBQ"]{content:"iaIgja,"}/*!sc*/
.hLxGCH{margin-top:0;margin-bottom:0.5em;}/*!sc*/
data-styled.g92[id="sc-jxDJBN"]{content:"hLxGCH,"}/*!sc*/
.kfVmAL{width:9ex;display:inline-block;height:13px;line-height:13px;background-color:#333;border-radius:3px;background-repeat:no-repeat;background-position:6px 4px;font-size:7px;font-family:Verdana,sans-serif;color:white;text-transform:uppercase;text-align:center;font-weight:bold;vertical-align:middle;margin-right:6px;margin-top:2px;}/*!sc*/
.kfVmAL.get{background-color:#2F8132;}/*!sc*/
.kfVmAL.post{background-color:#186FAF;}/*!sc*/
.kfVmAL.put{background-color:#95507c;}/*!sc*/
.kfVmAL.options{background-color:#947014;}/*!sc*/
.kfVmAL.patch{background-color:#bf581d;}/*!sc*/
.kfVmAL.delete{background-color:#cc3333;}/*!sc*/
.kfVmAL.basic{background-color:#707070;}/*!sc*/
.kfVmAL.link{background-color:#07818F;}/*!sc*/
.kfVmAL.head{background-color:#A23DAD;}/*!sc*/
.kfVmAL.hook{background-color:#32329f;}/*!sc*/
.kfVmAL.schema{background-color:#707070;}/*!sc*/
data-styled.g100[id="sc-jsGcFV"]{content:"kfVmAL,"}/*!sc*/
.bquqCV{margin:0;padding:0;}/*!sc*/
.bquqCV:first-child{padding-bottom:32px;}/*!sc*/
.sc-FhnSQ .sc-FhnSQ{font-size:0.929em;}/*!sc*/
.jtzvEg{margin:0;padding:0;display:none;}/*!sc*/
.jtzvEg:first-child{padding-bottom:32px;}/*!sc*/
.sc-FhnSQ .sc-FhnSQ{font-size:0.929em;}/*!sc*/
data-styled.g101[id="sc-FhnSQ"]{content:"bquqCV,jtzvEg,"}/*!sc*/
.inBPjO{list-style:none inside none;overflow:hidden;text-overflow:ellipsis;padding:0;margin-top:15px;}/*!sc*/
.dsPLkk{list-style:none inside none;overflow:hidden;text-overflow:ellipsis;padding:0;}/*!sc*/
data-styled.g102[id="sc-cwcSVF"]{content:"inBPjO,dsPLkk,"}/*!sc*/
.lfXrFS{cursor:pointer;color:#333333;margin:0;padding:12.5px 20px;display:flex;justify-content:space-between;font-family:Montserrat,sans-serif;opacity:0.7;text-transform:uppercase;font-size:0.8em;padding-bottom:0;cursor:default;background-color:#fafafa;}/*!sc*/
.lfXrFS .sc-dibcMh{height:1.5em;width:1.5em;}/*!sc*/
.lfXrFS .sc-dibcMh polygon{fill:#333333;}/*!sc*/
.cNpcJW{cursor:pointer;color:#333333;margin:0;padding:12.5px 20px;display:flex;justify-content:space-between;font-family:Montserrat,sans-serif;font-size:0.929em;text-transform:none;background-color:#fafafa;}/*!sc*/
.cNpcJW:hover{color:#32329f;background-color:#e1e1e1;}/*!sc*/
.cNpcJW .sc-dibcMh{height:1.5em;width:1.5em;}/*!sc*/
.cNpcJW .sc-dibcMh polygon{fill:#333333;}/*!sc*/
.bqKYCQ{cursor:pointer;color:#333333;margin:0;padding:12.5px 20px;display:flex;justify-content:space-between;font-family:Montserrat,sans-serif;background-color:#fafafa;}/*!sc*/
.bqKYCQ:hover{color:#32329f;background-color:#ededed;}/*!sc*/
.bqKYCQ .sc-dibcMh{height:1.5em;width:1.5em;}/*!sc*/
.bqKYCQ .sc-dibcMh polygon{fill:#333333;}/*!sc*/
data-styled.g103[id="sc-fuBaRS"]{content:"lfXrFS,cNpcJW,bqKYCQ,"}/*!sc*/
.dsZUKj{display:inline-block;vertical-align:middle;width:calc(100% - 38px);overflow:hidden;text-overflow:ellipsis;}/*!sc*/
data-styled.g104[id="sc-hlwFUH"]{content:"dsZUKj,"}/*!sc*/
.eVjdzv{font-size:0.8em;margin-top:10px;text-align:center;position:fixed;width:260px;bottom:0;background:#fafafa;}/*!sc*/
.eVjdzv a,.eVjdzv a:visited,.eVjdzv a:hover{color:#333333!important;padding:5px 0;border-top:1px solid #e1e1e1;text-decoration:none;display:flex;align-items:center;justify-content:center;}/*!sc*/
.eVjdzv img{width:15px;margin-right:5px;}/*!sc*/
@media screen and (max-width: 50rem){.eVjdzv{width:100%;}}/*!sc*/
data-styled.g105[id="sc-kXtNZI"]{content:"eVjdzv,"}/*!sc*/
.cgvUrj{cursor:pointer;position:relative;margin-bottom:5px;}/*!sc*/
data-styled.g111[id="sc-eUzzst"]{content:"cgvUrj,"}/*!sc*/
.hgvsuc{font-family:Courier,monospace;margin-left:10px;flex:1;overflow-x:hidden;text-overflow:ellipsis;}/*!sc*/
data-styled.g112[id="sc-jgoAos"]{content:"hgvsuc,"}/*!sc*/
.ecNgtW{outline:0;color:inherit;width:100%;text-align:left;cursor:pointer;padding:10px 30px 10px 20px;border-radius:4px 4px 0 0;background-color:#11171a;display:flex;white-space:nowrap;align-items:center;border:1px solid transparent;border-bottom:0;transition:border-color 0.25s ease;}/*!sc*/
.ecNgtW ..sc-jgoAos{color:#ffffff;}/*!sc*/
.ecNgtW:focus{box-shadow:inset 0 2px 2px rgba(0, 0, 0, 0.45),0 2px 0 rgba(128, 128, 128, 0.25);}/*!sc*/
data-styled.g113[id="sc-bpAAPv"]{content:"ecNgtW,"}/*!sc*/
.bxmdGF{font-size:0.929em;line-height:20px;background-color:#186FAF;color:#ffffff;padding:3px 10px;text-transform:uppercase;font-family:Montserrat,sans-serif;margin:0;}/*!sc*/
.BkeVf{font-size:0.929em;line-height:20px;background-color:#cc3333;color:#ffffff;padding:3px 10px;text-transform:uppercase;font-family:Montserrat,sans-serif;margin:0;}/*!sc*/
.deMhiD{font-size:0.929em;line-height:20px;background-color:#2F8132;color:#ffffff;padding:3px 10px;text-transform:uppercase;font-family:Montserrat,sans-serif;margin:0;}/*!sc*/
data-styled.g114[id="sc-fBpBLu"]{content:"bxmdGF,BkeVf,deMhiD,"}/*!sc*/
.bVkkkk{position:absolute;width:100%;z-index:100;background:#fafafa;color:#263238;box-sizing:border-box;box-shadow:0 0 6px rgba(0, 0, 0, 0.33);overflow:hidden;border-bottom-left-radius:4px;border-bottom-right-radius:4px;transition:all 0.25s ease;visibility:hidden;transform:translateY(-50%) scaleY(0);}/*!sc*/
data-styled.g115[id="sc-dXqqap"]{content:"bVkkkk,"}/*!sc*/
.jlEXoB{padding:10px;}/*!sc*/
data-styled.g116[id="sc-ijfqWo"]{content:"jlEXoB,"}/*!sc*/
.zPpwX{padding:5px;border:1px solid #ccc;background:#fff;word-break:break-all;color:#32329f;}/*!sc*/
.zPpwX >span{color:#333333;}/*!sc*/
data-styled.g117[id="sc-srrxr"]{content:"zPpwX,"}/*!sc*/
.gfsyZD{display:block;border:0;width:100%;text-align:left;padding:10px;border-radius:2px;margin-bottom:4px;line-height:1.5em;cursor:pointer;color:#1d8127;background-color:rgba(29,129,39,0.07);}/*!sc*/
.gfsyZD:focus{outline:auto #1d8127;}/*!sc*/
.eNjFmB{display:block;border:0;width:100%;text-align:left;padding:10px;border-radius:2px;margin-bottom:4px;line-height:1.5em;cursor:pointer;color:#d41f1c;background-color:rgba(212,31,28,0.07);}/*!sc*/
.eNjFmB:focus{outline:auto #d41f1c;}/*!sc*/
.kdKxoT{display:block;border:0;width:100%;text-align:left;padding:10px;border-radius:2px;margin-bottom:4px;line-height:1.5em;cursor:pointer;color:#d41f1c;background-color:rgba(212,31,28,0.07);cursor:default;}/*!sc*/
.kdKxoT:focus{outline:auto #d41f1c;}/*!sc*/
.kdKxoT::before{content:"—";font-weight:bold;width:1.5em;text-align:center;display:inline-block;vertical-align:top;}/*!sc*/
.kdKxoT:focus{outline:0;}/*!sc*/
data-styled.g120[id="sc-jthNAk"]{content:"gfsyZD,eNjFmB,kdKxoT,"}/*!sc*/
.ecXPYN{vertical-align:top;}/*!sc*/
data-styled.g123[id="sc-eOObWA"]{content:"ecXPYN,"}/*!sc*/
.jWGRrJ{font-size:1.3em;padding:0.2em 0;margin:3em 0 1.1em;color:#333333;font-weight:normal;}/*!sc*/
data-styled.g124[id="sc-gSVmdp"]{content:"jWGRrJ,"}/*!sc*/
.fARMpn{margin-bottom:30px;}/*!sc*/
data-styled.g129[id="sc-bkBUWa"]{content:"fARMpn,"}/*!sc*/
.dpoYnd{user-select:none;width:20px;height:20px;align-self:center;display:flex;flex-direction:column;color:#32329f;}/*!sc*/
data-styled.g130[id="sc-doJfcP"]{content:"dpoYnd,"}/*!sc*/
.eMcOhX{width:260px;background-color:#fafafa;overflow:hidden;display:flex;flex-direction:column;backface-visibility:hidden;height:100vh;position:sticky;position:-webkit-sticky;top:0;}/*!sc*/
@media screen and (max-width: 50rem){.eMcOhX{position:fixed;z-index:20;width:100%;background:#fafafa;display:none;}}/*!sc*/
@media print{.eMcOhX{display:none;}}/*!sc*/
data-styled.g131[id="sc-fnaTjL"]{content:"eMcOhX,"}/*!sc*/
.Flmyj{outline:none;user-select:none;background-color:#f2f2f2;color:#32329f;display:none;cursor:pointer;position:fixed;right:20px;z-index:100;border-radius:50%;box-shadow:0 0 20px rgba(0, 0, 0, 0.3);bottom:44px;width:60px;height:60px;padding:0 20px;}/*!sc*/
@media screen and (max-width: 50rem){.Flmyj{display:flex;}}/*!sc*/
.Flmyj svg{color:#0065FB;}/*!sc*/
@media print{.Flmyj{display:none;}}/*!sc*/
data-styled.g132[id="sc-jyPUfK"]{content:"Flmyj,"}/*!sc*/
.ePGUxG{font-family:Roboto,sans-serif;font-size:14px;font-weight:400;line-height:1.5em;color:#333333;display:flex;position:relative;text-align:left;-webkit-font-smoothing:antialiased;font-smoothing:antialiased;text-rendering:optimizeSpeed!important;tap-highlight-color:rgba(0, 0, 0, 0);text-size-adjust:100%;}/*!sc*/
.ePGUxG *{box-sizing:border-box;-webkit-tap-highlight-color:rgba(255, 255, 255, 0);}/*!sc*/
data-styled.g133[id="sc-JNBUd"]{content:"ePGUxG,"}/*!sc*/
.bDhhVR{z-index:1;position:relative;overflow:hidden;width:calc(100% - 260px);contain:layout;}/*!sc*/
@media print,screen and (max-width: 50rem){.bDhhVR{width:100%;}}/*!sc*/
data-styled.g134[id="sc-eVCCQc"]{content:"bDhhVR,"}/*!sc*/
.haiOeP{background:#263238;position:absolute;top:0;bottom:0;right:0;width:calc((100% - 260px) * 0.4);}/*!sc*/
@media print,screen and (max-width: 75rem){.haiOeP{display:none;}}/*!sc*/
data-styled.g135[id="sc-epRJRH"]{content:"haiOeP,"}/*!sc*/
.jbjAuv{padding:5px 0;}/*!sc*/
data-styled.g136[id="sc-iBGKNG"]{content:"jbjAuv,"}/*!sc*/
.jVkBYH{width:calc(100% - 40px);box-sizing:border-box;margin:0 20px;padding:5px 10px 5px 20px;border:0;border-bottom:1px solid #e1e1e1;font-family:Roboto,sans-serif;font-weight:bold;font-size:13px;color:#333333;background-color:transparent;outline:none;}/*!sc*/
data-styled.g137[id="sc-lcacaV"]{content:"jVkBYH,"}/*!sc*/
.gXFsok{position:absolute;left:20px;height:1.8em;width:0.9em;}/*!sc*/
.gXFsok path{fill:#333333;}/*!sc*/
data-styled.g138[id="sc-dYttxY"]{content:"gXFsok,"}/*!sc*/
</style>
  <link href="https://fonts.googleapis.com/css?family=Montserrat:300,400,700|Roboto:300,400,700" rel="stylesheet">
</head>

<body>
  
      <div id="redoc"><div class="sc-JNBUd ePGUxG redoc-wrap"><div class="sc-fnaTjL eMcOhX menu-content" style="top:0px;height:calc(100vh - 0px)"><div role="search" class="sc-iBGKNG jbjAuv"><svg class="sc-dYttxY gXFsok search-icon" version="1.1" viewBox="0 0 1000 1000" x="0px" xmlns="http://www.w3.org/2000/svg" y="0px"><path d="M968.2,849.4L667.3,549c83.9-136.5,66.7-317.4-51.7-435.6C477.1-25,252.5-25,113.9,113.4c-138.5,138.3-138.5,362.6,0,501C219.2,730.1,413.2,743,547.6,666.5l301.9,301.4c43.6,43.6,76.9,14.9,104.2-12.4C981,928.3,1011.8,893,968.2,849.4z M524.5,522c-88.9,88.7-233,88.7-321.8,0c-88.9-88.7-88.9-232.6,0-321.3c88.9-88.7,233-88.7,321.8,0C613.4,289.4,613.4,433.3,524.5,522z"></path></svg><input placeholder="Search..." aria-label="Search" type="text" class="sc-lcacaV jVkBYH search-input" value=""/></div><div class="sc-epGxBs dkdAtf scrollbar-container undefined"><ul role="menu" class="sc-FhnSQ bquqCV"><li tabindex="0" depth="0" data-item-id="group/API-Reference" role="menuitem" aria-label="API Reference" aria-expanded="true" class="sc-cwcSVF inBPjO"><label class="sc-fuBaRS lfXrFS -depth0"><span width="calc(100% - 38px)" title="API Reference" class="sc-hlwFUH dsZUKj">API Reference</span></label><ul class="sc-FhnSQ bquqCV"><li tabindex="0" depth="1" data-item-id="tag/DataModel" role="menuitem" aria-label="DataModel" aria-expanded="false" class="sc-cwcSVF dsPLkk"><label class="sc-fuBaRS cNpcJW -depth1"><span width="calc(100% - 38px)" title="DataModel" class="sc-hlwFUH dsZUKj">DataModel</span><svg class="sc-dibcMh bnalXq" version="1.1" viewBox="0 0 24 24" x="0" xmlns="http://www.w3.org/2000/svg" y="0" aria-hidden="true"><polygon points="17.3 8.3 12 13.6 6.7 8.3 5.3 9.7 12 16.4 18.7 9.7 "></polygon></svg></label><ul class="sc-FhnSQ jtzvEg"><li tabindex="0" depth="2" data-item-id="tag/DataModel/operation/createDataModel" role="menuitem" aria-label="Create Data Model" aria-expanded="false" class="sc-cwcSVF dsPLkk"><label class="sc-fuBaRS bqKYCQ -depth2"><span type="post" class="sc-jsGcFV kfVmAL operation-type post">post</span><span tabindex="0" width="calc(100% - 38px)" class="sc-hlwFUH dsZUKj">Create Data Model</span></label></li><li tabindex="0" depth="2" data-item-id="tag/DataModel/operation/deleteDataModel" role="menuitem" aria-label="Delete Data Model" aria-expanded="false" class="sc-cwcSVF dsPLkk"><label class="sc-fuBaRS bqKYCQ -depth2"><span type="delete" class="sc-jsGcFV kfVmAL operation-type delete">del</span><span tabindex="0" width="calc(100% - 38px)" class="sc-hlwFUH dsZUKj">Delete Data Model</span></label></li><li tabindex="0" depth="2" data-item-id="tag/DataModel/operation/navigateDataModel" role="menuitem" aria-label="Navigate Data Model" aria-expanded="false" class="sc-cwcSVF dsPLkk"><label class="sc-fuBaRS bqKYCQ -depth2"><span type="get" class="sc-jsGcFV kfVmAL operation-type get">get</span><span tabindex="0" width="calc(100% - 38px)" class="sc-hlwFUH dsZUKj">Navigate Data Model</span></label></li></ul></li><li tabindex="0" depth="1" data-item-id="tag/Entity" role="menuitem" aria-label="Entity" aria-expanded="false" class="sc-cwcSVF dsPLkk"><label class="sc-fuBaRS cNpcJW -depth1"><span width="calc(100% - 38px)" title="Entity" class="sc-hlwFUH dsZUKj">Entity</span><svg class="sc-dibcMh bnalXq" version="1.1" viewBox="0 0 24 24" x="0" xmlns="http://www.w3.org/2000/svg" y="0" aria-hidden="true"><polygon points="17.3 8.3 12 13.6 6.7 8.3 5.3 9.7 12 16.4 18.7 9.7 "></polygon></svg></label><ul class="sc-FhnSQ jtzvEg"><li tabindex="0" depth="2" data-item-id="tag/Entity/operation/findEntities" role="menuitem" aria-label="Retrieve a List of Entities" aria-expanded="false" class="sc-cwcSVF dsPLkk"><label class="sc-fuBaRS bqKYCQ -depth2"><span type="get" class="sc-jsGcFV kfVmAL operation-type get">get</span><span tabindex="0" width="calc(100% - 38px)" class="sc-hlwFUH dsZUKj">Retrieve a List of Entities</span></label></li></ul></li><li tabindex="0" depth="1" data-item-id="tag/EntityElement" role="menuitem" aria-label="EntityElement" aria-expanded="false" class="sc-cwcSVF dsPLkk"><label class="sc-fuBaRS cNpcJW -depth1"><span width="calc(100% - 38px)" title="EntityElement" class="sc-hlwFUH dsZUKj">EntityElement</span><svg class="sc-dibcMh bnalXq" version="1.1" viewBox="0 0 24 24" x="0" xmlns="http://www.w3.org/2000/svg" y="0" aria-hidden="true"><polygon points="17.3 8.3 12 13.6 6.7 8.3 5.3 9.7 12 16.4 18.7 9.7 "></polygon></svg></label><ul class="sc-FhnSQ jtzvEg"><li tabindex="0" depth="2" data-item-id="tag/EntityElement/operation/findElements" role="menuitem" aria-label="Retrieve a List of Entity Elements" aria-expanded="false" class="sc-cwcSVF dsPLkk"><label class="sc-fuBaRS bqKYCQ -depth2"><span type="get" class="sc-jsGcFV kfVmAL operation-type get">get</span><span tabindex="0" width="calc(100% - 38px)" class="sc-hlwFUH dsZUKj">Retrieve a List of Entity Elements</span></label></li></ul></li><li tabindex="0" depth="1" data-item-id="tag/Import-DataModel" role="menuitem" aria-label="Import DataModel" aria-expanded="false" class="sc-cwcSVF dsPLkk"><label class="sc-fuBaRS cNpcJW -depth1"><span width="calc(100% - 38px)" title="Import DataModel" class="sc-hlwFUH dsZUKj">Import DataModel</span><svg class="sc-dibcMh bnalXq" version="1.1" viewBox="0 0 24 24" x="0" xmlns="http://www.w3.org/2000/svg" y="0" aria-hidden="true"><polygon points="17.3 8.3 12 13.6 6.7 8.3 5.3 9.7 12 16.4 18.7 9.7 "></polygon></svg></label><ul class="sc-FhnSQ jtzvEg"><li tabindex="0" depth="2" data-item-id="tag/Import-DataModel/operation/importDataModel" role="menuitem" aria-label="Import Data Model" aria-expanded="false" class="sc-cwcSVF dsPLkk"><label class="sc-fuBaRS bqKYCQ -depth2"><span type="post" class="sc-jsGcFV kfVmAL operation-type post">post</span><span tabindex="0" width="calc(100% - 38px)" class="sc-hlwFUH dsZUKj">Import Data Model</span></label></li></ul></li></ul></li><li tabindex="0" depth="0" data-item-id="group/Models" role="menuitem" aria-label="Models" aria-expanded="true" class="sc-cwcSVF inBPjO"><label class="sc-fuBaRS lfXrFS -depth0"><span width="calc(100% - 38px)" title="Models" class="sc-hlwFUH dsZUKj">Models</span></label><ul class="sc-FhnSQ bquqCV"><li tabindex="0" depth="1" data-item-id="tag/Schemas" role="menuitem" aria-label="Schemas" aria-expanded="false" class="sc-cwcSVF dsPLkk"><label class="sc-fuBaRS cNpcJW -depth1"><span width="calc(100% - 38px)" title="Schemas" class="sc-hlwFUH dsZUKj">Schemas</span><svg class="sc-dibcMh bnalXq" version="1.1" viewBox="0 0 24 24" x="0" xmlns="http://www.w3.org/2000/svg" y="0" aria-hidden="true"><polygon points="17.3 8.3 12 13.6 6.7 8.3 5.3 9.7 12 16.4 18.7 9.7 "></polygon></svg></label><ul class="sc-FhnSQ jtzvEg"><li tabindex="0" depth="2" data-item-id="schema/EntityElement" role="menuitem" aria-label="EntityElement" aria-expanded="false" class="sc-cwcSVF dsPLkk"><label class="sc-fuBaRS bqKYCQ -depth2"><span type="schema" class="sc-jsGcFV kfVmAL operation-type schema">schema</span><span width="calc(100% - 38px)" title="EntityElement" class="sc-hlwFUH dsZUKj">EntityElement</span></label></li><li tabindex="0" depth="2" data-item-id="schema/Entity" role="menuitem" aria-label="Entity" aria-expanded="false" class="sc-cwcSVF dsPLkk"><label class="sc-fuBaRS bqKYCQ -depth2"><span type="schema" class="sc-jsGcFV kfVmAL operation-type schema">schema</span><span width="calc(100% - 38px)" title="Entity" class="sc-hlwFUH dsZUKj">Entity</span></label></li><li tabindex="0" depth="2" data-item-id="schema/ModelElementRef" role="menuitem" aria-label="ModelElementRef" aria-expanded="false" class="sc-cwcSVF dsPLkk"><label class="sc-fuBaRS bqKYCQ -depth2"><span type="schema" class="sc-jsGcFV kfVmAL operation-type schema">schema</span><span width="calc(100% - 38px)" title="ModelElementRef" class="sc-hlwFUH dsZUKj">ModelElementRef</span></label></li><li tabindex="0" depth="2" data-item-id="schema/Relationship" role="menuitem" aria-label="Relationship" aria-expanded="false" class="sc-cwcSVF dsPLkk"><label class="sc-fuBaRS bqKYCQ -depth2"><span type="schema" class="sc-jsGcFV kfVmAL operation-type schema">schema</span><span width="calc(100% - 38px)" title="Relationship" class="sc-hlwFUH dsZUKj">Relationship</span></label></li><li tabindex="0" depth="2" data-item-id="schema/DataModelRequest" role="menuitem" aria-label="DataModelRequest" aria-expanded="false" class="sc-cwcSVF dsPLkk"><label class="sc-fuBaRS bqKYCQ -depth2"><span type="schema" class="sc-jsGcFV kfVmAL operation-type schema">schema</span><span width="calc(100% - 38px)" title="DataModelRequest" class="sc-hlwFUH dsZUKj">DataModelRequest</span></label></li><li tabindex="0" depth="2" data-item-id="schema/DataModelResponse" role="menuitem" aria-label="DataModelResponse" aria-expanded="false" class="sc-cwcSVF dsPLkk"><label class="sc-fuBaRS bqKYCQ -depth2"><span type="schema" class="sc-jsGcFV kfVmAL operation-type schema">schema</span><span width="calc(100% - 38px)" title="DataModelResponse" class="sc-hlwFUH dsZUKj">DataModelResponse</span></label></li><li tabindex="0" depth="2" data-item-id="schema/ErrorDetail" role="menuitem" aria-label="ErrorDetail" aria-expanded="false" class="sc-cwcSVF dsPLkk"><label class="sc-fuBaRS bqKYCQ -depth2"><span type="schema" class="sc-jsGcFV kfVmAL operation-type schema">schema</span><span width="calc(100% - 38px)" title="ErrorDetail" class="sc-hlwFUH dsZUKj">ErrorDetail</span></label></li><li tabindex="0" depth="2" data-item-id="schema/ApiError" role="menuitem" aria-label="ApiError" aria-expanded="false" class="sc-cwcSVF dsPLkk"><label class="sc-fuBaRS bqKYCQ -depth2"><span type="schema" class="sc-jsGcFV kfVmAL operation-type schema">schema</span><span width="calc(100% - 38px)" title="ApiError" class="sc-hlwFUH dsZUKj">ApiError</span></label></li><li tabindex="0" depth="2" data-item-id="schema/DeleteDataModelResponse" role="menuitem" aria-label="DeleteDataModelResponse" aria-expanded="false" class="sc-cwcSVF dsPLkk"><label class="sc-fuBaRS bqKYCQ -depth2"><span type="schema" class="sc-jsGcFV kfVmAL operation-type schema">schema</span><span width="calc(100% - 38px)" title="DeleteDataModelResponse" class="sc-hlwFUH dsZUKj">DeleteDataModelResponse</span></label></li><li tabindex="0" depth="2" data-item-id="schema/NavigateResponse" role="menuitem" aria-label="NavigateResponse" aria-expanded="false" class="sc-cwcSVF dsPLkk"><label class="sc-fuBaRS bqKYCQ -depth2"><span type="schema" class="sc-jsGcFV kfVmAL operation-type schema">schema</span><span width="calc(100% - 38px)" title="NavigateResponse" class="sc-hlwFUH dsZUKj">NavigateResponse</span></label></li><li tabindex="0" depth="2" data-item-id="schema/ImportDataModelRequest" role="menuitem" aria-label="ImportDataModelRequest" aria-expanded="false" class="sc-cwcSVF dsPLkk"><label class="sc-fuBaRS bqKYCQ -depth2"><span type="schema" class="sc-jsGcFV kfVmAL operation-type schema">schema</span><span width="calc(100% - 38px)" title="ImportDataModelRequest" class="sc-hlwFUH dsZUKj">ImportDataModelRequest</span></label></li><li tabindex="0" depth="2" data-item-id="schema/ImportDataModelResponse" role="menuitem" aria-label="ImportDataModelResponse" aria-expanded="false" class="sc-cwcSVF dsPLkk"><label class="sc-fuBaRS bqKYCQ -depth2"><span type="schema" class="sc-jsGcFV kfVmAL operation-type schema">schema</span><span width="calc(100% - 38px)" title="ImportDataModelResponse" class="sc-hlwFUH dsZUKj">ImportDataModelResponse</span></label></li><li tabindex="0" depth="2" data-item-id="schema/CatalogError" role="menuitem" aria-label="CatalogError" aria-expanded="false" class="sc-cwcSVF dsPLkk"><label class="sc-fuBaRS bqKYCQ -depth2"><span type="schema" class="sc-jsGcFV kfVmAL operation-type schema">schema</span><span width="calc(100% - 38px)" title="CatalogError" class="sc-hlwFUH dsZUKj">CatalogError</span></label></li></ul></li></ul></li></ul><div class="sc-kXtNZI eVjdzv"><a target="_blank" rel="noopener noreferrer" href="https://redocly.com/redoc/">API docs by Redocly</a></div></div></div><div class="sc-jyPUfK Flmyj"><div class="sc-doJfcP dpoYnd"><svg class="" style="transform:translate(2px, -4px) rotate(180deg);transition:transform 0.2s ease" viewBox="0 0 926.23699 573.74994" version="1.1" x="0px" y="0px" width="15" height="15"><g transform="translate(904.92214,-879.1482)"><path d="
          m -673.67664,1221.6502 -231.2455,-231.24803 55.6165,
          -55.627 c 30.5891,-30.59485 56.1806,-55.627 56.8701,-55.627 0.6894,
          0 79.8637,78.60862 175.9427,174.68583 l 174.6892,174.6858 174.6892,
          -174.6858 c 96.079,-96.07721 175.253196,-174.68583 175.942696,
          -174.68583 0.6895,0 26.281,25.03215 56.8701,
          55.627 l 55.6165,55.627 -231.245496,231.24803 c -127.185,127.1864
          -231.5279,231.248 -231.873,231.248 -0.3451,0 -104.688,
          -104.0616 -231.873,-231.248 z
        " fill="currentColor"></path></g></svg><svg class="" style="transform:translate(2px, 4px);transition:transform 0.2s ease" viewBox="0 0 926.23699 573.74994" version="1.1" x="0px" y="0px" width="15" height="15"><g transform="translate(904.92214,-879.1482)"><path d="
          m -673.67664,1221.6502 -231.2455,-231.24803 55.6165,
          -55.627 c 30.5891,-30.59485 56.1806,-55.627 56.8701,-55.627 0.6894,
          0 79.8637,78.60862 175.9427,174.68583 l 174.6892,174.6858 174.6892,
          -174.6858 c 96.079,-96.07721 175.253196,-174.68583 175.942696,
          -174.68583 0.6895,0 26.281,25.03215 56.8701,
          55.627 l 55.6165,55.627 -231.245496,231.24803 c -127.185,127.1864
          -231.5279,231.248 -231.873,231.248 -0.3451,0 -104.688,
          -104.0616 -231.873,-231.248 z
        " fill="currentColor"></path></g></svg></div></div><div class="sc-eVCCQc bDhhVR api-content"><div class="sc-dYOLZc kDuBQd"><div class="sc-jZhnRx tMeMv"><div class="sc-gwsNht iRoSfe api-info"><h1 class="sc-hBDmJg sc-jxDJBN dKykVD hLxGCH">Catalog Service API<!-- --> <span>(<!-- -->0.0.1<!-- -->)</span></h1><p>Download OpenAPI specification<!-- -->:</p><div class="sc-faJlkc sc-fdduAw kOqeZB kEOvim"></div><div data-role="redoc-summary" html="" class="sc-faJlkc sc-fdduAw kOqeZB kEOvim"></div><div data-role="redoc-description" html="&lt;p&gt;The Syniti Knowledge Platform (SKP) Catalog is the collection of technical and business data definitions that is used to document and describe the systems and business processes of our clients.
The data definitons stored in the Catalog is used to enable several functionalities within SKP like data profiling, data migration, data quality, matching, data replication, reporting, etc.
This catalog exists primarily as a means to enable this functionality, but should also be considered as a knowledge repository for the business users.&lt;/p&gt;
&lt;p&gt;Key features include:&lt;/p&gt;
&lt;ul&gt;
&lt;li&gt;Create and Manage Technical Data Definitions retrieved from the MetaScanner (Database Table, Database View)&lt;/li&gt;
&lt;li&gt;Create and Manage Business Data Definitions (View, Rule, Validation)&lt;/li&gt;
&lt;li&gt;Capture data profiling information&lt;/li&gt;
&lt;li&gt;Ablity to version and manage the lifecycle of the Data Definitions&lt;/li&gt;
&lt;li&gt;Support to catpure the schema of future polyglot Datasources (ODATA, OpenAPI, etc.)&lt;/li&gt;
&lt;/ul&gt;
" class="sc-faJlkc sc-fdduAw kOqeZB kEOvim"><p>The Syniti Knowledge Platform (SKP) Catalog is the collection of technical and business data definitions that is used to document and describe the systems and business processes of our clients.
The data definitons stored in the Catalog is used to enable several functionalities within SKP like data profiling, data migration, data quality, matching, data replication, reporting, etc.
This catalog exists primarily as a means to enable this functionality, but should also be considered as a knowledge repository for the business users.</p>
<p>Key features include:</p>
<ul>
<li>Create and Manage Technical Data Definitions retrieved from the MetaScanner (Database Table, Database View)</li>
<li>Create and Manage Business Data Definitions (View, Rule, Validation)</li>
<li>Capture data profiling information</li>
<li>Ablity to version and manage the lifecycle of the Data Definitions</li>
<li>Support to catpure the schema of future polyglot Datasources (ODATA, OpenAPI, etc.)</li>
</ul>
</div></div></div></div><div id="tag/DataModel" data-section-id="tag/DataModel" class="sc-dYOLZc kDuBQd"><div class="sc-jZhnRx tMeMv"><div class="sc-gwsNht iRoSfe"><h2 class="sc-kThouk jkwlQD"><a class="sc-kieALA jOWzfS" href="#tag/DataModel" aria-label="tag/DataModel"></a>DataModel</h2></div></div><div class="sc-gwsNht ciCSbi"><div class="sc-faJlkc sc-fdduAw kOqeZB kEOvim redoc-markdown " html="&lt;p&gt;Data Model Operations&lt;/p&gt;
"><p>Data Model Operations</p>
</div></div></div><div id="tag/DataModel/operation/createDataModel" data-section-id="tag/DataModel/operation/createDataModel" class="sc-dYOLZc dRVblm"><div data-section-id="operation/createDataModel" id="operation/createDataModel" class="sc-jZhnRx tMeMv"><div class="sc-gwsNht iRoSfe"><h2 class="sc-kThouk jkwlQD"><a class="sc-kieALA jOWzfS" href="#tag/DataModel/operation/createDataModel" aria-label="tag/DataModel/operation/createDataModel"></a>Create Data Model<!-- --> </h2><div class="sc-bkBUWa fARMpn"><div html="&lt;p&gt;The API allows you to create a new data model with a complete, authoritative representation.&lt;/p&gt;
&lt;p&gt;&lt;strong&gt;How It Works&lt;/strong&gt;&lt;/p&gt;
&lt;ul&gt;
&lt;li&gt;Creation: Use the POST method to create a new data model. The server stores it as the authoritative version.&lt;/li&gt;
&lt;li&gt;Full Update: Use the PUT method to replace an existing data model entirely with an updated version.&lt;/li&gt;
&lt;li&gt;Partial Update: Use the PATCH method for partial updates, specifying only the changes to be made.&lt;/li&gt;
&lt;/ul&gt;
&lt;p&gt;&lt;strong&gt;Key Points for Consumers&lt;/strong&gt;&lt;/p&gt;
&lt;ul&gt;
&lt;li&gt;Complete Representation: The PUT method requires the entire data model. Missing sections will be removed or reset.&lt;/li&gt;
&lt;li&gt;Versioning: Each full update creates a new version. Include the current version to avoid conflicts. Updates in progress for the same version will be rejected.&lt;/li&gt;
&lt;li&gt;Idempotency: Repeating the same PUT request will yield the same result.&lt;/li&gt;
&lt;/ul&gt;
&lt;p&gt;&lt;strong&gt;Data Model Context&lt;/strong&gt;&lt;/p&gt;
&lt;ul&gt;
&lt;li&gt;The data model is defined within a context like a Datastore.&lt;/li&gt;
&lt;li&gt;Wihthin a context,&lt;ul&gt;
&lt;li&gt;The name of the entity and relationship must be unique.&lt;/li&gt;
&lt;li&gt;The name of an entity element must be unique within the entity.&lt;/li&gt;
&lt;/ul&gt;
&lt;/li&gt;
&lt;/ul&gt;
&lt;p&gt;&lt;strong&gt;Naming Rules&lt;/strong&gt;&lt;/p&gt;
&lt;ul&gt;
&lt;li&gt;Names can contain&lt;ul&gt;
&lt;li&gt;alphanumeric characters&lt;/li&gt;
&lt;li&gt;case insenstive&lt;/li&gt;
&lt;li&gt;underscore&lt;/li&gt;
&lt;li&gt;hyphen&lt;/li&gt;
&lt;/ul&gt;
&lt;/li&gt;
&lt;li&gt;Entity / Relationship Name&lt;ul&gt;
&lt;li&gt;Must be unique within the namespace&lt;/li&gt;
&lt;/ul&gt;
&lt;/li&gt;
&lt;li&gt;Entity Element Name&lt;ul&gt;
&lt;li&gt;Must be unique within the entity.&lt;/li&gt;
&lt;/ul&gt;
&lt;/li&gt;
&lt;/ul&gt;
" class="sc-faJlkc sc-fdduAw kOqeZB kEOvim"><p>The API allows you to create a new data model with a complete, authoritative representation.</p>
<p><strong>How It Works</strong></p>
<ul>
<li>Creation: Use the POST method to create a new data model. The server stores it as the authoritative version.</li>
<li>Full Update: Use the PUT method to replace an existing data model entirely with an updated version.</li>
<li>Partial Update: Use the PATCH method for partial updates, specifying only the changes to be made.</li>
</ul>
<p><strong>Key Points for Consumers</strong></p>
<ul>
<li>Complete Representation: The PUT method requires the entire data model. Missing sections will be removed or reset.</li>
<li>Versioning: Each full update creates a new version. Include the current version to avoid conflicts. Updates in progress for the same version will be rejected.</li>
<li>Idempotency: Repeating the same PUT request will yield the same result.</li>
</ul>
<p><strong>Data Model Context</strong></p>
<ul>
<li>The data model is defined within a context like a Datastore.</li>
<li>Wihthin a context,<ul>
<li>The name of the entity and relationship must be unique.</li>
<li>The name of an entity element must be unique within the entity.</li>
</ul>
</li>
</ul>
<p><strong>Naming Rules</strong></p>
<ul>
<li>Names can contain<ul>
<li>alphanumeric characters</li>
<li>case insenstive</li>
<li>underscore</li>
<li>hyphen</li>
</ul>
</li>
<li>Entity / Relationship Name<ul>
<li>Must be unique within the namespace</li>
</ul>
</li>
<li>Entity Element Name<ul>
<li>Must be unique within the entity.</li>
</ul>
</li>
</ul>
</div></div><div class="sc-hUPhBQ iaIgja"><div class="sc-ilCyfT gqqgtC"><h5 class="sc-elFkmj sc-gUMLBR eUkINt jefPco">Authorizations:</h5><svg class="sc-dibcMh gsIrbT" version="1.1" viewBox="0 0 24 24" x="0" xmlns="http://www.w3.org/2000/svg" y="0" aria-hidden="true"><polygon points="17.3 8.3 12 13.6 6.7 8.3 5.3 9.7 12 16.4 18.7 9.7 "></polygon></svg></div><div class="sc-jlEUfS dNXpfB"><span class="sc-iFRVkA WPZDV"><span class="sc-hFPzkB eSQudw"><i>bearerAuth</i></span></span></div></div><div><h5 class="sc-elFkmj eUkINt">path<!-- --> Parameters</h5><table class="sc-eGjrzz cGiuNU"><tbody><tr class="last "><td kind="field" title="context_id" class="sc-kHNKno sc-frmfij cDFxGr kNAbAx"><span class="sc-hLyRwt lhCIDE"></span><span class="property-name">context_id</span><div class="sc-byRegH sc-isZTFa eyIabj iUhYwd">required</div></td><td class="sc-groBii dzPbxx"><div><div><span class="sc-byRegH sc-budtlk eyIabj jGdyyu"></span><span class="sc-byRegH sc-fJvEKN eyIabj fcQkkm">string</span></div> <div><div html="&lt;p&gt;The unique identifier for the datastore.&lt;/p&gt;
" class="sc-faJlkc sc-fdduAw kOqeZB csbUjU"><p>The unique identifier for the datastore.</p>
</div></div></div></td></tr></tbody></table></div><h5 class="sc-elFkmj eUkINt">Request Body schema: <span class="sc-edaYAx iFdOsg">application/json</span></h5><div html="" class="sc-faJlkc sc-fdduAw kOqeZB kEOvim"></div><table class="sc-eGjrzz cGiuNU"><tbody><tr class=""><td kind="field" title="meta_schema_version" class="sc-kHNKno sc-frmfij cDFxGr kNAbAx"><span class="sc-hLyRwt lhCIDE"></span><span class="property-name">meta_schema_version</span><div class="sc-byRegH sc-isZTFa eyIabj iUhYwd">required</div></td><td class="sc-groBii dzPbxx"><div><div><span class="sc-byRegH sc-budtlk eyIabj jGdyyu"></span><span class="sc-byRegH sc-fJvEKN eyIabj fcQkkm">string</span></div> <div><div html="&lt;p&gt;The meta-meta schema version.
It controls the rules and structure of the data model, including the allowed fields, their types, and other constraints
The value of this field ensures that the system understands how to interpret the data and which validation rules to apply.
For example, the version &lt;code&gt;relational-20250501&lt;/code&gt; informs the system that the data model defined in the current API is representing the database schama.
Hence the system can validate that the allowed entity types are only TABLE and VIEW.&lt;/p&gt;
" class="sc-faJlkc sc-fdduAw kOqeZB csbUjU"><p>The meta-meta schema version.
It controls the rules and structure of the data model, including the allowed fields, their types, and other constraints
The value of this field ensures that the system understands how to interpret the data and which validation rules to apply.
For example, the version <code>relational-20250501</code> informs the system that the data model defined in the current API is representing the database schama.
Hence the system can validate that the allowed entity types are only TABLE and VIEW.</p>
</div></div></div></td></tr><tr class=""><td kind="field" title="entities" class="sc-kHNKno sc-frmfij sc-iyUCga cDFxGr kNAbAx fmWEAp"><span class="sc-hLyRwt lhCIDE"></span><button aria-label="expand entities"><span class="property-name">entities</span><svg class="sc-dibcMh eVNtOe" version="1.1" viewBox="0 0 24 24" x="0" xmlns="http://www.w3.org/2000/svg" y="0" aria-hidden="true"><polygon points="17.3 8.3 12 13.6 6.7 8.3 5.3 9.7 12 16.4 18.7 9.7 "></polygon></svg></button></td><td class="sc-groBii dzPbxx"><div><div><span class="sc-byRegH sc-budtlk eyIabj jGdyyu">Array of </span><span class="sc-byRegH sc-fJvEKN eyIabj fcQkkm">objects</span><span class="sc-byRegH sc-hsXxFb eyIabj iZVMho"> (<!-- -->Entity<!-- -->) </span></div> <div><div html="&lt;p&gt;The list of entities in the data model.
Each entity represents a distinct object or concept within the data model.
The entity can be a table, view, or any other object that is relevant to the data model.
Each entity has a unique name and can have multiple entity elements (fields) associated with it.&lt;/p&gt;
" class="sc-faJlkc sc-fdduAw kOqeZB csbUjU"><p>The list of entities in the data model.
Each entity represents a distinct object or concept within the data model.
The entity can be a table, view, or any other object that is relevant to the data model.
Each entity has a unique name and can have multiple entity elements (fields) associated with it.</p>
</div></div></div></td></tr><tr class="last "><td kind="field" title="relationships" class="sc-kHNKno sc-frmfij sc-iyUCga cDFxGr kNAbAx fmWEAp"><span class="sc-hLyRwt lhCIDE"></span><button aria-label="expand relationships"><span class="property-name">relationships</span><svg class="sc-dibcMh eVNtOe" version="1.1" viewBox="0 0 24 24" x="0" xmlns="http://www.w3.org/2000/svg" y="0" aria-hidden="true"><polygon points="17.3 8.3 12 13.6 6.7 8.3 5.3 9.7 12 16.4 18.7 9.7 "></polygon></svg></button></td><td class="sc-groBii dzPbxx"><div><div><span class="sc-byRegH sc-budtlk eyIabj jGdyyu">Array of </span><span class="sc-byRegH sc-fJvEKN eyIabj fcQkkm">objects</span><span class="sc-byRegH sc-hsXxFb eyIabj iZVMho"> (<!-- -->Relationship<!-- -->) </span></div> <div><div html="&lt;p&gt;The list of relationships in the data model.
Each relationship represents a connection between entities or entity elements.&lt;/p&gt;
" class="sc-faJlkc sc-fdduAw kOqeZB csbUjU"><p>The list of relationships in the data model.
Each relationship represents a connection between entities or entity elements.</p>
</div></div></div></td></tr></tbody></table><div><h3 class="sc-gSVmdp jWGRrJ">Responses</h3><div><button class="sc-jthNAk gfsyZD"><svg class="sc-dibcMh dpIKEE" version="1.1" viewBox="0 0 24 24" x="0" xmlns="http://www.w3.org/2000/svg" y="0" aria-hidden="true"><polygon points="17.3 8.3 12 13.6 6.7 8.3 5.3 9.7 12 16.4 18.7 9.7 "></polygon></svg><strong class="sc-eOObWA ecXPYN">200<!-- --> </strong><div html="&lt;p&gt;Created or Updated.&lt;/p&gt;
" class="sc-faJlkc sc-fdduAw sc-eINXvP kOqeZB kkltVg fbuMAW"><p>Created or Updated.</p>
</div></button></div><div><button class="sc-jthNAk eNjFmB"><svg class="sc-dibcMh jsRtRU" version="1.1" viewBox="0 0 24 24" x="0" xmlns="http://www.w3.org/2000/svg" y="0" aria-hidden="true"><polygon points="17.3 8.3 12 13.6 6.7 8.3 5.3 9.7 12 16.4 18.7 9.7 "></polygon></svg><strong class="sc-eOObWA ecXPYN">400<!-- --> </strong><div html="&lt;p&gt;Bad Request.&lt;/p&gt;
" class="sc-faJlkc sc-fdduAw sc-eINXvP kOqeZB kkltVg fbuMAW"><p>Bad Request.</p>
</div></button></div><div><button class="sc-jthNAk kdKxoT" disabled=""><strong class="sc-eOObWA ecXPYN">401<!-- --> </strong><div html="&lt;p&gt;Authentication information is expired, missing, or invalid.
Ensure that you have authenticated correctly.&lt;/p&gt;
" class="sc-faJlkc sc-fdduAw sc-eINXvP kOqeZB kkltVg fbuMAW"><p>Authentication information is expired, missing, or invalid.
Ensure that you have authenticated correctly.</p>
</div></button></div><div><button class="sc-jthNAk kdKxoT" disabled=""><strong class="sc-eOObWA ecXPYN">403<!-- --> </strong><div html="&lt;p&gt;Authorization failed or access denied error.
Ensure that you have the necessary permissions.&lt;/p&gt;
" class="sc-faJlkc sc-fdduAw sc-eINXvP kOqeZB kkltVg fbuMAW"><p>Authorization failed or access denied error.
Ensure that you have the necessary permissions.</p>
</div></button></div></div></div><div class="sc-jMpmlX sc-hoLldG kCcXaS jpPJrn"><div class="sc-eUzzst cgvUrj"><button class="sc-bpAAPv ecNgtW"><span type="post" class="sc-fBpBLu bxmdGF http-verb post">post</span><span class="sc-jgoAos hgvsuc">/contexts/{context_id}/datamodel</span><svg class="sc-dibcMh NOyYc" style="margin-right:-25px" version="1.1" viewBox="0 0 24 24" x="0" xmlns="http://www.w3.org/2000/svg" y="0" aria-hidden="true"><polygon points="17.3 8.3 12 13.6 6.7 8.3 5.3 9.7 12 16.4 18.7 9.7 "></polygon></svg></button><div aria-hidden="true" class="sc-dXqqap bVkkkk"><div class="sc-ijfqWo jlEXoB"><div html="" class="sc-faJlkc sc-fdduAw kOqeZB csbUjU"></div><div tabindex="0" role="button"><div class="sc-srrxr zPpwX"><span>http://catalog.govern.svc.cluster.local</span>/contexts/{context_id}/datamodel</div></div></div><div class="sc-ijfqWo jlEXoB"><div html="" class="sc-faJlkc sc-fdduAw kOqeZB csbUjU"></div><div tabindex="0" role="button"><div class="sc-srrxr zPpwX"><span>https://ct.mgt.syniti-dev.com</span>/contexts/{context_id}/datamodel</div></div></div></div></div><div><h3 class="sc-llIIlC fdkIVZ"> <!-- -->Request samples<!-- --> </h3><div class="sc-cTIdZS dFpYBv" data-rttabs="true"><ul class="react-tabs__tab-list" role="tablist"><li class="react-tabs__tab react-tabs__tab--selected" role="tab" id="tab«R99ccq»0" aria-selected="true" aria-disabled="false" aria-controls="panel«R99ccq»0" tabindex="0" data-rttab="true">Payload</li></ul><div class="react-tabs__tab-panel react-tabs__tab-panel--selected" role="tabpanel" id="panel«R99ccq»0" aria-labelledby="tab«R99ccq»0"><div><div class="sc-bXYrjy fpFYoG"><span class="sc-fUPiRJ TqlOY">Content type</span><div class="sc-bFwXsg caaBgn">application/json</div></div><div class="sc-bgpKpp kuvBhH"><div class="sc-dRHutB iAOzgg"><div class="sc-VILhF itwxyW"><button><div class="sc-fIQtvO indwFD">Copy</div></button><button> Expand all </button><button> Collapse all </button></div><div tabindex="0" class="sc-faJlkc kOqeZB sc-eRJQtA eudsMA"><div class="redoc-json"><code><button class="collapser" aria-label="collapse"></button><span class="token punctuation">{</span><span class="ellipsis"></span><ul class="obj collapsible"><li><div class="hoverable "><span class="property token string">"meta_schema_version"</span>: <span class="token string">&quot;relational-20250501&quot;</span><span class="token punctuation">,</span></div></li><li><div class="hoverable "><span class="property token string">"entities"</span>: <button class="collapser" aria-label="collapse"></button><span class="token punctuation">[</span><span class="ellipsis"></span><ul class="array collapsible"><li><div class="hoverable collapsed"><button class="collapser" aria-label="expand"></button><span class="token punctuation">{</span><span class="ellipsis"></span><ul class="obj collapsible"><li><div class="hoverable collapsed"><span class="property token string">"model_name"</span>: <span class="token string">&quot;ns1.Product&quot;</span><span class="token punctuation">,</span></div></li><li><div class="hoverable collapsed"><span class="property token string">"name"</span>: <span class="token string">&quot;Product&quot;</span><span class="token punctuation">,</span></div></li><li><div class="hoverable collapsed"><span class="property token string">"type"</span>: <span class="token string">&quot;TABLE&quot;</span><span class="token punctuation">,</span></div></li><li><div class="hoverable collapsed"><span class="property token string">"consumption_type"</span>: <span class="token string">&quot;dataset&quot;</span><span class="token punctuation">,</span></div></li><li><div class="hoverable collapsed"><span class="property token string">"description"</span>: <span class="token string">&quot;&quot;</span><span class="token punctuation">,</span></div></li><li><div class="hoverable collapsed"><span class="property token string">"active"</span>: <span class="token boolean">true</span><span class="token punctuation">,</span></div></li><li><div class="hoverable collapsed"><span class="property token string">"index"</span>: <span class="token number">1</span><span class="token punctuation">,</span></div></li><li><div class="hoverable collapsed"><span class="property token string">"tags"</span>: <span class="token punctuation">[ ]</span><span class="token punctuation">,</span></div></li><li><div class="hoverable collapsed"><span class="property token string">"properties"</span>: <button class="collapser" aria-label="expand"></button><span class="token punctuation">{</span><span class="ellipsis"></span><ul class="obj collapsible"><li><div class="hoverable collapsed"><span class="property token string">"schema"</span>: <span class="token string">&quot;Production&quot;</span></div></li></ul><span class="token punctuation">}</span><span class="token punctuation">,</span></div></li><li><div class="hoverable collapsed"><span class="property token string">"custom_properties"</span>: <span class="token punctuation">{ }</span><span class="token punctuation">,</span></div></li><li><div class="hoverable collapsed"><span class="property token string">"entity_elements"</span>: <button class="collapser" aria-label="expand"></button><span class="token punctuation">[</span><span class="ellipsis"></span><ul class="array collapsible"><li><div class="hoverable collapsed"><button class="collapser" aria-label="expand"></button><span class="token punctuation">{</span><span class="ellipsis"></span><ul class="obj collapsible"><li><div class="hoverable collapsed"><span class="property token string">"model_name"</span>: <span class="token string">&quot;ns1.Product.ProductID&quot;</span><span class="token punctuation">,</span></div></li><li><div class="hoverable collapsed"><span class="property token string">"name"</span>: <span class="token string">&quot;ProductID&quot;</span><span class="token punctuation">,</span></div></li><li><div class="hoverable collapsed"><span class="property token string">"type"</span>: <span class="token string">&quot;COLUMN&quot;</span><span class="token punctuation">,</span></div></li><li><div class="hoverable collapsed"><span class="property token string">"consumption_type"</span>: <span class="token string">&quot;field&quot;</span><span class="token punctuation">,</span></div></li><li><div class="hoverable collapsed"><span class="property token string">"description"</span>: <span class="token string">&quot;&quot;</span><span class="token punctuation">,</span></div></li><li><div class="hoverable collapsed"><span class="property token string">"active"</span>: <span class="token boolean">true</span><span class="token punctuation">,</span></div></li><li><div class="hoverable collapsed"><span class="property token string">"index"</span>: <span class="token number">1</span><span class="token punctuation">,</span></div></li><li><div class="hoverable collapsed"><span class="property token string">"data_type"</span>: <span class="token string">&quot;int&quot;</span><span class="token punctuation">,</span></div></li><li><div class="hoverable collapsed"><span class="property token string">"default_value"</span>: <span class="token string">&quot;&quot;</span><span class="token punctuation">,</span></div></li><li><div class="hoverable collapsed"><span class="property token string">"precision"</span>: <span class="token number">10</span><span class="token punctuation">,</span></div></li><li><div class="hoverable collapsed"><span class="property token string">"scale"</span>: <span class="token number">0</span><span class="token punctuation">,</span></div></li><li><div class="hoverable collapsed"><span class="property token string">"is_required"</span>: <span class="token boolean">false</span><span class="token punctuation">,</span></div></li><li><div class="hoverable collapsed"><span class="property token string">"tags"</span>: <span class="token punctuation">[ ]</span><span class="token punctuation">,</span></div></li><li><div class="hoverable collapsed"><span class="property token string">"properties"</span>: <span class="token punctuation">{ }</span><span class="token punctuation">,</span></div></li><li><div class="hoverable collapsed"><span class="property token string">"custom_properties"</span>: <span class="token punctuation">{ }</span></div></li></ul><span class="token punctuation">}</span>,</div></li><li><div class="hoverable collapsed"><button class="collapser" aria-label="expand"></button><span class="token punctuation">{</span><span class="ellipsis"></span><ul class="obj collapsible"><li><div class="hoverable collapsed"><span class="property token string">"model_name"</span>: <span class="token string">&quot;ns1.Product.Name&quot;</span><span class="token punctuation">,</span></div></li><li><div class="hoverable collapsed"><span class="property token string">"name"</span>: <span class="token string">&quot;Name&quot;</span><span class="token punctuation">,</span></div></li><li><div class="hoverable collapsed"><span class="property token string">"type"</span>: <span class="token string">&quot;COLUMN&quot;</span><span class="token punctuation">,</span></div></li><li><div class="hoverable collapsed"><span class="property token string">"consumption_type"</span>: <span class="token string">&quot;field&quot;</span><span class="token punctuation">,</span></div></li><li><div class="hoverable collapsed"><span class="property token string">"index"</span>: <span class="token number">2</span><span class="token punctuation">,</span></div></li><li><div class="hoverable collapsed"><span class="property token string">"data_type"</span>: <span class="token string">&quot;Name&quot;</span><span class="token punctuation">,</span></div></li><li><div class="hoverable collapsed"><span class="property token string">"default_value"</span>: <span class="token string">&quot;&quot;</span><span class="token punctuation">,</span></div></li><li><div class="hoverable collapsed"><span class="property token string">"max_length"</span>: <span class="token number">50</span><span class="token punctuation">,</span></div></li><li><div class="hoverable collapsed"><span class="property token string">"is_required"</span>: <span class="token boolean">false</span><span class="token punctuation">,</span></div></li><li><div class="hoverable collapsed"><span class="property token string">"properties"</span>: <span class="token punctuation">{ }</span><span class="token punctuation">,</span></div></li><li><div class="hoverable collapsed"><span class="property token string">"custom_properties"</span>: <span class="token punctuation">{ }</span></div></li></ul><span class="token punctuation">}</span>,</div></li><li><div class="hoverable collapsed"><button class="collapser" aria-label="expand"></button><span class="token punctuation">{</span><span class="ellipsis"></span><ul class="obj collapsible"><li><div class="hoverable collapsed"><span class="property token string">"model_name"</span>: <span class="token string">&quot;ns1.Product.Model&quot;</span><span class="token punctuation">,</span></div></li><li><div class="hoverable collapsed"><span class="property token string">"name"</span>: <span class="token string">&quot;Model&quot;</span><span class="token punctuation">,</span></div></li><li><div class="hoverable collapsed"><span class="property token string">"type"</span>: <span class="token string">&quot;COLUMN&quot;</span><span class="token punctuation">,</span></div></li><li><div class="hoverable collapsed"><span class="property token string">"consumption_type"</span>: <span class="token string">&quot;field&quot;</span><span class="token punctuation">,</span></div></li><li><div class="hoverable collapsed"><span class="property token string">"description"</span>: <span class="token string">&quot;Stores the model GUID that is a foreign key to another table&quot;</span><span class="token punctuation">,</span></div></li><li><div class="hoverable collapsed"><span class="property token string">"index"</span>: <span class="token number">3</span><span class="token punctuation">,</span></div></li><li><div class="hoverable collapsed"><span class="property token string">"data_type"</span>: <span class="token string">&quot;nvarchar&quot;</span><span class="token punctuation">,</span></div></li><li><div class="hoverable collapsed"><span class="property token string">"default_value"</span>: <span class="token string">&quot;&quot;</span><span class="token punctuation">,</span></div></li><li><div class="hoverable collapsed"><span class="property token string">"max_length"</span>: <span class="token number">24</span><span class="token punctuation">,</span></div></li><li><div class="hoverable collapsed"><span class="property token string">"is_required"</span>: <span class="token boolean">false</span><span class="token punctuation">,</span></div></li><li><div class="hoverable collapsed"><span class="property token string">"properties"</span>: <span class="token punctuation">{ }</span><span class="token punctuation">,</span></div></li><li><div class="hoverable collapsed"><span class="property token string">"custom_properties"</span>: <span class="token punctuation">{ }</span></div></li></ul><span class="token punctuation">}</span>,</div></li><li><div class="hoverable collapsed"><button class="collapser" aria-label="expand"></button><span class="token punctuation">{</span><span class="ellipsis"></span><ul class="obj collapsible"><li><div class="hoverable collapsed"><span class="property token string">"model_name"</span>: <span class="token string">&quot;ns1.Product.Description&quot;</span><span class="token punctuation">,</span></div></li><li><div class="hoverable collapsed"><span class="property token string">"name"</span>: <span class="token string">&quot;Description&quot;</span><span class="token punctuation">,</span></div></li><li><div class="hoverable collapsed"><span class="property token string">"type"</span>: <span class="token string">&quot;COLUMN&quot;</span><span class="token punctuation">,</span></div></li><li><div class="hoverable collapsed"><span class="property token string">"consumption_type"</span>: <span class="token string">&quot;field&quot;</span><span class="token punctuation">,</span></div></li><li><div class="hoverable collapsed"><span class="property token string">"index"</span>: <span class="token number">4</span><span class="token punctuation">,</span></div></li><li><div class="hoverable collapsed"><span class="property token string">"data_type"</span>: <span class="token string">&quot;nvarchar&quot;</span><span class="token punctuation">,</span></div></li><li><div class="hoverable collapsed"><span class="property token string">"default_value"</span>: <span class="token string">&quot;&quot;</span><span class="token punctuation">,</span></div></li><li><div class="hoverable collapsed"><span class="property token string">"max_length"</span>: <span class="token number">400</span><span class="token punctuation">,</span></div></li><li><div class="hoverable collapsed"><span class="property token string">"is_required"</span>: <span class="token boolean">false</span><span class="token punctuation">,</span></div></li><li><div class="hoverable collapsed"><span class="property token string">"properties"</span>: <span class="token punctuation">{ }</span><span class="token punctuation">,</span></div></li><li><div class="hoverable collapsed"><span class="property token string">"custom_properties"</span>: <span class="token punctuation">{ }</span></div></li></ul><span class="token punctuation">}</span>,</div></li><li><div class="hoverable collapsed"><button class="collapser" aria-label="expand"></button><span class="token punctuation">{</span><span class="ellipsis"></span><ul class="obj collapsible"><li><div class="hoverable collapsed"><span class="property token string">"model_name"</span>: <span class="token string">&quot;ns1.Product.Makeflag&quot;</span><span class="token punctuation">,</span></div></li><li><div class="hoverable collapsed"><span class="property token string">"name"</span>: <span class="token string">&quot;Makeflag&quot;</span><span class="token punctuation">,</span></div></li><li><div class="hoverable collapsed"><span class="property token string">"type"</span>: <span class="token string">&quot;COLUMN&quot;</span><span class="token punctuation">,</span></div></li><li><div class="hoverable collapsed"><span class="property token string">"consumption_type"</span>: <span class="token string">&quot;field&quot;</span><span class="token punctuation">,</span></div></li><li><div class="hoverable collapsed"><span class="property token string">"index"</span>: <span class="token number">5</span><span class="token punctuation">,</span></div></li><li><div class="hoverable collapsed"><span class="property token string">"data_type"</span>: <span class="token string">&quot;Flag&quot;</span><span class="token punctuation">,</span></div></li><li><div class="hoverable collapsed"><span class="property token string">"default_value"</span>: <span class="token string">&quot;&quot;</span><span class="token punctuation">,</span></div></li><li><div class="hoverable collapsed"><span class="property token string">"precision"</span>: <span class="token number">1</span><span class="token punctuation">,</span></div></li><li><div class="hoverable collapsed"><span class="property token string">"scale"</span>: <span class="token number">0</span><span class="token punctuation">,</span></div></li><li><div class="hoverable collapsed"><span class="property token string">"is_required"</span>: <span class="token boolean">false</span><span class="token punctuation">,</span></div></li><li><div class="hoverable collapsed"><span class="property token string">"properties"</span>: <span class="token punctuation">{ }</span><span class="token punctuation">,</span></div></li><li><div class="hoverable collapsed"><span class="property token string">"custom_properties"</span>: <span class="token punctuation">{ }</span></div></li></ul><span class="token punctuation">}</span>,</div></li><li><div class="hoverable collapsed"><button class="collapser" aria-label="expand"></button><span class="token punctuation">{</span><span class="ellipsis"></span><ul class="obj collapsible"><li><div class="hoverable collapsed"><span class="property token string">"model_name"</span>: <span class="token string">&quot;ns1.Product.PK_ProductID&quot;</span><span class="token punctuation">,</span></div></li><li><div class="hoverable collapsed"><span class="property token string">"name"</span>: <span class="token string">&quot;PK_ProductID&quot;</span><span class="token punctuation">,</span></div></li><li><div class="hoverable collapsed"><span class="property token string">"type"</span>: <span class="token string">&quot;PRIMARY-KEY&quot;</span><span class="token punctuation">,</span></div></li><li><div class="hoverable collapsed"><span class="property token string">"consumption_type"</span>: <span class="token string">&quot;key&quot;</span></div></li></ul><span class="token punctuation">}</span>,</div></li><li><div class="hoverable collapsed"><button class="collapser" aria-label="expand"></button><span class="token punctuation">{</span><span class="ellipsis"></span><ul class="obj collapsible"><li><div class="hoverable collapsed"><span class="property token string">"model_name"</span>: <span class="token string">&quot;ns1.Product.IDX_Name_Makeflag&quot;</span><span class="token punctuation">,</span></div></li><li><div class="hoverable collapsed"><span class="property token string">"name"</span>: <span class="token string">&quot;IDX_Name_MakeFlag&quot;</span><span class="token punctuation">,</span></div></li><li><div class="hoverable collapsed"><span class="property token string">"type"</span>: <span class="token string">&quot;INDEX&quot;</span><span class="token punctuation">,</span></div></li><li><div class="hoverable collapsed"><span class="property token string">"consumption_type"</span>: <span class="token string">&quot;index&quot;</span><span class="token punctuation">,</span></div></li><li><div class="hoverable collapsed"><span class="property token string">"properties"</span>: <button class="collapser" aria-label="expand"></button><span class="token punctuation">{</span><span class="ellipsis"></span><ul class="obj collapsible"><li><div class="hoverable collapsed"><span class="property token string">"is_unique"</span>: <span class="token boolean">true</span></div></li></ul><span class="token punctuation">}</span></div></li></ul><span class="token punctuation">}</span>,</div></li><li><div class="hoverable collapsed"><button class="collapser" aria-label="expand"></button><span class="token punctuation">{</span><span class="ellipsis"></span><ul class="obj collapsible"><li><div class="hoverable collapsed"><span class="property token string">"model_name"</span>: <span class="token string">&quot;ns1.Product.FK_Model&quot;</span><span class="token punctuation">,</span></div></li><li><div class="hoverable collapsed"><span class="property token string">"name"</span>: <span class="token string">&quot;FK_Model&quot;</span><span class="token punctuation">,</span></div></li><li><div class="hoverable collapsed"><span class="property token string">"type"</span>: <span class="token string">&quot;FOREIGN-KEY&quot;</span><span class="token punctuation">,</span></div></li><li><div class="hoverable collapsed"><span class="property token string">"consumption_type"</span>: <span class="token string">&quot;constraint&quot;</span><span class="token punctuation">,</span></div></li><li><div class="hoverable collapsed"><span class="property token string">"properties"</span>: <button class="collapser" aria-label="expand"></button><span class="token punctuation">{</span><span class="ellipsis"></span><ul class="obj collapsible"><li><div class="hoverable collapsed"><span class="property token string">"on_update"</span>: <span class="token string">&quot;no_action&quot;</span><span class="token punctuation">,</span></div></li><li><div class="hoverable collapsed"><span class="property token string">"on_delete"</span>: <span class="token string">&quot;no_action&quot;</span></div></li></ul><span class="token punctuation">}</span></div></li></ul><span class="token punctuation">}</span></div></li></ul><span class="token punctuation">]</span></div></li></ul><span class="token punctuation">}</span>,</div></li><li><div class="hoverable collapsed"><button class="collapser" aria-label="expand"></button><span class="token punctuation">{</span><span class="ellipsis"></span><ul class="obj collapsible"><li><div class="hoverable collapsed"><span class="property token string">"model_name"</span>: <span class="token string">&quot;ns1.ProductView&quot;</span><span class="token punctuation">,</span></div></li><li><div class="hoverable collapsed"><span class="property token string">"name"</span>: <span class="token string">&quot;ProductView&quot;</span><span class="token punctuation">,</span></div></li><li><div class="hoverable collapsed"><span class="property token string">"type"</span>: <span class="token string">&quot;VIEW&quot;</span><span class="token punctuation">,</span></div></li><li><div class="hoverable collapsed"><span class="property token string">"consumption_type"</span>: <span class="token string">&quot;dataset&quot;</span><span class="token punctuation">,</span></div></li><li><div class="hoverable collapsed"><span class="property token string">"properties"</span>: <button class="collapser" aria-label="expand"></button><span class="token punctuation">{</span><span class="ellipsis"></span><ul class="obj collapsible"><li><div class="hoverable collapsed"><span class="property token string">"schema"</span>: <span class="token string">&quot;Production&quot;</span></div></li></ul><span class="token punctuation">}</span><span class="token punctuation">,</span></div></li><li><div class="hoverable collapsed"><span class="property token string">"entity_elements"</span>: <button class="collapser" aria-label="expand"></button><span class="token punctuation">[</span><span class="ellipsis"></span><ul class="array collapsible"><li><div class="hoverable collapsed"><button class="collapser" aria-label="expand"></button><span class="token punctuation">{</span><span class="ellipsis"></span><ul class="obj collapsible"><li><div class="hoverable collapsed"><span class="property token string">"model_name"</span>: <span class="token string">&quot;ns1.ProductView.ProductID&quot;</span><span class="token punctuation">,</span></div></li><li><div class="hoverable collapsed"><span class="property token string">"name"</span>: <span class="token string">&quot;ProductID&quot;</span><span class="token punctuation">,</span></div></li><li><div class="hoverable collapsed"><span class="property token string">"type"</span>: <span class="token string">&quot;COLUMN&quot;</span><span class="token punctuation">,</span></div></li><li><div class="hoverable collapsed"><span class="property token string">"consumption_type"</span>: <span class="token string">&quot;field&quot;</span><span class="token punctuation">,</span></div></li><li><div class="hoverable collapsed"><span class="property token string">"index"</span>: <span class="token number">1</span><span class="token punctuation">,</span></div></li><li><div class="hoverable collapsed"><span class="property token string">"data_type"</span>: <span class="token string">&quot;int&quot;</span><span class="token punctuation">,</span></div></li><li><div class="hoverable collapsed"><span class="property token string">"default_value"</span>: <span class="token string">&quot;&quot;</span><span class="token punctuation">,</span></div></li><li><div class="hoverable collapsed"><span class="property token string">"precision"</span>: <span class="token number">10</span><span class="token punctuation">,</span></div></li><li><div class="hoverable collapsed"><span class="property token string">"scale"</span>: <span class="token number">0</span><span class="token punctuation">,</span></div></li><li><div class="hoverable collapsed"><span class="property token string">"is_required"</span>: <span class="token boolean">false</span><span class="token punctuation">,</span></div></li><li><div class="hoverable collapsed"><span class="property token string">"properties"</span>: <span class="token punctuation">{ }</span></div></li></ul><span class="token punctuation">}</span>,</div></li><li><div class="hoverable collapsed"><button class="collapser" aria-label="expand"></button><span class="token punctuation">{</span><span class="ellipsis"></span><ul class="obj collapsible"><li><div class="hoverable collapsed"><span class="property token string">"model_name"</span>: <span class="token string">&quot;ns1.ProductView.Name&quot;</span><span class="token punctuation">,</span></div></li><li><div class="hoverable collapsed"><span class="property token string">"name"</span>: <span class="token string">&quot;Name&quot;</span><span class="token punctuation">,</span></div></li><li><div class="hoverable collapsed"><span class="property token string">"type"</span>: <span class="token string">&quot;COLUMN&quot;</span><span class="token punctuation">,</span></div></li><li><div class="hoverable collapsed"><span class="property token string">"consumption_type"</span>: <span class="token string">&quot;field&quot;</span><span class="token punctuation">,</span></div></li><li><div class="hoverable collapsed"><span class="property token string">"index"</span>: <span class="token number">2</span><span class="token punctuation">,</span></div></li><li><div class="hoverable collapsed"><span class="property token string">"data_type"</span>: <span class="token string">&quot;Name&quot;</span><span class="token punctuation">,</span></div></li><li><div class="hoverable collapsed"><span class="property token string">"default_value"</span>: <span class="token string">&quot;&quot;</span><span class="token punctuation">,</span></div></li><li><div class="hoverable collapsed"><span class="property token string">"max_length"</span>: <span class="token number">50</span><span class="token punctuation">,</span></div></li><li><div class="hoverable collapsed"><span class="property token string">"is_required"</span>: <span class="token boolean">false</span><span class="token punctuation">,</span></div></li><li><div class="hoverable collapsed"><span class="property token string">"properties"</span>: <span class="token punctuation">{ }</span></div></li></ul><span class="token punctuation">}</span></div></li></ul><span class="token punctuation">]</span></div></li></ul><span class="token punctuation">}</span></div></li></ul><span class="token punctuation">]</span><span class="token punctuation">,</span></div></li><li><div class="hoverable "><span class="property token string">"relationships"</span>: <button class="collapser" aria-label="collapse"></button><span class="token punctuation">[</span><span class="ellipsis"></span><ul class="array collapsible"><li><div class="hoverable collapsed"><button class="collapser" aria-label="expand"></button><span class="token punctuation">{</span><span class="ellipsis"></span><ul class="obj collapsible"><li><div class="hoverable collapsed"><span class="property token string">"name"</span>: <span class="token string">&quot;PK_Production_Product_ProductID&quot;</span><span class="token punctuation">,</span></div></li><li><div class="hoverable collapsed"><span class="property token string">"type"</span>: <span class="token string">&quot;PK_REF&quot;</span><span class="token punctuation">,</span></div></li><li><div class="hoverable collapsed"><span class="property token string">"description"</span>: <span class="token string">&quot;&quot;</span><span class="token punctuation">,</span></div></li><li><div class="hoverable collapsed"><span class="property token string">"source"</span>: <span class="token string">&quot;#element/ns1.Product.PK_ProductID&quot;</span><span class="token punctuation">,</span></div></li><li><div class="hoverable collapsed"><span class="property token string">"target"</span>: <span class="token string">&quot;#element/ns1.Product.ProductID&quot;</span></div></li></ul><span class="token punctuation">}</span>,</div></li><li><div class="hoverable collapsed"><button class="collapser" aria-label="expand"></button><span class="token punctuation">{</span><span class="ellipsis"></span><ul class="obj collapsible"><li><div class="hoverable collapsed"><span class="property token string">"name"</span>: <span class="token string">&quot;IDX_Production_Product_Name_MakeFlag_Name&quot;</span><span class="token punctuation">,</span></div></li><li><div class="hoverable collapsed"><span class="property token string">"type"</span>: <span class="token string">&quot;IDX_REF&quot;</span><span class="token punctuation">,</span></div></li><li><div class="hoverable collapsed"><span class="property token string">"description"</span>: <span class="token string">&quot;&quot;</span><span class="token punctuation">,</span></div></li><li><div class="hoverable collapsed"><span class="property token string">"index"</span>: <span class="token number">10</span><span class="token punctuation">,</span></div></li><li><div class="hoverable collapsed"><span class="property token string">"source"</span>: <span class="token string">&quot;#element/ns1.Product.IDX_Name_Makeflag&quot;</span><span class="token punctuation">,</span></div></li><li><div class="hoverable collapsed"><span class="property token string">"target"</span>: <span class="token string">&quot;#element/ns1.Product.Name&quot;</span></div></li></ul><span class="token punctuation">}</span>,</div></li><li><div class="hoverable collapsed"><button class="collapser" aria-label="expand"></button><span class="token punctuation">{</span><span class="ellipsis"></span><ul class="obj collapsible"><li><div class="hoverable collapsed"><span class="property token string">"name"</span>: <span class="token string">&quot;IDX_Production_Product_Name_MakeFlag_MakeFlag&quot;</span><span class="token punctuation">,</span></div></li><li><div class="hoverable collapsed"><span class="property token string">"type"</span>: <span class="token string">&quot;IDX_REF&quot;</span><span class="token punctuation">,</span></div></li><li><div class="hoverable collapsed"><span class="property token string">"description"</span>: <span class="token string">&quot;&quot;</span><span class="token punctuation">,</span></div></li><li><div class="hoverable collapsed"><span class="property token string">"index"</span>: <span class="token number">20</span><span class="token punctuation">,</span></div></li><li><div class="hoverable collapsed"><span class="property token string">"source"</span>: <span class="token string">&quot;#element/ns1.Product.IDX_Name_Makeflag&quot;</span><span class="token punctuation">,</span></div></li><li><div class="hoverable collapsed"><span class="property token string">"target"</span>: <span class="token string">&quot;#element/ns1.Product.Makeflag&quot;</span></div></li></ul><span class="token punctuation">}</span>,</div></li><li><div class="hoverable collapsed"><button class="collapser" aria-label="expand"></button><span class="token punctuation">{</span><span class="ellipsis"></span><ul class="obj collapsible"><li><div class="hoverable collapsed"><span class="property token string">"name"</span>: <span class="token string">&quot;FK_Production_Product_Model&quot;</span><span class="token punctuation">,</span></div></li><li><div class="hoverable collapsed"><span class="property token string">"type"</span>: <span class="token string">&quot;FK_REF_SOURCE&quot;</span><span class="token punctuation">,</span></div></li><li><div class="hoverable collapsed"><span class="property token string">"description"</span>: <span class="token string">&quot;&quot;</span><span class="token punctuation">,</span></div></li><li><div class="hoverable collapsed"><span class="property token string">"source"</span>: <span class="token string">&quot;#element/ns1.Product.FK_Model&quot;</span><span class="token punctuation">,</span></div></li><li><div class="hoverable collapsed"><span class="property token string">"target"</span>: <span class="token string">&quot;#element/ns1.Product.Model&quot;</span></div></li></ul><span class="token punctuation">}</span>,</div></li><li><div class="hoverable collapsed"><button class="collapser" aria-label="expand"></button><span class="token punctuation">{</span><span class="ellipsis"></span><ul class="obj collapsible"><li><div class="hoverable collapsed"><span class="property token string">"name"</span>: <span class="token string">&quot;FK_Production_Product_Model&quot;</span><span class="token punctuation">,</span></div></li><li><div class="hoverable collapsed"><span class="property token string">"type"</span>: <span class="token string">&quot;FK_REF_TARGET&quot;</span><span class="token punctuation">,</span></div></li><li><div class="hoverable collapsed"><span class="property token string">"description"</span>: <span class="token string">&quot;&quot;</span><span class="token punctuation">,</span></div></li><li><div class="hoverable collapsed"><span class="property token string">"source"</span>: <span class="token string">&quot;#element/ns1.Product.FK_Model&quot;</span><span class="token punctuation">,</span></div></li><li><div class="hoverable collapsed"><span class="property token string">"target"</span>: <span class="token string">&quot;#element/ns1.ProductModel.ModelID&quot;</span></div></li></ul><span class="token punctuation">}</span></div></li></ul><span class="token punctuation">]</span></div></li></ul><span class="token punctuation">}</span></code></div></div></div></div></div></div></div></div><div><h3 class="sc-llIIlC fdkIVZ"> <!-- -->Response samples<!-- --> </h3><div class="sc-cTIdZS dFpYBv" data-rttabs="true"><ul class="react-tabs__tab-list" role="tablist"><li class="tab-success react-tabs__tab--selected" role="tab" id="tab«R9pccq»0" aria-selected="true" aria-disabled="false" aria-controls="panel«R9pccq»0" tabindex="0" data-rttab="true">200</li><li class="tab-error" role="tab" id="tab«R9pccq»1" aria-selected="false" aria-disabled="false" aria-controls="panel«R9pccq»1" data-rttab="true">400</li></ul><div class="react-tabs__tab-panel react-tabs__tab-panel--selected" role="tabpanel" id="panel«R9pccq»0" aria-labelledby="tab«R9pccq»0"><div><div class="sc-bXYrjy fpFYoG"><span class="sc-fUPiRJ TqlOY">Content type</span><div class="sc-bFwXsg caaBgn">application/json</div></div><div class="sc-bgpKpp kuvBhH"><div class="sc-dRHutB iAOzgg"><div class="sc-VILhF itwxyW"><button><div class="sc-fIQtvO indwFD">Copy</div></button></div><div tabindex="0" class="sc-faJlkc kOqeZB sc-eRJQtA eudsMA"><div class="redoc-json"><code><button class="collapser" aria-label="collapse"></button><span class="token punctuation">{</span><span class="ellipsis"></span><ul class="obj collapsible"><li><div class="hoverable "><span class="property token string">"new_version"</span>: <span class="token string">&quot;20250201-002&quot;</span></div></li></ul><span class="token punctuation">}</span></code></div></div></div></div></div></div><div class="react-tabs__tab-panel" role="tabpanel" id="panel«R9pccq»1" aria-labelledby="tab«R9pccq»1"></div></div></div></div></div></div><div id="tag/DataModel/operation/deleteDataModel" data-section-id="tag/DataModel/operation/deleteDataModel" class="sc-dYOLZc dRVblm"><div data-section-id="operation/deleteDataModel" id="operation/deleteDataModel" class="sc-jZhnRx tMeMv"><div class="sc-gwsNht iRoSfe"><h2 class="sc-kThouk jkwlQD"><a class="sc-kieALA jOWzfS" href="#tag/DataModel/operation/deleteDataModel" aria-label="tag/DataModel/operation/deleteDataModel"></a>Delete Data Model<!-- --> </h2><div class="sc-bkBUWa fARMpn"><div html="&lt;p&gt;This is a temporary API that allows synchronous cleanup of the full data model for the given context. It will eventually be changed into an asynchronous process in future.&lt;/p&gt;
" class="sc-faJlkc sc-fdduAw kOqeZB kEOvim"><p>This is a temporary API that allows synchronous cleanup of the full data model for the given context. It will eventually be changed into an asynchronous process in future.</p>
</div></div><div class="sc-hUPhBQ iaIgja"><div class="sc-ilCyfT gqqgtC"><h5 class="sc-elFkmj sc-gUMLBR eUkINt jefPco">Authorizations:</h5><svg class="sc-dibcMh gsIrbT" version="1.1" viewBox="0 0 24 24" x="0" xmlns="http://www.w3.org/2000/svg" y="0" aria-hidden="true"><polygon points="17.3 8.3 12 13.6 6.7 8.3 5.3 9.7 12 16.4 18.7 9.7 "></polygon></svg></div><div class="sc-jlEUfS dNXpfB"><span class="sc-iFRVkA WPZDV"><span class="sc-hFPzkB eSQudw"><i>bearerAuth</i></span></span></div></div><div><h5 class="sc-elFkmj eUkINt">path<!-- --> Parameters</h5><table class="sc-eGjrzz cGiuNU"><tbody><tr class="last "><td kind="field" title="context_id" class="sc-kHNKno sc-frmfij cDFxGr kNAbAx"><span class="sc-hLyRwt lhCIDE"></span><span class="property-name">context_id</span><div class="sc-byRegH sc-isZTFa eyIabj iUhYwd">required</div></td><td class="sc-groBii dzPbxx"><div><div><span class="sc-byRegH sc-budtlk eyIabj jGdyyu"></span><span class="sc-byRegH sc-fJvEKN eyIabj fcQkkm">string</span></div> <div><div html="&lt;p&gt;The unique identifier for the datastore.&lt;/p&gt;
" class="sc-faJlkc sc-fdduAw kOqeZB csbUjU"><p>The unique identifier for the datastore.</p>
</div></div></div></td></tr></tbody></table></div><div><h3 class="sc-gSVmdp jWGRrJ">Responses</h3><div><button class="sc-jthNAk gfsyZD"><svg class="sc-dibcMh dpIKEE" version="1.1" viewBox="0 0 24 24" x="0" xmlns="http://www.w3.org/2000/svg" y="0" aria-hidden="true"><polygon points="17.3 8.3 12 13.6 6.7 8.3 5.3 9.7 12 16.4 18.7 9.7 "></polygon></svg><strong class="sc-eOObWA ecXPYN">200<!-- --> </strong><div html="&lt;p&gt;Sucessfully deleted.&lt;/p&gt;
" class="sc-faJlkc sc-fdduAw sc-eINXvP kOqeZB kkltVg fbuMAW"><p>Sucessfully deleted.</p>
</div></button></div><div><button class="sc-jthNAk eNjFmB"><svg class="sc-dibcMh jsRtRU" version="1.1" viewBox="0 0 24 24" x="0" xmlns="http://www.w3.org/2000/svg" y="0" aria-hidden="true"><polygon points="17.3 8.3 12 13.6 6.7 8.3 5.3 9.7 12 16.4 18.7 9.7 "></polygon></svg><strong class="sc-eOObWA ecXPYN">400<!-- --> </strong><div html="&lt;p&gt;Bad Request.&lt;/p&gt;
" class="sc-faJlkc sc-fdduAw sc-eINXvP kOqeZB kkltVg fbuMAW"><p>Bad Request.</p>
</div></button></div><div><button class="sc-jthNAk kdKxoT" disabled=""><strong class="sc-eOObWA ecXPYN">401<!-- --> </strong><div html="&lt;p&gt;Authentication information is expired, missing, or invalid.
Ensure that you have authenticated correctly.&lt;/p&gt;
" class="sc-faJlkc sc-fdduAw sc-eINXvP kOqeZB kkltVg fbuMAW"><p>Authentication information is expired, missing, or invalid.
Ensure that you have authenticated correctly.</p>
</div></button></div><div><button class="sc-jthNAk kdKxoT" disabled=""><strong class="sc-eOObWA ecXPYN">403<!-- --> </strong><div html="&lt;p&gt;Authorization failed or access denied error.
Ensure that you have the necessary permissions.&lt;/p&gt;
" class="sc-faJlkc sc-fdduAw sc-eINXvP kOqeZB kkltVg fbuMAW"><p>Authorization failed or access denied error.
Ensure that you have the necessary permissions.</p>
</div></button></div></div></div><div class="sc-jMpmlX sc-hoLldG kCcXaS jpPJrn"><div class="sc-eUzzst cgvUrj"><button class="sc-bpAAPv ecNgtW"><span type="delete" class="sc-fBpBLu BkeVf http-verb delete">delete</span><span class="sc-jgoAos hgvsuc">/contexts/{context_id}/datamodel</span><svg class="sc-dibcMh NOyYc" style="margin-right:-25px" version="1.1" viewBox="0 0 24 24" x="0" xmlns="http://www.w3.org/2000/svg" y="0" aria-hidden="true"><polygon points="17.3 8.3 12 13.6 6.7 8.3 5.3 9.7 12 16.4 18.7 9.7 "></polygon></svg></button><div aria-hidden="true" class="sc-dXqqap bVkkkk"><div class="sc-ijfqWo jlEXoB"><div html="" class="sc-faJlkc sc-fdduAw kOqeZB csbUjU"></div><div tabindex="0" role="button"><div class="sc-srrxr zPpwX"><span>http://catalog.govern.svc.cluster.local</span>/contexts/{context_id}/datamodel</div></div></div><div class="sc-ijfqWo jlEXoB"><div html="" class="sc-faJlkc sc-fdduAw kOqeZB csbUjU"></div><div tabindex="0" role="button"><div class="sc-srrxr zPpwX"><span>https://ct.mgt.syniti-dev.com</span>/contexts/{context_id}/datamodel</div></div></div></div></div><div><h3 class="sc-llIIlC fdkIVZ"> <!-- -->Response samples<!-- --> </h3><div class="sc-cTIdZS dFpYBv" data-rttabs="true"><ul class="react-tabs__tab-list" role="tablist"><li class="tab-success react-tabs__tab--selected" role="tab" id="tab«R9pkcq»0" aria-selected="true" aria-disabled="false" aria-controls="panel«R9pkcq»0" tabindex="0" data-rttab="true">200</li><li class="tab-error" role="tab" id="tab«R9pkcq»1" aria-selected="false" aria-disabled="false" aria-controls="panel«R9pkcq»1" data-rttab="true">400</li></ul><div class="react-tabs__tab-panel react-tabs__tab-panel--selected" role="tabpanel" id="panel«R9pkcq»0" aria-labelledby="tab«R9pkcq»0"><div><div class="sc-bXYrjy fpFYoG"><span class="sc-fUPiRJ TqlOY">Content type</span><div class="sc-bFwXsg caaBgn">application/json</div></div><div class="sc-bgpKpp kuvBhH"><div class="sc-dRHutB iAOzgg"><div class="sc-VILhF itwxyW"><button><div class="sc-fIQtvO indwFD">Copy</div></button></div><div tabindex="0" class="sc-faJlkc kOqeZB sc-eRJQtA eudsMA"><div class="redoc-json"><code><button class="collapser" aria-label="collapse"></button><span class="token punctuation">{</span><span class="ellipsis"></span><ul class="obj collapsible"><li><div class="hoverable "><span class="property token string">"context_id"</span>: <span class="token string">&quot;dst_222&quot;</span></div></li></ul><span class="token punctuation">}</span></code></div></div></div></div></div></div><div class="react-tabs__tab-panel" role="tabpanel" id="panel«R9pkcq»1" aria-labelledby="tab«R9pkcq»1"></div></div></div></div></div></div><div id="tag/DataModel/operation/navigateDataModel" data-section-id="tag/DataModel/operation/navigateDataModel" class="sc-dYOLZc dRVblm"><div data-section-id="operation/navigateDataModel" id="operation/navigateDataModel" class="sc-jZhnRx tMeMv"><div class="sc-gwsNht iRoSfe"><h2 class="sc-kThouk jkwlQD"><a class="sc-kieALA jOWzfS" href="#tag/DataModel/operation/navigateDataModel" aria-label="tag/DataModel/operation/navigateDataModel"></a>Navigate Data Model<!-- --> </h2><div class="sc-bkBUWa fARMpn"><div html="&lt;p&gt;Navigate the data model.&lt;/p&gt;
&lt;p&gt;Each data model (database schemas, OpenAPI definitions, or OData models) has its own unique structure and semantics for organizing its contents.
With this API, you can start at the root of the data model and traverse its nested structure, accessing the contents in their original form.
The API provides a consistent interface for navigating the data model, no matter its specific semantics or internal organization.&lt;/p&gt;
&lt;p&gt;You begin by providing no parameters and fetching the root contents of the model.
From there, you can traverse along each of the root elements, exploring their nested structures and accessing the data in its original form.&lt;/p&gt;
&lt;p&gt;&lt;strong&gt;Sample navigation for Database schema&lt;/strong&gt;&lt;/p&gt;
&lt;p&gt;&lt;code&gt;/navigate?context_id=dst_1877e07898eb4581ade0b0db4bd98963111&lt;/code&gt;&lt;/p&gt;
&lt;ul&gt;
&lt;li&gt;This query returns the root entities which are tables, and views.&lt;/li&gt;
&lt;/ul&gt;
&lt;p&gt;&lt;code&gt;/navigate?context_id=dst_1877e07898eb4581ade0b0db4bd98963111&amp;amp;type=table&lt;/code&gt;&lt;/p&gt;
&lt;ul&gt;
&lt;li&gt;This query fetches only root level entities of type &amp;#39;table&amp;#39;.&lt;/li&gt;
&lt;/ul&gt;
&lt;p&gt;&lt;code&gt;/navigate?context_id=dst_1877e07898eb4581ade0b0db4bd98963111&amp;amp;parent_id=table123&lt;/code&gt;&lt;/p&gt;
&lt;ul&gt;
&lt;li&gt;This query fetches all child elements of table with id &amp;#39;table123&amp;#39;.&lt;/li&gt;
&lt;/ul&gt;
&lt;p&gt;&lt;code&gt;/navigate?context_id=dst_1877e07898eb4581ade0b0db4bd98963111&amp;amp;parent_id=table123&amp;amp;type=column&lt;/code&gt;&lt;/p&gt;
&lt;ul&gt;
&lt;li&gt;This query fetches only column child elements of table with id &amp;#39;table123&amp;#39;. It automatically resolves all references ($ref) and any relationships where column is the source.&lt;/li&gt;
&lt;/ul&gt;
&lt;p&gt;&lt;code&gt;/navigate?context_id=dst_1877e07898eb4581ade0b0db4bd98963111&amp;amp;parent_id=table123&amp;amp;type=primary-key&amp;amp;type=foreign-key&amp;amp;type=index&lt;/code&gt;&lt;/p&gt;
&lt;ul&gt;
&lt;li&gt;This query fetches only constraint elements of table with id &amp;#39;table123&amp;#39;. It automatically resolves all relationships where column is the source.&lt;/li&gt;
&lt;/ul&gt;
&lt;p&gt;&lt;strong&gt;Sample navigation for Open API&lt;/strong&gt;&lt;/p&gt;
&lt;p&gt;&lt;code&gt;/navigate&lt;/code&gt;&lt;/p&gt;
&lt;ul&gt;
&lt;li&gt;This query returns the root entities which are all operations. For e.g. &amp;#39;addPet&amp;#39;&lt;/li&gt;
&lt;/ul&gt;
&lt;p&gt;&lt;code&gt;/navigate?type=schemas&lt;/code&gt;&lt;/p&gt;
&lt;ul&gt;
&lt;li&gt;This query fetches all root level entities which are the schemas defined in the Open API. For e.g. &amp;#39;Pet&amp;#39;&lt;/li&gt;
&lt;/ul&gt;
&lt;p&gt;&lt;code&gt;/navigate?parent_id=operation1&lt;/code&gt;&lt;/p&gt;
&lt;ul&gt;
&lt;li&gt;This query fetches all child elements (parameter, request, response, security) of operation with id &amp;#39;operation1&amp;#39;.&lt;/li&gt;
&lt;/ul&gt;
&lt;p&gt;&lt;code&gt;/navigate?parent_id=petschema1&lt;/code&gt;&lt;/p&gt;
&lt;ul&gt;
&lt;li&gt;This query fetches all child elements of Pet schema with id &amp;#39;petschema1&amp;#39;. It automatically resolves all references ($ref).&lt;/li&gt;
&lt;/ul&gt;
&lt;p&gt;&lt;strong&gt;Sample navigation for ODATA&lt;/strong&gt;&lt;/p&gt;
&lt;p&gt;&lt;code&gt;/navigate&lt;/code&gt;&lt;/p&gt;
&lt;ul&gt;
&lt;li&gt;This query returns the root entities which are all EntitySet, FunctionImport, ActionImport. For e.g. &amp;#39;People&amp;#39;.&lt;/li&gt;
&lt;/ul&gt;
&lt;p&gt;&lt;code&gt;/navigate?type=entity_type&lt;/code&gt;&lt;/p&gt;
&lt;ul&gt;
&lt;li&gt;This query fetches all root level entities which are the EntityType defined in the ODATA. For e.g. &amp;#39;Person&amp;#39;. We can also fetch more that one type : enum_type | complex_type | entity_type | function | action.&lt;/li&gt;
&lt;/ul&gt;
&lt;p&gt;&lt;code&gt;/navigate?parent_id=entityset1&lt;/code&gt;&lt;/p&gt;
&lt;ul&gt;
&lt;li&gt;This query fetches all child elements (navigation bindings) of EntitySet with id &amp;#39;entityset1&amp;#39;.&lt;/li&gt;
&lt;/ul&gt;
&lt;p&gt;&lt;code&gt;/navigate?parent_id=entitytype1&lt;/code&gt;&lt;/p&gt;
&lt;ul&gt;
&lt;li&gt;This query fetches all child elements (properties) of EntityType with id &amp;#39;entitytype1&amp;#39;. It automatically resolves all references ($ref).&lt;/li&gt;
&lt;/ul&gt;
&lt;p&gt;&lt;strong&gt;Sample navigation for Business Catalog schema&lt;/strong&gt;&lt;/p&gt;
&lt;p&gt;&lt;code&gt;/navigate&lt;/code&gt;&lt;/p&gt;
&lt;ul&gt;
&lt;li&gt;This query returns the root dataset definitions (UI, QUERY, REPORT) and record type definitions&lt;/li&gt;
&lt;/ul&gt;
&lt;p&gt;&lt;code&gt;/navigate?type=ui&lt;/code&gt;&lt;/p&gt;
&lt;ul&gt;
&lt;li&gt;This query fetches only root level dataset definitions of type &amp;#39;UI&amp;#39;&lt;/li&gt;
&lt;/ul&gt;
&lt;p&gt;&lt;code&gt;/navigate?parent_id=dataset_def_ui_001&lt;/code&gt;&lt;/p&gt;
&lt;ul&gt;
&lt;li&gt;This query fetches all child elements of dataset definition with id &amp;#39;dataset_def_ui_001&amp;#39;.&lt;/li&gt;
&lt;/ul&gt;
&lt;p&gt;&lt;code&gt;/navigate?parent_id=dataset_def_ui_001&amp;amp;type=property&lt;/code&gt;&lt;/p&gt;
&lt;ul&gt;
&lt;li&gt;This query fetches only property child elements of dataset definition with id &amp;#39;dataset_def_ui_001&amp;#39;. It automatically resolves all references.&lt;/li&gt;
&lt;/ul&gt;
" class="sc-faJlkc sc-fdduAw kOqeZB kEOvim"><p>Navigate the data model.</p>
<p>Each data model (database schemas, OpenAPI definitions, or OData models) has its own unique structure and semantics for organizing its contents.
With this API, you can start at the root of the data model and traverse its nested structure, accessing the contents in their original form.
The API provides a consistent interface for navigating the data model, no matter its specific semantics or internal organization.</p>
<p>You begin by providing no parameters and fetching the root contents of the model.
From there, you can traverse along each of the root elements, exploring their nested structures and accessing the data in its original form.</p>
<p><strong>Sample navigation for Database schema</strong></p>
<p><code>/navigate?context_id=dst_1877e07898eb4581ade0b0db4bd98963111</code></p>
<ul>
<li>This query returns the root entities which are tables, and views.</li>
</ul>
<p><code>/navigate?context_id=dst_1877e07898eb4581ade0b0db4bd98963111&amp;type=table</code></p>
<ul>
<li>This query fetches only root level entities of type &#39;table&#39;.</li>
</ul>
<p><code>/navigate?context_id=dst_1877e07898eb4581ade0b0db4bd98963111&amp;parent_id=table123</code></p>
<ul>
<li>This query fetches all child elements of table with id &#39;table123&#39;.</li>
</ul>
<p><code>/navigate?context_id=dst_1877e07898eb4581ade0b0db4bd98963111&amp;parent_id=table123&amp;type=column</code></p>
<ul>
<li>This query fetches only column child elements of table with id &#39;table123&#39;. It automatically resolves all references ($ref) and any relationships where column is the source.</li>
</ul>
<p><code>/navigate?context_id=dst_1877e07898eb4581ade0b0db4bd98963111&amp;parent_id=table123&amp;type=primary-key&amp;type=foreign-key&amp;type=index</code></p>
<ul>
<li>This query fetches only constraint elements of table with id &#39;table123&#39;. It automatically resolves all relationships where column is the source.</li>
</ul>
<p><strong>Sample navigation for Open API</strong></p>
<p><code>/navigate</code></p>
<ul>
<li>This query returns the root entities which are all operations. For e.g. &#39;addPet&#39;</li>
</ul>
<p><code>/navigate?type=schemas</code></p>
<ul>
<li>This query fetches all root level entities which are the schemas defined in the Open API. For e.g. &#39;Pet&#39;</li>
</ul>
<p><code>/navigate?parent_id=operation1</code></p>
<ul>
<li>This query fetches all child elements (parameter, request, response, security) of operation with id &#39;operation1&#39;.</li>
</ul>
<p><code>/navigate?parent_id=petschema1</code></p>
<ul>
<li>This query fetches all child elements of Pet schema with id &#39;petschema1&#39;. It automatically resolves all references ($ref).</li>
</ul>
<p><strong>Sample navigation for ODATA</strong></p>
<p><code>/navigate</code></p>
<ul>
<li>This query returns the root entities which are all EntitySet, FunctionImport, ActionImport. For e.g. &#39;People&#39;.</li>
</ul>
<p><code>/navigate?type=entity_type</code></p>
<ul>
<li>This query fetches all root level entities which are the EntityType defined in the ODATA. For e.g. &#39;Person&#39;. We can also fetch more that one type : enum_type | complex_type | entity_type | function | action.</li>
</ul>
<p><code>/navigate?parent_id=entityset1</code></p>
<ul>
<li>This query fetches all child elements (navigation bindings) of EntitySet with id &#39;entityset1&#39;.</li>
</ul>
<p><code>/navigate?parent_id=entitytype1</code></p>
<ul>
<li>This query fetches all child elements (properties) of EntityType with id &#39;entitytype1&#39;. It automatically resolves all references ($ref).</li>
</ul>
<p><strong>Sample navigation for Business Catalog schema</strong></p>
<p><code>/navigate</code></p>
<ul>
<li>This query returns the root dataset definitions (UI, QUERY, REPORT) and record type definitions</li>
</ul>
<p><code>/navigate?type=ui</code></p>
<ul>
<li>This query fetches only root level dataset definitions of type &#39;UI&#39;</li>
</ul>
<p><code>/navigate?parent_id=dataset_def_ui_001</code></p>
<ul>
<li>This query fetches all child elements of dataset definition with id &#39;dataset_def_ui_001&#39;.</li>
</ul>
<p><code>/navigate?parent_id=dataset_def_ui_001&amp;type=property</code></p>
<ul>
<li>This query fetches only property child elements of dataset definition with id &#39;dataset_def_ui_001&#39;. It automatically resolves all references.</li>
</ul>
</div></div><div class="sc-hUPhBQ iaIgja"><div class="sc-ilCyfT gqqgtC"><h5 class="sc-elFkmj sc-gUMLBR eUkINt jefPco">Authorizations:</h5><svg class="sc-dibcMh gsIrbT" version="1.1" viewBox="0 0 24 24" x="0" xmlns="http://www.w3.org/2000/svg" y="0" aria-hidden="true"><polygon points="17.3 8.3 12 13.6 6.7 8.3 5.3 9.7 12 16.4 18.7 9.7 "></polygon></svg></div><div class="sc-jlEUfS dNXpfB"><span class="sc-iFRVkA WPZDV"><span class="sc-hFPzkB eSQudw"><i>bearerAuth</i></span></span></div></div><div><h5 class="sc-elFkmj eUkINt">path<!-- --> Parameters</h5><table class="sc-eGjrzz cGiuNU"><tbody><tr class="last "><td kind="field" title="context_id" class="sc-kHNKno sc-frmfij cDFxGr kNAbAx"><span class="sc-hLyRwt lhCIDE"></span><span class="property-name">context_id</span><div class="sc-byRegH sc-isZTFa eyIabj iUhYwd">required</div></td><td class="sc-groBii dzPbxx"><div><div><span class="sc-byRegH sc-budtlk eyIabj jGdyyu"></span><span class="sc-byRegH sc-fJvEKN eyIabj fcQkkm">string</span></div> <div><div html="&lt;p&gt;The identifier of the context for which the data model needs to be fetched.
The context can be a Datastore or a Subject Area. This is a mandatory parameter.&lt;/p&gt;
" class="sc-faJlkc sc-fdduAw kOqeZB csbUjU"><p>The identifier of the context for which the data model needs to be fetched.
The context can be a Datastore or a Subject Area. This is a mandatory parameter.</p>
</div></div></div></td></tr></tbody></table></div><div><h5 class="sc-elFkmj eUkINt">query<!-- --> Parameters</h5><table class="sc-eGjrzz cGiuNU"><tbody><tr class=""><td kind="field" title="parent_id" class="sc-kHNKno sc-frmfij cDFxGr kNAbAx"><span class="sc-hLyRwt lhCIDE"></span><span class="property-name">parent_id</span></td><td class="sc-groBii dzPbxx"><div><div><span class="sc-byRegH sc-budtlk eyIabj jGdyyu"></span><span class="sc-byRegH sc-fJvEKN eyIabj fcQkkm">string</span></div> <div><div html="&lt;p&gt;The identifier of the parent under which the child elements needs to be fetched.
If not specified, then the root level model elements are fetched.&lt;/p&gt;
" class="sc-faJlkc sc-fdduAw kOqeZB csbUjU"><p>The identifier of the parent under which the child elements needs to be fetched.
If not specified, then the root level model elements are fetched.</p>
</div></div></div></td></tr><tr class=""><td kind="field" title="type" class="sc-kHNKno sc-frmfij cDFxGr kNAbAx"><span class="sc-hLyRwt lhCIDE"></span><span class="property-name">type</span></td><td class="sc-groBii dzPbxx"><div><div><span class="sc-byRegH sc-budtlk eyIabj jGdyyu">Array of </span><span class="sc-byRegH sc-fJvEKN eyIabj fcQkkm">strings</span></div> <div><div html="&lt;p&gt;Specify the type of contents to be fetched. Its value is specific to each type of data model being explored. &lt;br&gt;&lt;br&gt;Database: table, view, column, primary-key, index, foreign-key &lt;br&gt;Open API: operation, schema, security_scheme &lt;br&gt;ODATA: entity_set, function_import, action_import, entity_type, enum_type, complex_type, function, action&lt;/p&gt;
" class="sc-faJlkc sc-fdduAw kOqeZB csbUjU"><p>Specify the type of contents to be fetched. Its value is specific to each type of data model being explored. <br><br>Database: table, view, column, primary-key, index, foreign-key <br>Open API: operation, schema, security_scheme <br>ODATA: entity_set, function_import, action_import, entity_type, enum_type, complex_type, function, action</p>
</div></div></div></td></tr><tr class=""><td kind="field" title="first" class="sc-kHNKno sc-frmfij cDFxGr kNAbAx"><span class="sc-hLyRwt lhCIDE"></span><span class="property-name">first</span></td><td class="sc-groBii dzPbxx"><div><div><span class="sc-byRegH sc-budtlk eyIabj jGdyyu"></span><span class="sc-byRegH sc-fJvEKN eyIabj fcQkkm">integer</span></div> <div><div html="&lt;p&gt;Number of requested results for paging&lt;/p&gt;
" class="sc-faJlkc sc-fdduAw kOqeZB csbUjU"><p>Number of requested results for paging</p>
</div></div></div></td></tr><tr class=""><td kind="field" title="offset" class="sc-kHNKno sc-frmfij cDFxGr kNAbAx"><span class="sc-hLyRwt lhCIDE"></span><span class="property-name">offset</span></td><td class="sc-groBii dzPbxx"><div><div><span class="sc-byRegH sc-budtlk eyIabj jGdyyu"></span><span class="sc-byRegH sc-fJvEKN eyIabj fcQkkm">string</span></div> <div><div html="&lt;p&gt;Encoded value used to indicate where in the result set to begin.
This is used for paging. To fetch the next page of results, use the value returned in the &lt;code&gt;next_token&lt;/code&gt; field of the previous response.&lt;/p&gt;
" class="sc-faJlkc sc-fdduAw kOqeZB csbUjU"><p>Encoded value used to indicate where in the result set to begin.
This is used for paging. To fetch the next page of results, use the value returned in the <code>next_token</code> field of the previous response.</p>
</div></div></div></td></tr><tr class="last "><td kind="field" title="sort" class="sc-kHNKno sc-frmfij cDFxGr kNAbAx"><span class="sc-hLyRwt lhCIDE"></span><span class="property-name">sort</span></td><td class="sc-groBii dzPbxx"><div><div><span class="sc-byRegH sc-budtlk eyIabj jGdyyu"></span><span class="sc-byRegH sc-fJvEKN eyIabj fcQkkm">string</span></div> <div><div html="&lt;p&gt;A base64 encoded sort value&lt;/p&gt;
" class="sc-faJlkc sc-fdduAw kOqeZB csbUjU"><p>A base64 encoded sort value</p>
</div></div></div></td></tr></tbody></table></div><div><h3 class="sc-gSVmdp jWGRrJ">Responses</h3><div><button class="sc-jthNAk gfsyZD"><svg class="sc-dibcMh dpIKEE" version="1.1" viewBox="0 0 24 24" x="0" xmlns="http://www.w3.org/2000/svg" y="0" aria-hidden="true"><polygon points="17.3 8.3 12 13.6 6.7 8.3 5.3 9.7 12 16.4 18.7 9.7 "></polygon></svg><strong class="sc-eOObWA ecXPYN">200<!-- --> </strong><div html="&lt;p&gt;Model contents list.&lt;/p&gt;
" class="sc-faJlkc sc-fdduAw sc-eINXvP kOqeZB kkltVg fbuMAW"><p>Model contents list.</p>
</div></button></div><div><button class="sc-jthNAk eNjFmB"><svg class="sc-dibcMh jsRtRU" version="1.1" viewBox="0 0 24 24" x="0" xmlns="http://www.w3.org/2000/svg" y="0" aria-hidden="true"><polygon points="17.3 8.3 12 13.6 6.7 8.3 5.3 9.7 12 16.4 18.7 9.7 "></polygon></svg><strong class="sc-eOObWA ecXPYN">400<!-- --> </strong><div html="&lt;p&gt;Bad Request&lt;/p&gt;
" class="sc-faJlkc sc-fdduAw sc-eINXvP kOqeZB kkltVg fbuMAW"><p>Bad Request</p>
</div></button></div><div><button class="sc-jthNAk kdKxoT" disabled=""><strong class="sc-eOObWA ecXPYN">401<!-- --> </strong><div html="&lt;p&gt;Authentication information is expired, missing, or invalid.
Ensure that you have authenticated correctly.&lt;/p&gt;
" class="sc-faJlkc sc-fdduAw sc-eINXvP kOqeZB kkltVg fbuMAW"><p>Authentication information is expired, missing, or invalid.
Ensure that you have authenticated correctly.</p>
</div></button></div><div><button class="sc-jthNAk kdKxoT" disabled=""><strong class="sc-eOObWA ecXPYN">403<!-- --> </strong><div html="&lt;p&gt;Authorization failed or access denied error.
Ensure that you have the necessary permissions.&lt;/p&gt;
" class="sc-faJlkc sc-fdduAw sc-eINXvP kOqeZB kkltVg fbuMAW"><p>Authorization failed or access denied error.
Ensure that you have the necessary permissions.</p>
</div></button></div></div></div><div class="sc-jMpmlX sc-hoLldG kCcXaS jpPJrn"><div class="sc-eUzzst cgvUrj"><button class="sc-bpAAPv ecNgtW"><span type="get" class="sc-fBpBLu deMhiD http-verb get">get</span><span class="sc-jgoAos hgvsuc">/contexts/{context_id}/datamodel/navigation</span><svg class="sc-dibcMh NOyYc" style="margin-right:-25px" version="1.1" viewBox="0 0 24 24" x="0" xmlns="http://www.w3.org/2000/svg" y="0" aria-hidden="true"><polygon points="17.3 8.3 12 13.6 6.7 8.3 5.3 9.7 12 16.4 18.7 9.7 "></polygon></svg></button><div aria-hidden="true" class="sc-dXqqap bVkkkk"><div class="sc-ijfqWo jlEXoB"><div html="" class="sc-faJlkc sc-fdduAw kOqeZB csbUjU"></div><div tabindex="0" role="button"><div class="sc-srrxr zPpwX"><span>http://catalog.govern.svc.cluster.local</span>/contexts/{context_id}/datamodel/navigation</div></div></div><div class="sc-ijfqWo jlEXoB"><div html="" class="sc-faJlkc sc-fdduAw kOqeZB csbUjU"></div><div tabindex="0" role="button"><div class="sc-srrxr zPpwX"><span>https://ct.mgt.syniti-dev.com</span>/contexts/{context_id}/datamodel/navigation</div></div></div></div></div><div><h3 class="sc-llIIlC fdkIVZ"> <!-- -->Response samples<!-- --> </h3><div class="sc-cTIdZS dFpYBv" data-rttabs="true"><ul class="react-tabs__tab-list" role="tablist"><li class="tab-success react-tabs__tab--selected" role="tab" id="tab«R9pscq»0" aria-selected="true" aria-disabled="false" aria-controls="panel«R9pscq»0" tabindex="0" data-rttab="true">200</li><li class="tab-error" role="tab" id="tab«R9pscq»1" aria-selected="false" aria-disabled="false" aria-controls="panel«R9pscq»1" data-rttab="true">400</li></ul><div class="react-tabs__tab-panel react-tabs__tab-panel--selected" role="tabpanel" id="panel«R9pscq»0" aria-labelledby="tab«R9pscq»0"><div><div class="sc-bXYrjy fpFYoG"><span class="sc-fUPiRJ TqlOY">Content type</span><div class="sc-bFwXsg caaBgn">application/json</div></div><div class="sc-bgpKpp kuvBhH"><div class="sc-dRHutB iAOzgg"><div class="sc-VILhF itwxyW"><button><div class="sc-fIQtvO indwFD">Copy</div></button><button> Expand all </button><button> Collapse all </button></div><div tabindex="0" class="sc-faJlkc kOqeZB sc-eRJQtA eudsMA"><div class="redoc-json"><code><button class="collapser" aria-label="collapse"></button><span class="token punctuation">{</span><span class="ellipsis"></span><ul class="obj collapsible"><li><div class="hoverable "><span class="property token string">"results"</span>: <button class="collapser" aria-label="collapse"></button><span class="token punctuation">{</span><span class="ellipsis"></span><ul class="obj collapsible"><li><div class="hoverable collapsed"><span class="property token string">"entities"</span>: <button class="collapser" aria-label="expand"></button><span class="token punctuation">[</span><span class="ellipsis"></span><ul class="array collapsible"><li><div class="hoverable collapsed"><button class="collapser" aria-label="expand"></button><span class="token punctuation">{</span><span class="ellipsis"></span><ul class="obj collapsible"><li><div class="hoverable collapsed"><span class="property token string">"id"</span>: <span class="token string">&quot;string&quot;</span><span class="token punctuation">,</span></div></li><li><div class="hoverable collapsed"><span class="property token string">"model_name"</span>: <span class="token string">&quot;string&quot;</span><span class="token punctuation">,</span></div></li><li><div class="hoverable collapsed"><span class="property token string">"name"</span>: <span class="token string">&quot;string&quot;</span><span class="token punctuation">,</span></div></li><li><div class="hoverable collapsed"><span class="property token string">"description"</span>: <span class="token string">&quot;string&quot;</span><span class="token punctuation">,</span></div></li><li><div class="hoverable collapsed"><span class="property token string">"type"</span>: <span class="token string">&quot;string&quot;</span><span class="token punctuation">,</span></div></li><li><div class="hoverable collapsed"><span class="property token string">"consumption_type"</span>: <span class="token string">&quot;string&quot;</span><span class="token punctuation">,</span></div></li><li><div class="hoverable collapsed"><span class="property token string">"index"</span>: <span class="token number">0</span><span class="token punctuation">,</span></div></li><li><div class="hoverable collapsed"><span class="property token string">"properties"</span>: <span class="token punctuation">{ }</span><span class="token punctuation">,</span></div></li><li><div class="hoverable collapsed"><span class="property token string">"custom_properties"</span>: <span class="token punctuation">{ }</span><span class="token punctuation">,</span></div></li><li><div class="hoverable collapsed"><span class="property token string">"tags"</span>: <button class="collapser" aria-label="expand"></button><span class="token punctuation">[</span><span class="ellipsis"></span><ul class="array collapsible"><li><div class="hoverable collapsed"><span class="token string">&quot;string&quot;</span></div></li></ul><span class="token punctuation">]</span><span class="token punctuation">,</span></div></li><li><div class="hoverable collapsed"><span class="property token string">"created_by"</span>: <span class="token string">&quot;string&quot;</span><span class="token punctuation">,</span></div></li><li><div class="hoverable collapsed"><span class="property token string">"entity_elements"</span>: <button class="collapser" aria-label="expand"></button><span class="token punctuation">[</span><span class="ellipsis"></span><ul class="array collapsible"><li><div class="hoverable collapsed"><button class="collapser" aria-label="expand"></button><span class="token punctuation">{</span><span class="ellipsis"></span><ul class="obj collapsible"><li><div class="hoverable collapsed"><span class="property token string">"id"</span>: <span class="token string">&quot;string&quot;</span><span class="token punctuation">,</span></div></li><li><div class="hoverable collapsed"><span class="property token string">"model_name"</span>: <span class="token string">&quot;string&quot;</span><span class="token punctuation">,</span></div></li><li><div class="hoverable collapsed"><span class="property token string">"name"</span>: <span class="token string">&quot;string&quot;</span><span class="token punctuation">,</span></div></li><li><div class="hoverable collapsed"><span class="property token string">"description"</span>: <span class="token string">&quot;string&quot;</span><span class="token punctuation">,</span></div></li><li><div class="hoverable collapsed"><span class="property token string">"type"</span>: <span class="token string">&quot;string&quot;</span><span class="token punctuation">,</span></div></li><li><div class="hoverable collapsed"><span class="property token string">"consumption_type"</span>: <span class="token string">&quot;string&quot;</span><span class="token punctuation">,</span></div></li><li><div class="hoverable collapsed"><span class="property token string">"index"</span>: <span class="token number">0</span><span class="token punctuation">,</span></div></li><li><div class="hoverable collapsed"><span class="property token string">"data_type"</span>: <span class="token string">&quot;string&quot;</span><span class="token punctuation">,</span></div></li><li><div class="hoverable collapsed"><span class="property token string">"default_value"</span>: <span class="token string">&quot;string&quot;</span><span class="token punctuation">,</span></div></li><li><div class="hoverable collapsed"><span class="property token string">"enum"</span>: <button class="collapser" aria-label="expand"></button><span class="token punctuation">[</span><span class="ellipsis"></span><ul class="array collapsible"><li><div class="hoverable collapsed"><span class="token string">&quot;string&quot;</span></div></li></ul><span class="token punctuation">]</span><span class="token punctuation">,</span></div></li><li><div class="hoverable collapsed"><span class="property token string">"is_required"</span>: <span class="token boolean">true</span><span class="token punctuation">,</span></div></li><li><div class="hoverable collapsed"><span class="property token string">"is_array"</span>: <span class="token boolean">true</span><span class="token punctuation">,</span></div></li><li><div class="hoverable collapsed"><span class="property token string">"max_length"</span>: <span class="token number">0</span><span class="token punctuation">,</span></div></li><li><div class="hoverable collapsed"><span class="property token string">"precision"</span>: <span class="token number">0</span><span class="token punctuation">,</span></div></li><li><div class="hoverable collapsed"><span class="property token string">"scale"</span>: <span class="token number">0</span><span class="token punctuation">,</span></div></li><li><div class="hoverable collapsed"><span class="property token string">"properties"</span>: <span class="token punctuation">{ }</span><span class="token punctuation">,</span></div></li><li><div class="hoverable collapsed"><span class="property token string">"custom_properties"</span>: <span class="token punctuation">{ }</span><span class="token punctuation">,</span></div></li><li><div class="hoverable collapsed"><span class="property token string">"tags"</span>: <button class="collapser" aria-label="expand"></button><span class="token punctuation">[</span><span class="ellipsis"></span><ul class="array collapsible"><li><div class="hoverable collapsed"><span class="token string">&quot;string&quot;</span></div></li></ul><span class="token punctuation">]</span><span class="token punctuation">,</span></div></li><li><div class="hoverable collapsed"><span class="property token string">"created_by"</span>: <span class="token string">&quot;string&quot;</span></div></li></ul><span class="token punctuation">}</span></div></li></ul><span class="token punctuation">]</span></div></li></ul><span class="token punctuation">}</span></div></li></ul><span class="token punctuation">]</span><span class="token punctuation">,</span></div></li><li><div class="hoverable collapsed"><span class="property token string">"entity_elements"</span>: <button class="collapser" aria-label="expand"></button><span class="token punctuation">[</span><span class="ellipsis"></span><ul class="array collapsible"><li><div class="hoverable collapsed"><button class="collapser" aria-label="expand"></button><span class="token punctuation">{</span><span class="ellipsis"></span><ul class="obj collapsible"><li><div class="hoverable collapsed"><span class="property token string">"id"</span>: <span class="token string">&quot;string&quot;</span><span class="token punctuation">,</span></div></li><li><div class="hoverable collapsed"><span class="property token string">"model_name"</span>: <span class="token string">&quot;string&quot;</span><span class="token punctuation">,</span></div></li><li><div class="hoverable collapsed"><span class="property token string">"name"</span>: <span class="token string">&quot;string&quot;</span><span class="token punctuation">,</span></div></li><li><div class="hoverable collapsed"><span class="property token string">"description"</span>: <span class="token string">&quot;string&quot;</span><span class="token punctuation">,</span></div></li><li><div class="hoverable collapsed"><span class="property token string">"type"</span>: <span class="token string">&quot;string&quot;</span><span class="token punctuation">,</span></div></li><li><div class="hoverable collapsed"><span class="property token string">"consumption_type"</span>: <span class="token string">&quot;string&quot;</span><span class="token punctuation">,</span></div></li><li><div class="hoverable collapsed"><span class="property token string">"index"</span>: <span class="token number">0</span><span class="token punctuation">,</span></div></li><li><div class="hoverable collapsed"><span class="property token string">"data_type"</span>: <span class="token string">&quot;string&quot;</span><span class="token punctuation">,</span></div></li><li><div class="hoverable collapsed"><span class="property token string">"default_value"</span>: <span class="token string">&quot;string&quot;</span><span class="token punctuation">,</span></div></li><li><div class="hoverable collapsed"><span class="property token string">"enum"</span>: <button class="collapser" aria-label="expand"></button><span class="token punctuation">[</span><span class="ellipsis"></span><ul class="array collapsible"><li><div class="hoverable collapsed"><span class="token string">&quot;string&quot;</span></div></li></ul><span class="token punctuation">]</span><span class="token punctuation">,</span></div></li><li><div class="hoverable collapsed"><span class="property token string">"is_required"</span>: <span class="token boolean">true</span><span class="token punctuation">,</span></div></li><li><div class="hoverable collapsed"><span class="property token string">"is_array"</span>: <span class="token boolean">true</span><span class="token punctuation">,</span></div></li><li><div class="hoverable collapsed"><span class="property token string">"max_length"</span>: <span class="token number">0</span><span class="token punctuation">,</span></div></li><li><div class="hoverable collapsed"><span class="property token string">"precision"</span>: <span class="token number">0</span><span class="token punctuation">,</span></div></li><li><div class="hoverable collapsed"><span class="property token string">"scale"</span>: <span class="token number">0</span><span class="token punctuation">,</span></div></li><li><div class="hoverable collapsed"><span class="property token string">"properties"</span>: <span class="token punctuation">{ }</span><span class="token punctuation">,</span></div></li><li><div class="hoverable collapsed"><span class="property token string">"custom_properties"</span>: <span class="token punctuation">{ }</span><span class="token punctuation">,</span></div></li><li><div class="hoverable collapsed"><span class="property token string">"tags"</span>: <button class="collapser" aria-label="expand"></button><span class="token punctuation">[</span><span class="ellipsis"></span><ul class="array collapsible"><li><div class="hoverable collapsed"><span class="token string">&quot;string&quot;</span></div></li></ul><span class="token punctuation">]</span><span class="token punctuation">,</span></div></li><li><div class="hoverable collapsed"><span class="property token string">"created_by"</span>: <span class="token string">&quot;string&quot;</span></div></li></ul><span class="token punctuation">}</span></div></li></ul><span class="token punctuation">]</span><span class="token punctuation">,</span></div></li><li><div class="hoverable collapsed"><span class="property token string">"relationships"</span>: <button class="collapser" aria-label="expand"></button><span class="token punctuation">[</span><span class="ellipsis"></span><ul class="array collapsible"><li><div class="hoverable collapsed"><button class="collapser" aria-label="expand"></button><span class="token punctuation">{</span><span class="ellipsis"></span><ul class="obj collapsible"><li><div class="hoverable collapsed"><span class="property token string">"id"</span>: <span class="token string">&quot;string&quot;</span><span class="token punctuation">,</span></div></li><li><div class="hoverable collapsed"><span class="property token string">"name"</span>: <span class="token string">&quot;string&quot;</span><span class="token punctuation">,</span></div></li><li><div class="hoverable collapsed"><span class="property token string">"description"</span>: <span class="token string">&quot;string&quot;</span><span class="token punctuation">,</span></div></li><li><div class="hoverable collapsed"><span class="property token string">"type"</span>: <span class="token string">&quot;string&quot;</span><span class="token punctuation">,</span></div></li><li><div class="hoverable collapsed"><span class="property token string">"index"</span>: <span class="token number">0</span><span class="token punctuation">,</span></div></li><li><div class="hoverable collapsed"><span class="property token string">"source"</span>: <button class="collapser" aria-label="expand"></button><span class="token punctuation">{</span><span class="ellipsis"></span><ul class="obj collapsible"><li><div class="hoverable collapsed"><span class="property token string">"id"</span>: <span class="token string">&quot;cpt_xkHRtSJrPh4JLgeZQLfA7VdLsbi&quot;</span><span class="token punctuation">,</span></div></li><li><div class="hoverable collapsed"><span class="property token string">"$ref"</span>: <span class="token string">&quot;string&quot;</span></div></li></ul><span class="token punctuation">}</span><span class="token punctuation">,</span></div></li><li><div class="hoverable collapsed"><span class="property token string">"target"</span>: <button class="collapser" aria-label="expand"></button><span class="token punctuation">{</span><span class="ellipsis"></span><ul class="obj collapsible"><li><div class="hoverable collapsed"><span class="property token string">"id"</span>: <span class="token string">&quot;cpt_xkHRtSJrPh4JLgeZQLfA7VdLsbi&quot;</span><span class="token punctuation">,</span></div></li><li><div class="hoverable collapsed"><span class="property token string">"$ref"</span>: <span class="token string">&quot;string&quot;</span></div></li></ul><span class="token punctuation">}</span><span class="token punctuation">,</span></div></li><li><div class="hoverable collapsed"><span class="property token string">"properties"</span>: <span class="token punctuation">{ }</span><span class="token punctuation">,</span></div></li><li><div class="hoverable collapsed"><span class="property token string">"tags"</span>: <button class="collapser" aria-label="expand"></button><span class="token punctuation">[</span><span class="ellipsis"></span><ul class="array collapsible"><li><div class="hoverable collapsed"><span class="token string">&quot;string&quot;</span></div></li></ul><span class="token punctuation">]</span><span class="token punctuation">,</span></div></li><li><div class="hoverable collapsed"><span class="property token string">"created_by"</span>: <span class="token string">&quot;string&quot;</span></div></li></ul><span class="token punctuation">}</span></div></li></ul><span class="token punctuation">]</span></div></li></ul><span class="token punctuation">}</span><span class="token punctuation">,</span></div></li><li><div class="hoverable "><span class="property token string">"next_token"</span>: <span class="token string">&quot;string&quot;</span></div></li></ul><span class="token punctuation">}</span></code></div></div></div></div></div></div><div class="react-tabs__tab-panel" role="tabpanel" id="panel«R9pscq»1" aria-labelledby="tab«R9pscq»1"></div></div></div></div></div></div><div id="tag/Entity" data-section-id="tag/Entity" class="sc-dYOLZc kDuBQd"><div class="sc-jZhnRx tMeMv"><div class="sc-gwsNht iRoSfe"><h2 class="sc-kThouk jkwlQD"><a class="sc-kieALA jOWzfS" href="#tag/Entity" aria-label="tag/Entity"></a>Entity</h2></div></div><div class="sc-gwsNht ciCSbi"><div class="sc-faJlkc sc-fdduAw kOqeZB kEOvim redoc-markdown " html="&lt;p&gt;Entity Operations&lt;/p&gt;
"><p>Entity Operations</p>
</div></div></div><div id="tag/Entity/operation/findEntities" data-section-id="tag/Entity/operation/findEntities" class="sc-dYOLZc dRVblm"><div data-section-id="operation/findEntities" id="operation/findEntities" class="sc-jZhnRx tMeMv"><div class="sc-gwsNht iRoSfe"><h2 class="sc-kThouk jkwlQD"><a class="sc-kieALA jOWzfS" href="#tag/Entity/operation/findEntities" aria-label="tag/Entity/operation/findEntities"></a>Retrieve a List of Entities<!-- --> </h2><div class="sc-bkBUWa fARMpn"><div html="&lt;p&gt;The API endpoint allows you to fetch the data model entities.
The API supports pagination and filtering options to manage large results effectively.&lt;/p&gt;
" class="sc-faJlkc sc-fdduAw kOqeZB kEOvim"><p>The API endpoint allows you to fetch the data model entities.
The API supports pagination and filtering options to manage large results effectively.</p>
</div></div><div class="sc-hUPhBQ iaIgja"><div class="sc-ilCyfT gqqgtC"><h5 class="sc-elFkmj sc-gUMLBR eUkINt jefPco">Authorizations:</h5><svg class="sc-dibcMh gsIrbT" version="1.1" viewBox="0 0 24 24" x="0" xmlns="http://www.w3.org/2000/svg" y="0" aria-hidden="true"><polygon points="17.3 8.3 12 13.6 6.7 8.3 5.3 9.7 12 16.4 18.7 9.7 "></polygon></svg></div><div class="sc-jlEUfS dNXpfB"><span class="sc-iFRVkA WPZDV"><span class="sc-hFPzkB eSQudw"><i>bearerAuth</i></span></span></div></div><div><h5 class="sc-elFkmj eUkINt">path<!-- --> Parameters</h5><table class="sc-eGjrzz cGiuNU"><tbody><tr class="last "><td kind="field" title="context_id" class="sc-kHNKno sc-frmfij cDFxGr kNAbAx"><span class="sc-hLyRwt lhCIDE"></span><span class="property-name">context_id</span></td><td class="sc-groBii dzPbxx"><div><div><span class="sc-byRegH sc-budtlk eyIabj jGdyyu"></span><span class="sc-byRegH sc-fJvEKN eyIabj fcQkkm">string</span></div> <div><div html="&lt;p&gt;Filter entities by the context like a datastore or subject areas identifier.&lt;/p&gt;
" class="sc-faJlkc sc-fdduAw kOqeZB csbUjU"><p>Filter entities by the context like a datastore or subject areas identifier.</p>
</div></div></div></td></tr></tbody></table></div><div><h5 class="sc-elFkmj eUkINt">query<!-- --> Parameters</h5><table class="sc-eGjrzz cGiuNU"><tbody><tr class=""><td kind="field" title="type" class="sc-kHNKno sc-frmfij cDFxGr kNAbAx"><span class="sc-hLyRwt lhCIDE"></span><span class="property-name">type</span></td><td class="sc-groBii dzPbxx"><div><div><span class="sc-byRegH sc-budtlk eyIabj jGdyyu">Array of </span><span class="sc-byRegH sc-fJvEKN eyIabj fcQkkm">strings</span></div> <div><div html="&lt;p&gt;Filter by the type of entity.
For example &lt;code&gt;TABLE&lt;/code&gt;.
Please review the meta-meta information for the data model for the valid types.&lt;/p&gt;
" class="sc-faJlkc sc-fdduAw kOqeZB csbUjU"><p>Filter by the type of entity.
For example <code>TABLE</code>.
Please review the meta-meta information for the data model for the valid types.</p>
</div></div></div></td></tr><tr class=""><td kind="field" title="consumption_type" class="sc-kHNKno sc-frmfij cDFxGr kNAbAx"><span class="sc-hLyRwt lhCIDE"></span><span class="property-name">consumption_type</span></td><td class="sc-groBii dzPbxx"><div><div><span class="sc-byRegH sc-budtlk eyIabj jGdyyu">Array of </span><span class="sc-byRegH sc-fJvEKN eyIabj fcQkkm">strings</span></div> <div><div html="&lt;p&gt;Filter by the consumption_type of entity.
For example &lt;code&gt;table&lt;/code&gt;.&lt;/p&gt;
" class="sc-faJlkc sc-fdduAw kOqeZB csbUjU"><p>Filter by the consumption_type of entity.
For example <code>table</code>.</p>
</div></div></div></td></tr><tr class=""><td kind="field" title="tags" class="sc-kHNKno sc-frmfij cDFxGr kNAbAx"><span class="sc-hLyRwt lhCIDE"></span><span class="property-name">tags</span></td><td class="sc-groBii dzPbxx"><div><div><span class="sc-byRegH sc-budtlk eyIabj jGdyyu">Array of </span><span class="sc-byRegH sc-fJvEKN eyIabj fcQkkm">strings</span></div> <div><div html="&lt;p&gt;Filter by the tags of entity.&lt;/p&gt;
" class="sc-faJlkc sc-fdduAw kOqeZB csbUjU"><p>Filter by the tags of entity.</p>
</div></div></div></td></tr><tr class=""><td kind="field" title="name" class="sc-kHNKno sc-frmfij cDFxGr kNAbAx"><span class="sc-hLyRwt lhCIDE"></span><span class="property-name">name</span></td><td class="sc-groBii dzPbxx"><div><div><span class="sc-byRegH sc-budtlk eyIabj jGdyyu"></span><span class="sc-byRegH sc-fJvEKN eyIabj fcQkkm">string</span></div> <div><div html="&lt;p&gt;Filter entities by the matching exact name.&lt;/p&gt;
" class="sc-faJlkc sc-fdduAw kOqeZB csbUjU"><p>Filter entities by the matching exact name.</p>
</div></div></div></td></tr><tr class=""><td kind="field" title="name_text" class="sc-kHNKno sc-frmfij cDFxGr kNAbAx"><span class="sc-hLyRwt lhCIDE"></span><span class="property-name">name_text</span></td><td class="sc-groBii dzPbxx"><div><div><span class="sc-byRegH sc-budtlk eyIabj jGdyyu"></span><span class="sc-byRegH sc-fJvEKN eyIabj fcQkkm">string</span></div> <div><div html="&lt;p&gt;Filter entities using contains text search on the name of the entity.&lt;/p&gt;
" class="sc-faJlkc sc-fdduAw kOqeZB csbUjU"><p>Filter entities using contains text search on the name of the entity.</p>
</div></div></div></td></tr><tr class=""><td kind="field" title="description_text" class="sc-kHNKno sc-frmfij cDFxGr kNAbAx"><span class="sc-hLyRwt lhCIDE"></span><span class="property-name">description_text</span></td><td class="sc-groBii dzPbxx"><div><div><span class="sc-byRegH sc-budtlk eyIabj jGdyyu"></span><span class="sc-byRegH sc-fJvEKN eyIabj fcQkkm">string</span></div> <div><div html="&lt;p&gt;Filter entities using contains text search on the description of the entity.&lt;/p&gt;
" class="sc-faJlkc sc-fdduAw kOqeZB csbUjU"><p>Filter entities using contains text search on the description of the entity.</p>
</div></div></div></td></tr><tr class=""><td kind="field" title="first" class="sc-kHNKno sc-frmfij cDFxGr kNAbAx"><span class="sc-hLyRwt lhCIDE"></span><span class="property-name">first</span></td><td class="sc-groBii dzPbxx"><div><div><span class="sc-byRegH sc-budtlk eyIabj jGdyyu"></span><span class="sc-byRegH sc-fJvEKN eyIabj fcQkkm">integer</span></div> <div><div html="&lt;p&gt;Number of requested results for paging&lt;/p&gt;
" class="sc-faJlkc sc-fdduAw kOqeZB csbUjU"><p>Number of requested results for paging</p>
</div></div></div></td></tr><tr class=""><td kind="field" title="offset" class="sc-kHNKno sc-frmfij cDFxGr kNAbAx"><span class="sc-hLyRwt lhCIDE"></span><span class="property-name">offset</span></td><td class="sc-groBii dzPbxx"><div><div><span class="sc-byRegH sc-budtlk eyIabj jGdyyu"></span><span class="sc-byRegH sc-fJvEKN eyIabj fcQkkm">string</span></div> <div><div html="&lt;p&gt;Encoded value used to indicate where in the result set to begin&lt;/p&gt;
" class="sc-faJlkc sc-fdduAw kOqeZB csbUjU"><p>Encoded value used to indicate where in the result set to begin</p>
</div></div></div></td></tr><tr class=""><td kind="field" title="sort" class="sc-kHNKno sc-frmfij cDFxGr kNAbAx"><span class="sc-hLyRwt lhCIDE"></span><span class="property-name">sort</span></td><td class="sc-groBii dzPbxx"><div><div><span class="sc-byRegH sc-budtlk eyIabj jGdyyu"></span><span class="sc-byRegH sc-fJvEKN eyIabj fcQkkm">string</span></div> <div><div html="&lt;p&gt;A base64 encoded sort value&lt;/p&gt;
" class="sc-faJlkc sc-fdduAw kOqeZB csbUjU"><p>A base64 encoded sort value</p>
</div></div></div></td></tr><tr class="last "><td kind="field" title="filter" class="sc-kHNKno sc-frmfij cDFxGr kNAbAx"><span class="sc-hLyRwt lhCIDE"></span><span class="property-name">filter</span></td><td class="sc-groBii dzPbxx"><div><div><span class="sc-byRegH sc-budtlk eyIabj jGdyyu"></span><span class="sc-byRegH sc-fJvEKN eyIabj fcQkkm">string</span></div> <div><div html="&lt;p&gt;A base64 encoded filter value&lt;/p&gt;
" class="sc-faJlkc sc-fdduAw kOqeZB csbUjU"><p>A base64 encoded filter value</p>
</div></div></div></td></tr></tbody></table></div><div><h3 class="sc-gSVmdp jWGRrJ">Responses</h3><div><button class="sc-jthNAk gfsyZD"><svg class="sc-dibcMh dpIKEE" version="1.1" viewBox="0 0 24 24" x="0" xmlns="http://www.w3.org/2000/svg" y="0" aria-hidden="true"><polygon points="17.3 8.3 12 13.6 6.7 8.3 5.3 9.7 12 16.4 18.7 9.7 "></polygon></svg><strong class="sc-eOObWA ecXPYN">200<!-- --> </strong><div html="&lt;p&gt;Entities list.&lt;/p&gt;
" class="sc-faJlkc sc-fdduAw sc-eINXvP kOqeZB kkltVg fbuMAW"><p>Entities list.</p>
</div></button></div><div><button class="sc-jthNAk eNjFmB"><svg class="sc-dibcMh jsRtRU" version="1.1" viewBox="0 0 24 24" x="0" xmlns="http://www.w3.org/2000/svg" y="0" aria-hidden="true"><polygon points="17.3 8.3 12 13.6 6.7 8.3 5.3 9.7 12 16.4 18.7 9.7 "></polygon></svg><strong class="sc-eOObWA ecXPYN">400<!-- --> </strong><div html="&lt;p&gt;Bad Request&lt;/p&gt;
" class="sc-faJlkc sc-fdduAw sc-eINXvP kOqeZB kkltVg fbuMAW"><p>Bad Request</p>
</div></button></div><div><button class="sc-jthNAk kdKxoT" disabled=""><strong class="sc-eOObWA ecXPYN">401<!-- --> </strong><div html="&lt;p&gt;Authentication information is expired, missing, or invalid.
Ensure that you have authenticated correctly.&lt;/p&gt;
" class="sc-faJlkc sc-fdduAw sc-eINXvP kOqeZB kkltVg fbuMAW"><p>Authentication information is expired, missing, or invalid.
Ensure that you have authenticated correctly.</p>
</div></button></div><div><button class="sc-jthNAk kdKxoT" disabled=""><strong class="sc-eOObWA ecXPYN">403<!-- --> </strong><div html="&lt;p&gt;Authorization failed or access denied error.
Ensure that you have the necessary permissions.&lt;/p&gt;
" class="sc-faJlkc sc-fdduAw sc-eINXvP kOqeZB kkltVg fbuMAW"><p>Authorization failed or access denied error.
Ensure that you have the necessary permissions.</p>
</div></button></div></div></div><div class="sc-jMpmlX sc-hoLldG kCcXaS jpPJrn"><div class="sc-eUzzst cgvUrj"><button class="sc-bpAAPv ecNgtW"><span type="get" class="sc-fBpBLu deMhiD http-verb get">get</span><span class="sc-jgoAos hgvsuc">/contexts/{context_id}/datamodel/entities</span><svg class="sc-dibcMh NOyYc" style="margin-right:-25px" version="1.1" viewBox="0 0 24 24" x="0" xmlns="http://www.w3.org/2000/svg" y="0" aria-hidden="true"><polygon points="17.3 8.3 12 13.6 6.7 8.3 5.3 9.7 12 16.4 18.7 9.7 "></polygon></svg></button><div aria-hidden="true" class="sc-dXqqap bVkkkk"><div class="sc-ijfqWo jlEXoB"><div html="" class="sc-faJlkc sc-fdduAw kOqeZB csbUjU"></div><div tabindex="0" role="button"><div class="sc-srrxr zPpwX"><span>http://catalog.govern.svc.cluster.local</span>/contexts/{context_id}/datamodel/entities</div></div></div><div class="sc-ijfqWo jlEXoB"><div html="" class="sc-faJlkc sc-fdduAw kOqeZB csbUjU"></div><div tabindex="0" role="button"><div class="sc-srrxr zPpwX"><span>https://ct.mgt.syniti-dev.com</span>/contexts/{context_id}/datamodel/entities</div></div></div></div></div><div><h3 class="sc-llIIlC fdkIVZ"> <!-- -->Response samples<!-- --> </h3><div class="sc-cTIdZS dFpYBv" data-rttabs="true"><ul class="react-tabs__tab-list" role="tablist"><li class="tab-success react-tabs__tab--selected" role="tab" id="tab«R4sskq»0" aria-selected="true" aria-disabled="false" aria-controls="panel«R4sskq»0" tabindex="0" data-rttab="true">200</li><li class="tab-error" role="tab" id="tab«R4sskq»1" aria-selected="false" aria-disabled="false" aria-controls="panel«R4sskq»1" data-rttab="true">400</li></ul><div class="react-tabs__tab-panel react-tabs__tab-panel--selected" role="tabpanel" id="panel«R4sskq»0" aria-labelledby="tab«R4sskq»0"><div><div class="sc-bXYrjy fpFYoG"><span class="sc-fUPiRJ TqlOY">Content type</span><div class="sc-bFwXsg caaBgn">application/json</div></div><div class="sc-bgpKpp kuvBhH"><div class="sc-dRHutB iAOzgg"><div class="sc-VILhF itwxyW"><button><div class="sc-fIQtvO indwFD">Copy</div></button><button> Expand all </button><button> Collapse all </button></div><div tabindex="0" class="sc-faJlkc kOqeZB sc-eRJQtA eudsMA"><div class="redoc-json"><code><button class="collapser" aria-label="collapse"></button><span class="token punctuation">{</span><span class="ellipsis"></span><ul class="obj collapsible"><li><div class="hoverable "><span class="property token string">"next_token"</span>: <span class="token string">&quot;string&quot;</span><span class="token punctuation">,</span></div></li><li><div class="hoverable "><span class="property token string">"items"</span>: <button class="collapser" aria-label="collapse"></button><span class="token punctuation">[</span><span class="ellipsis"></span><ul class="array collapsible"><li><div class="hoverable collapsed"><button class="collapser" aria-label="expand"></button><span class="token punctuation">{</span><span class="ellipsis"></span><ul class="obj collapsible"><li><div class="hoverable collapsed"><span class="property token string">"id"</span>: <span class="token string">&quot;string&quot;</span><span class="token punctuation">,</span></div></li><li><div class="hoverable collapsed"><span class="property token string">"model_name"</span>: <span class="token string">&quot;string&quot;</span><span class="token punctuation">,</span></div></li><li><div class="hoverable collapsed"><span class="property token string">"name"</span>: <span class="token string">&quot;string&quot;</span><span class="token punctuation">,</span></div></li><li><div class="hoverable collapsed"><span class="property token string">"description"</span>: <span class="token string">&quot;string&quot;</span><span class="token punctuation">,</span></div></li><li><div class="hoverable collapsed"><span class="property token string">"type"</span>: <span class="token string">&quot;string&quot;</span><span class="token punctuation">,</span></div></li><li><div class="hoverable collapsed"><span class="property token string">"consumption_type"</span>: <span class="token string">&quot;string&quot;</span><span class="token punctuation">,</span></div></li><li><div class="hoverable collapsed"><span class="property token string">"index"</span>: <span class="token number">0</span><span class="token punctuation">,</span></div></li><li><div class="hoverable collapsed"><span class="property token string">"properties"</span>: <span class="token punctuation">{ }</span><span class="token punctuation">,</span></div></li><li><div class="hoverable collapsed"><span class="property token string">"custom_properties"</span>: <span class="token punctuation">{ }</span><span class="token punctuation">,</span></div></li><li><div class="hoverable collapsed"><span class="property token string">"tags"</span>: <button class="collapser" aria-label="expand"></button><span class="token punctuation">[</span><span class="ellipsis"></span><ul class="array collapsible"><li><div class="hoverable collapsed"><span class="token string">&quot;string&quot;</span></div></li></ul><span class="token punctuation">]</span><span class="token punctuation">,</span></div></li><li><div class="hoverable collapsed"><span class="property token string">"created_by"</span>: <span class="token string">&quot;string&quot;</span><span class="token punctuation">,</span></div></li><li><div class="hoverable collapsed"><span class="property token string">"entity_elements"</span>: <button class="collapser" aria-label="expand"></button><span class="token punctuation">[</span><span class="ellipsis"></span><ul class="array collapsible"><li><div class="hoverable collapsed"><button class="collapser" aria-label="expand"></button><span class="token punctuation">{</span><span class="ellipsis"></span><ul class="obj collapsible"><li><div class="hoverable collapsed"><span class="property token string">"id"</span>: <span class="token string">&quot;string&quot;</span><span class="token punctuation">,</span></div></li><li><div class="hoverable collapsed"><span class="property token string">"model_name"</span>: <span class="token string">&quot;string&quot;</span><span class="token punctuation">,</span></div></li><li><div class="hoverable collapsed"><span class="property token string">"name"</span>: <span class="token string">&quot;string&quot;</span><span class="token punctuation">,</span></div></li><li><div class="hoverable collapsed"><span class="property token string">"description"</span>: <span class="token string">&quot;string&quot;</span><span class="token punctuation">,</span></div></li><li><div class="hoverable collapsed"><span class="property token string">"type"</span>: <span class="token string">&quot;string&quot;</span><span class="token punctuation">,</span></div></li><li><div class="hoverable collapsed"><span class="property token string">"consumption_type"</span>: <span class="token string">&quot;string&quot;</span><span class="token punctuation">,</span></div></li><li><div class="hoverable collapsed"><span class="property token string">"index"</span>: <span class="token number">0</span><span class="token punctuation">,</span></div></li><li><div class="hoverable collapsed"><span class="property token string">"data_type"</span>: <span class="token string">&quot;string&quot;</span><span class="token punctuation">,</span></div></li><li><div class="hoverable collapsed"><span class="property token string">"default_value"</span>: <span class="token string">&quot;string&quot;</span><span class="token punctuation">,</span></div></li><li><div class="hoverable collapsed"><span class="property token string">"enum"</span>: <button class="collapser" aria-label="expand"></button><span class="token punctuation">[</span><span class="ellipsis"></span><ul class="array collapsible"><li><div class="hoverable collapsed"><span class="token string">&quot;string&quot;</span></div></li></ul><span class="token punctuation">]</span><span class="token punctuation">,</span></div></li><li><div class="hoverable collapsed"><span class="property token string">"is_required"</span>: <span class="token boolean">true</span><span class="token punctuation">,</span></div></li><li><div class="hoverable collapsed"><span class="property token string">"is_array"</span>: <span class="token boolean">true</span><span class="token punctuation">,</span></div></li><li><div class="hoverable collapsed"><span class="property token string">"max_length"</span>: <span class="token number">0</span><span class="token punctuation">,</span></div></li><li><div class="hoverable collapsed"><span class="property token string">"precision"</span>: <span class="token number">0</span><span class="token punctuation">,</span></div></li><li><div class="hoverable collapsed"><span class="property token string">"scale"</span>: <span class="token number">0</span><span class="token punctuation">,</span></div></li><li><div class="hoverable collapsed"><span class="property token string">"properties"</span>: <span class="token punctuation">{ }</span><span class="token punctuation">,</span></div></li><li><div class="hoverable collapsed"><span class="property token string">"custom_properties"</span>: <span class="token punctuation">{ }</span><span class="token punctuation">,</span></div></li><li><div class="hoverable collapsed"><span class="property token string">"tags"</span>: <button class="collapser" aria-label="expand"></button><span class="token punctuation">[</span><span class="ellipsis"></span><ul class="array collapsible"><li><div class="hoverable collapsed"><span class="token string">&quot;string&quot;</span></div></li></ul><span class="token punctuation">]</span><span class="token punctuation">,</span></div></li><li><div class="hoverable collapsed"><span class="property token string">"created_by"</span>: <span class="token string">&quot;string&quot;</span></div></li></ul><span class="token punctuation">}</span></div></li></ul><span class="token punctuation">]</span></div></li></ul><span class="token punctuation">}</span></div></li></ul><span class="token punctuation">]</span></div></li></ul><span class="token punctuation">}</span></code></div></div></div></div></div></div><div class="react-tabs__tab-panel" role="tabpanel" id="panel«R4sskq»1" aria-labelledby="tab«R4sskq»1"></div></div></div></div></div></div><div id="tag/EntityElement" data-section-id="tag/EntityElement" class="sc-dYOLZc kDuBQd"><div class="sc-jZhnRx tMeMv"><div class="sc-gwsNht iRoSfe"><h2 class="sc-kThouk jkwlQD"><a class="sc-kieALA jOWzfS" href="#tag/EntityElement" aria-label="tag/EntityElement"></a>EntityElement</h2></div></div><div class="sc-gwsNht ciCSbi"><div class="sc-faJlkc sc-fdduAw kOqeZB kEOvim redoc-markdown " html="&lt;p&gt;Element Operations&lt;/p&gt;
"><p>Element Operations</p>
</div></div></div><div id="tag/EntityElement/operation/findElements" data-section-id="tag/EntityElement/operation/findElements" class="sc-dYOLZc dRVblm"><div data-section-id="operation/findElements" id="operation/findElements" class="sc-jZhnRx tMeMv"><div class="sc-gwsNht iRoSfe"><h2 class="sc-kThouk jkwlQD"><a class="sc-kieALA jOWzfS" href="#tag/EntityElement/operation/findElements" aria-label="tag/EntityElement/operation/findElements"></a>Retrieve a List of Entity Elements<!-- --> </h2><div class="sc-bkBUWa fARMpn"><div html="&lt;p&gt;The API endpoint allows you to fetch the data model entity elements.
The API supports pagination and filtering options to manage large results effectively.&lt;/p&gt;
" class="sc-faJlkc sc-fdduAw kOqeZB kEOvim"><p>The API endpoint allows you to fetch the data model entity elements.
The API supports pagination and filtering options to manage large results effectively.</p>
</div></div><div class="sc-hUPhBQ iaIgja"><div class="sc-ilCyfT gqqgtC"><h5 class="sc-elFkmj sc-gUMLBR eUkINt jefPco">Authorizations:</h5><svg class="sc-dibcMh gsIrbT" version="1.1" viewBox="0 0 24 24" x="0" xmlns="http://www.w3.org/2000/svg" y="0" aria-hidden="true"><polygon points="17.3 8.3 12 13.6 6.7 8.3 5.3 9.7 12 16.4 18.7 9.7 "></polygon></svg></div><div class="sc-jlEUfS dNXpfB"><span class="sc-iFRVkA WPZDV"><span class="sc-hFPzkB eSQudw"><i>bearerAuth</i></span></span></div></div><div><h5 class="sc-elFkmj eUkINt">path<!-- --> Parameters</h5><table class="sc-eGjrzz cGiuNU"><tbody><tr class="last "><td kind="field" title="entity_id" class="sc-kHNKno sc-frmfij cDFxGr kNAbAx"><span class="sc-hLyRwt lhCIDE"></span><span class="property-name">entity_id</span><div class="sc-byRegH sc-isZTFa eyIabj iUhYwd">required</div></td><td class="sc-groBii dzPbxx"><div><div><span class="sc-byRegH sc-budtlk eyIabj jGdyyu"></span><span class="sc-byRegH sc-fJvEKN eyIabj fcQkkm">string</span></div> <div><div html="&lt;p&gt;Fetch elements for the parent entity.&lt;/p&gt;
" class="sc-faJlkc sc-fdduAw kOqeZB csbUjU"><p>Fetch elements for the parent entity.</p>
</div></div></div></td></tr></tbody></table></div><div><h5 class="sc-elFkmj eUkINt">query<!-- --> Parameters</h5><table class="sc-eGjrzz cGiuNU"><tbody><tr class=""><td kind="field" title="type" class="sc-kHNKno sc-frmfij cDFxGr kNAbAx"><span class="sc-hLyRwt lhCIDE"></span><span class="property-name">type</span></td><td class="sc-groBii dzPbxx"><div><div><span class="sc-byRegH sc-budtlk eyIabj jGdyyu">Array of </span><span class="sc-byRegH sc-fJvEKN eyIabj fcQkkm">strings</span></div> <div><div html="&lt;p&gt;Filter by the type of entity element.
For example &lt;code&gt;COLUMN&lt;/code&gt;.
Please review the meta-meta information for the data model for the valid types.&lt;/p&gt;
" class="sc-faJlkc sc-fdduAw kOqeZB csbUjU"><p>Filter by the type of entity element.
For example <code>COLUMN</code>.
Please review the meta-meta information for the data model for the valid types.</p>
</div></div></div></td></tr><tr class=""><td kind="field" title="consumption_type" class="sc-kHNKno sc-frmfij cDFxGr kNAbAx"><span class="sc-hLyRwt lhCIDE"></span><span class="property-name">consumption_type</span></td><td class="sc-groBii dzPbxx"><div><div><span class="sc-byRegH sc-budtlk eyIabj jGdyyu">Array of </span><span class="sc-byRegH sc-fJvEKN eyIabj fcQkkm">strings</span></div> <div><div html="&lt;p&gt;Filter by the consumption_type of entity element.
For example &lt;code&gt;columns&lt;/code&gt;.&lt;/p&gt;
" class="sc-faJlkc sc-fdduAw kOqeZB csbUjU"><p>Filter by the consumption_type of entity element.
For example <code>columns</code>.</p>
</div></div></div></td></tr><tr class=""><td kind="field" title="tags" class="sc-kHNKno sc-frmfij cDFxGr kNAbAx"><span class="sc-hLyRwt lhCIDE"></span><span class="property-name">tags</span></td><td class="sc-groBii dzPbxx"><div><div><span class="sc-byRegH sc-budtlk eyIabj jGdyyu">Array of </span><span class="sc-byRegH sc-fJvEKN eyIabj fcQkkm">strings</span></div> <div><div html="&lt;p&gt;Filter by the tags of entity element.&lt;/p&gt;
" class="sc-faJlkc sc-fdduAw kOqeZB csbUjU"><p>Filter by the tags of entity element.</p>
</div></div></div></td></tr><tr class=""><td kind="field" title="name" class="sc-kHNKno sc-frmfij cDFxGr kNAbAx"><span class="sc-hLyRwt lhCIDE"></span><span class="property-name">name</span></td><td class="sc-groBii dzPbxx"><div><div><span class="sc-byRegH sc-budtlk eyIabj jGdyyu"></span><span class="sc-byRegH sc-fJvEKN eyIabj fcQkkm">string</span></div> <div><div html="&lt;p&gt;Filter entity elements by the matching exact name.&lt;/p&gt;
" class="sc-faJlkc sc-fdduAw kOqeZB csbUjU"><p>Filter entity elements by the matching exact name.</p>
</div></div></div></td></tr><tr class=""><td kind="field" title="name_text" class="sc-kHNKno sc-frmfij cDFxGr kNAbAx"><span class="sc-hLyRwt lhCIDE"></span><span class="property-name">name_text</span></td><td class="sc-groBii dzPbxx"><div><div><span class="sc-byRegH sc-budtlk eyIabj jGdyyu"></span><span class="sc-byRegH sc-fJvEKN eyIabj fcQkkm">string</span></div> <div><div html="&lt;p&gt;Filter entity elements using contains text search on the name of the entity.&lt;/p&gt;
" class="sc-faJlkc sc-fdduAw kOqeZB csbUjU"><p>Filter entity elements using contains text search on the name of the entity.</p>
</div></div></div></td></tr><tr class=""><td kind="field" title="description_text" class="sc-kHNKno sc-frmfij cDFxGr kNAbAx"><span class="sc-hLyRwt lhCIDE"></span><span class="property-name">description_text</span></td><td class="sc-groBii dzPbxx"><div><div><span class="sc-byRegH sc-budtlk eyIabj jGdyyu"></span><span class="sc-byRegH sc-fJvEKN eyIabj fcQkkm">string</span></div> <div><div html="&lt;p&gt;Filter entity elements using contains text search on the description of the entity.&lt;/p&gt;
" class="sc-faJlkc sc-fdduAw kOqeZB csbUjU"><p>Filter entity elements using contains text search on the description of the entity.</p>
</div></div></div></td></tr><tr class=""><td kind="field" title="first" class="sc-kHNKno sc-frmfij cDFxGr kNAbAx"><span class="sc-hLyRwt lhCIDE"></span><span class="property-name">first</span></td><td class="sc-groBii dzPbxx"><div><div><span class="sc-byRegH sc-budtlk eyIabj jGdyyu"></span><span class="sc-byRegH sc-fJvEKN eyIabj fcQkkm">integer</span></div> <div><div html="&lt;p&gt;Number of requested results for paging&lt;/p&gt;
" class="sc-faJlkc sc-fdduAw kOqeZB csbUjU"><p>Number of requested results for paging</p>
</div></div></div></td></tr><tr class=""><td kind="field" title="offset" class="sc-kHNKno sc-frmfij cDFxGr kNAbAx"><span class="sc-hLyRwt lhCIDE"></span><span class="property-name">offset</span></td><td class="sc-groBii dzPbxx"><div><div><span class="sc-byRegH sc-budtlk eyIabj jGdyyu"></span><span class="sc-byRegH sc-fJvEKN eyIabj fcQkkm">string</span></div> <div><div html="&lt;p&gt;Encoded value used to indicate where in the result set to begin&lt;/p&gt;
" class="sc-faJlkc sc-fdduAw kOqeZB csbUjU"><p>Encoded value used to indicate where in the result set to begin</p>
</div></div></div></td></tr><tr class=""><td kind="field" title="sort" class="sc-kHNKno sc-frmfij cDFxGr kNAbAx"><span class="sc-hLyRwt lhCIDE"></span><span class="property-name">sort</span></td><td class="sc-groBii dzPbxx"><div><div><span class="sc-byRegH sc-budtlk eyIabj jGdyyu"></span><span class="sc-byRegH sc-fJvEKN eyIabj fcQkkm">string</span></div> <div><div html="&lt;p&gt;A base64 encoded sort value&lt;/p&gt;
" class="sc-faJlkc sc-fdduAw kOqeZB csbUjU"><p>A base64 encoded sort value</p>
</div></div></div></td></tr><tr class="last "><td kind="field" title="filter" class="sc-kHNKno sc-frmfij cDFxGr kNAbAx"><span class="sc-hLyRwt lhCIDE"></span><span class="property-name">filter</span></td><td class="sc-groBii dzPbxx"><div><div><span class="sc-byRegH sc-budtlk eyIabj jGdyyu"></span><span class="sc-byRegH sc-fJvEKN eyIabj fcQkkm">string</span></div> <div><div html="&lt;p&gt;A base64 encoded filter value&lt;/p&gt;
" class="sc-faJlkc sc-fdduAw kOqeZB csbUjU"><p>A base64 encoded filter value</p>
</div></div></div></td></tr></tbody></table></div><div><h3 class="sc-gSVmdp jWGRrJ">Responses</h3><div><button class="sc-jthNAk gfsyZD"><svg class="sc-dibcMh dpIKEE" version="1.1" viewBox="0 0 24 24" x="0" xmlns="http://www.w3.org/2000/svg" y="0" aria-hidden="true"><polygon points="17.3 8.3 12 13.6 6.7 8.3 5.3 9.7 12 16.4 18.7 9.7 "></polygon></svg><strong class="sc-eOObWA ecXPYN">200<!-- --> </strong><div html="&lt;p&gt;Entity Elements list.&lt;/p&gt;
" class="sc-faJlkc sc-fdduAw sc-eINXvP kOqeZB kkltVg fbuMAW"><p>Entity Elements list.</p>
</div></button></div><div><button class="sc-jthNAk eNjFmB"><svg class="sc-dibcMh jsRtRU" version="1.1" viewBox="0 0 24 24" x="0" xmlns="http://www.w3.org/2000/svg" y="0" aria-hidden="true"><polygon points="17.3 8.3 12 13.6 6.7 8.3 5.3 9.7 12 16.4 18.7 9.7 "></polygon></svg><strong class="sc-eOObWA ecXPYN">400<!-- --> </strong><div html="&lt;p&gt;Bad Request&lt;/p&gt;
" class="sc-faJlkc sc-fdduAw sc-eINXvP kOqeZB kkltVg fbuMAW"><p>Bad Request</p>
</div></button></div><div><button class="sc-jthNAk kdKxoT" disabled=""><strong class="sc-eOObWA ecXPYN">401<!-- --> </strong><div html="&lt;p&gt;Authentication information is expired, missing, or invalid.
Ensure that you have authenticated correctly.&lt;/p&gt;
" class="sc-faJlkc sc-fdduAw sc-eINXvP kOqeZB kkltVg fbuMAW"><p>Authentication information is expired, missing, or invalid.
Ensure that you have authenticated correctly.</p>
</div></button></div><div><button class="sc-jthNAk kdKxoT" disabled=""><strong class="sc-eOObWA ecXPYN">403<!-- --> </strong><div html="&lt;p&gt;Authorization failed or access denied error.
Ensure that you have the necessary permissions.&lt;/p&gt;
" class="sc-faJlkc sc-fdduAw sc-eINXvP kOqeZB kkltVg fbuMAW"><p>Authorization failed or access denied error.
Ensure that you have the necessary permissions.</p>
</div></button></div></div></div><div class="sc-jMpmlX sc-hoLldG kCcXaS jpPJrn"><div class="sc-eUzzst cgvUrj"><button class="sc-bpAAPv ecNgtW"><span type="get" class="sc-fBpBLu deMhiD http-verb get">get</span><span class="sc-jgoAos hgvsuc">/contexts/{context_id}/datamodel/elements</span><svg class="sc-dibcMh NOyYc" style="margin-right:-25px" version="1.1" viewBox="0 0 24 24" x="0" xmlns="http://www.w3.org/2000/svg" y="0" aria-hidden="true"><polygon points="17.3 8.3 12 13.6 6.7 8.3 5.3 9.7 12 16.4 18.7 9.7 "></polygon></svg></button><div aria-hidden="true" class="sc-dXqqap bVkkkk"><div class="sc-ijfqWo jlEXoB"><div html="" class="sc-faJlkc sc-fdduAw kOqeZB csbUjU"></div><div tabindex="0" role="button"><div class="sc-srrxr zPpwX"><span>http://catalog.govern.svc.cluster.local</span>/contexts/{context_id}/datamodel/elements</div></div></div><div class="sc-ijfqWo jlEXoB"><div html="" class="sc-faJlkc sc-fdduAw kOqeZB csbUjU"></div><div tabindex="0" role="button"><div class="sc-srrxr zPpwX"><span>https://ct.mgt.syniti-dev.com</span>/contexts/{context_id}/datamodel/elements</div></div></div></div></div><div><h3 class="sc-llIIlC fdkIVZ"> <!-- -->Response samples<!-- --> </h3><div class="sc-cTIdZS dFpYBv" data-rttabs="true"><ul class="react-tabs__tab-list" role="tablist"><li class="tab-success react-tabs__tab--selected" role="tab" id="tab«R4sssq»0" aria-selected="true" aria-disabled="false" aria-controls="panel«R4sssq»0" tabindex="0" data-rttab="true">200</li><li class="tab-error" role="tab" id="tab«R4sssq»1" aria-selected="false" aria-disabled="false" aria-controls="panel«R4sssq»1" data-rttab="true">400</li></ul><div class="react-tabs__tab-panel react-tabs__tab-panel--selected" role="tabpanel" id="panel«R4sssq»0" aria-labelledby="tab«R4sssq»0"><div><div class="sc-bXYrjy fpFYoG"><span class="sc-fUPiRJ TqlOY">Content type</span><div class="sc-bFwXsg caaBgn">application/json</div></div><div class="sc-bgpKpp kuvBhH"><div class="sc-dRHutB iAOzgg"><div class="sc-VILhF itwxyW"><button><div class="sc-fIQtvO indwFD">Copy</div></button><button> Expand all </button><button> Collapse all </button></div><div tabindex="0" class="sc-faJlkc kOqeZB sc-eRJQtA eudsMA"><div class="redoc-json"><code><button class="collapser" aria-label="collapse"></button><span class="token punctuation">{</span><span class="ellipsis"></span><ul class="obj collapsible"><li><div class="hoverable "><span class="property token string">"next_token"</span>: <span class="token string">&quot;string&quot;</span><span class="token punctuation">,</span></div></li><li><div class="hoverable "><span class="property token string">"items"</span>: <button class="collapser" aria-label="collapse"></button><span class="token punctuation">[</span><span class="ellipsis"></span><ul class="array collapsible"><li><div class="hoverable collapsed"><button class="collapser" aria-label="expand"></button><span class="token punctuation">{</span><span class="ellipsis"></span><ul class="obj collapsible"><li><div class="hoverable collapsed"><span class="property token string">"id"</span>: <span class="token string">&quot;string&quot;</span><span class="token punctuation">,</span></div></li><li><div class="hoverable collapsed"><span class="property token string">"model_name"</span>: <span class="token string">&quot;string&quot;</span><span class="token punctuation">,</span></div></li><li><div class="hoverable collapsed"><span class="property token string">"name"</span>: <span class="token string">&quot;string&quot;</span><span class="token punctuation">,</span></div></li><li><div class="hoverable collapsed"><span class="property token string">"description"</span>: <span class="token string">&quot;string&quot;</span><span class="token punctuation">,</span></div></li><li><div class="hoverable collapsed"><span class="property token string">"type"</span>: <span class="token string">&quot;string&quot;</span><span class="token punctuation">,</span></div></li><li><div class="hoverable collapsed"><span class="property token string">"consumption_type"</span>: <span class="token string">&quot;string&quot;</span><span class="token punctuation">,</span></div></li><li><div class="hoverable collapsed"><span class="property token string">"index"</span>: <span class="token number">0</span><span class="token punctuation">,</span></div></li><li><div class="hoverable collapsed"><span class="property token string">"data_type"</span>: <span class="token string">&quot;string&quot;</span><span class="token punctuation">,</span></div></li><li><div class="hoverable collapsed"><span class="property token string">"default_value"</span>: <span class="token string">&quot;string&quot;</span><span class="token punctuation">,</span></div></li><li><div class="hoverable collapsed"><span class="property token string">"enum"</span>: <button class="collapser" aria-label="expand"></button><span class="token punctuation">[</span><span class="ellipsis"></span><ul class="array collapsible"><li><div class="hoverable collapsed"><span class="token string">&quot;string&quot;</span></div></li></ul><span class="token punctuation">]</span><span class="token punctuation">,</span></div></li><li><div class="hoverable collapsed"><span class="property token string">"is_required"</span>: <span class="token boolean">true</span><span class="token punctuation">,</span></div></li><li><div class="hoverable collapsed"><span class="property token string">"is_array"</span>: <span class="token boolean">true</span><span class="token punctuation">,</span></div></li><li><div class="hoverable collapsed"><span class="property token string">"max_length"</span>: <span class="token number">0</span><span class="token punctuation">,</span></div></li><li><div class="hoverable collapsed"><span class="property token string">"precision"</span>: <span class="token number">0</span><span class="token punctuation">,</span></div></li><li><div class="hoverable collapsed"><span class="property token string">"scale"</span>: <span class="token number">0</span><span class="token punctuation">,</span></div></li><li><div class="hoverable collapsed"><span class="property token string">"properties"</span>: <span class="token punctuation">{ }</span><span class="token punctuation">,</span></div></li><li><div class="hoverable collapsed"><span class="property token string">"custom_properties"</span>: <span class="token punctuation">{ }</span><span class="token punctuation">,</span></div></li><li><div class="hoverable collapsed"><span class="property token string">"tags"</span>: <button class="collapser" aria-label="expand"></button><span class="token punctuation">[</span><span class="ellipsis"></span><ul class="array collapsible"><li><div class="hoverable collapsed"><span class="token string">&quot;string&quot;</span></div></li></ul><span class="token punctuation">]</span><span class="token punctuation">,</span></div></li><li><div class="hoverable collapsed"><span class="property token string">"created_by"</span>: <span class="token string">&quot;string&quot;</span></div></li></ul><span class="token punctuation">}</span></div></li></ul><span class="token punctuation">]</span></div></li></ul><span class="token punctuation">}</span></code></div></div></div></div></div></div><div class="react-tabs__tab-panel" role="tabpanel" id="panel«R4sssq»1" aria-labelledby="tab«R4sssq»1"></div></div></div></div></div></div><div id="tag/Import-DataModel" data-section-id="tag/Import-DataModel" class="sc-dYOLZc kDuBQd"><div class="sc-jZhnRx tMeMv"><div class="sc-gwsNht iRoSfe"><h2 class="sc-kThouk jkwlQD"><a class="sc-kieALA jOWzfS" href="#tag/Import-DataModel" aria-label="tag/Import-DataModel"></a>Import DataModel</h2></div></div><div class="sc-gwsNht ciCSbi"><div class="sc-faJlkc sc-fdduAw kOqeZB kEOvim redoc-markdown " html="&lt;p&gt;Import Data Model Operations&lt;/p&gt;
"><p>Import Data Model Operations</p>
</div></div></div><div id="tag/Import-DataModel/operation/importDataModel" data-section-id="tag/Import-DataModel/operation/importDataModel" class="sc-dYOLZc dRVblm"><div data-section-id="operation/importDataModel" id="operation/importDataModel" class="sc-jZhnRx tMeMv"><div class="sc-gwsNht iRoSfe"><h2 class="sc-kThouk jkwlQD"><a class="sc-kieALA jOWzfS" href="#tag/Import-DataModel/operation/importDataModel" aria-label="tag/Import-DataModel/operation/importDataModel"></a>Import Data Model<!-- --> </h2><div class="sc-bkBUWa fARMpn"><div html="&lt;p&gt;The API enables batch import of data models, accommodating scenarios where the MetaScanner scans technical data models with thousands of tables and columns.&lt;/p&gt;
&lt;p&gt;&lt;strong&gt;How It Works&lt;/strong&gt;&lt;/p&gt;
&lt;ol&gt;
&lt;li&gt;Logical Transaction Identifier:&lt;br&gt;  The entire import process must be handled as a single transaction to ensure the local data model is correctly updated with the scanned data model.
  A &lt;code&gt;scan_id&lt;/code&gt; is generated as part of a SystemScan entry in the Knowledge Graph.
  This &lt;code&gt;scan_id&lt;/code&gt; ensures that all batches are imported as part of the same transaction.&lt;/li&gt;
&lt;li&gt;Batch Import:&lt;br&gt;  Use the import API with the &lt;code&gt;scan_id&lt;/code&gt; to import all batches of the data model.&lt;/li&gt;
&lt;li&gt;Mark Import as Completed:&lt;br&gt;  Once all batches are imported, invoke the API to mark the transaction as complete.&lt;/li&gt;
&lt;/ol&gt;
&lt;p&gt;&lt;strong&gt;Key Points for Consumers&lt;/strong&gt;&lt;/p&gt;
&lt;ul&gt;
&lt;li&gt;Idempotent: The import process is idempotent, ensuring that re-importing the same batch does not create duplicate data.&lt;/li&gt;
&lt;li&gt;&lt;code&gt;scan_id&lt;/code&gt;: The &lt;code&gt;scan_id&lt;/code&gt; is critical for maintaining transactional integrity. It must be provided consistently for all batch imports.&lt;/li&gt;
&lt;li&gt;Single Import: Only one batch import is allowed at a time for a specific data model context.&lt;/li&gt;
&lt;/ul&gt;
" class="sc-faJlkc sc-fdduAw kOqeZB kEOvim"><p>The API enables batch import of data models, accommodating scenarios where the MetaScanner scans technical data models with thousands of tables and columns.</p>
<p><strong>How It Works</strong></p>
<ol>
<li>Logical Transaction Identifier:<br>  The entire import process must be handled as a single transaction to ensure the local data model is correctly updated with the scanned data model.
  A <code>scan_id</code> is generated as part of a SystemScan entry in the Knowledge Graph.
  This <code>scan_id</code> ensures that all batches are imported as part of the same transaction.</li>
<li>Batch Import:<br>  Use the import API with the <code>scan_id</code> to import all batches of the data model.</li>
<li>Mark Import as Completed:<br>  Once all batches are imported, invoke the API to mark the transaction as complete.</li>
</ol>
<p><strong>Key Points for Consumers</strong></p>
<ul>
<li>Idempotent: The import process is idempotent, ensuring that re-importing the same batch does not create duplicate data.</li>
<li><code>scan_id</code>: The <code>scan_id</code> is critical for maintaining transactional integrity. It must be provided consistently for all batch imports.</li>
<li>Single Import: Only one batch import is allowed at a time for a specific data model context.</li>
</ul>
</div></div><div class="sc-hUPhBQ iaIgja"><div class="sc-ilCyfT gqqgtC"><h5 class="sc-elFkmj sc-gUMLBR eUkINt jefPco">Authorizations:</h5><svg class="sc-dibcMh gsIrbT" version="1.1" viewBox="0 0 24 24" x="0" xmlns="http://www.w3.org/2000/svg" y="0" aria-hidden="true"><polygon points="17.3 8.3 12 13.6 6.7 8.3 5.3 9.7 12 16.4 18.7 9.7 "></polygon></svg></div><div class="sc-jlEUfS dNXpfB"><span class="sc-iFRVkA WPZDV"><span class="sc-hFPzkB eSQudw"><i>bearerAuth</i></span></span></div></div><div><h5 class="sc-elFkmj eUkINt">path<!-- --> Parameters</h5><table class="sc-eGjrzz cGiuNU"><tbody><tr class=""><td kind="field" title="context_id" class="sc-kHNKno sc-frmfij cDFxGr kNAbAx"><span class="sc-hLyRwt lhCIDE"></span><span class="property-name">context_id</span><div class="sc-byRegH sc-isZTFa eyIabj iUhYwd">required</div></td><td class="sc-groBii dzPbxx"><div><div><span class="sc-byRegH sc-budtlk eyIabj jGdyyu"></span><span class="sc-byRegH sc-fJvEKN eyIabj fcQkkm">string</span></div> <div><div html="&lt;p&gt;The identifier of the context for which the data model needs to be imported.
The context can be a Datastore or a Subject Area. This is a mandatory parameter.&lt;/p&gt;
" class="sc-faJlkc sc-fdduAw kOqeZB csbUjU"><p>The identifier of the context for which the data model needs to be imported.
The context can be a Datastore or a Subject Area. This is a mandatory parameter.</p>
</div></div></div></td></tr><tr class="last "><td kind="field" title="scan_id" class="sc-kHNKno sc-frmfij cDFxGr kNAbAx"><span class="sc-hLyRwt lhCIDE"></span><span class="property-name">scan_id</span><div class="sc-byRegH sc-isZTFa eyIabj iUhYwd">required</div></td><td class="sc-groBii dzPbxx"><div><div><span class="sc-byRegH sc-budtlk eyIabj jGdyyu"></span><span class="sc-byRegH sc-fJvEKN eyIabj fcQkkm">string</span></div> <div><div html="&lt;p&gt;The unique identifier for the scan.
This identifier is used to track the import process and ensure that all batches are part of the same transaction.
The &lt;code&gt;scan_id&lt;/code&gt; is generated as part of a SystemScan entry in the Knowledge Graph.
It is critical for maintaining transactional integrity and must be provided consistently for all batch imports.&lt;/p&gt;
" class="sc-faJlkc sc-fdduAw kOqeZB csbUjU"><p>The unique identifier for the scan.
This identifier is used to track the import process and ensure that all batches are part of the same transaction.
The <code>scan_id</code> is generated as part of a SystemScan entry in the Knowledge Graph.
It is critical for maintaining transactional integrity and must be provided consistently for all batch imports.</p>
</div></div></div></td></tr></tbody></table></div><h5 class="sc-elFkmj eUkINt">Request Body schema: <span class="sc-edaYAx iFdOsg">application/json</span></h5><div html="" class="sc-faJlkc sc-fdduAw kOqeZB kEOvim"></div><table class="sc-eGjrzz cGiuNU"><tbody><tr class=""><td kind="field" title="entities" class="sc-kHNKno sc-frmfij sc-iyUCga cDFxGr kNAbAx fmWEAp"><span class="sc-hLyRwt lhCIDE"></span><button aria-label="expand entities"><span class="property-name">entities</span><svg class="sc-dibcMh eVNtOe" version="1.1" viewBox="0 0 24 24" x="0" xmlns="http://www.w3.org/2000/svg" y="0" aria-hidden="true"><polygon points="17.3 8.3 12 13.6 6.7 8.3 5.3 9.7 12 16.4 18.7 9.7 "></polygon></svg></button></td><td class="sc-groBii dzPbxx"><div><div><span class="sc-byRegH sc-budtlk eyIabj jGdyyu">Array of </span><span class="sc-byRegH sc-fJvEKN eyIabj fcQkkm">objects</span><span class="sc-byRegH sc-hsXxFb eyIabj iZVMho"> (<!-- -->Entity<!-- -->) </span></div> <div><div html="&lt;p&gt;The list of entities in the data model.
Each entity represents a distinct object or concept within the data model.
The entity can be a table, view, or any other object that is relevant to the data model.
Each entity has a unique name and can have multiple entity elements (fields) associated with it.&lt;/p&gt;
" class="sc-faJlkc sc-fdduAw kOqeZB csbUjU"><p>The list of entities in the data model.
Each entity represents a distinct object or concept within the data model.
The entity can be a table, view, or any other object that is relevant to the data model.
Each entity has a unique name and can have multiple entity elements (fields) associated with it.</p>
</div></div></div></td></tr><tr class="last "><td kind="field" title="relationships" class="sc-kHNKno sc-frmfij sc-iyUCga cDFxGr kNAbAx fmWEAp"><span class="sc-hLyRwt lhCIDE"></span><button aria-label="expand relationships"><span class="property-name">relationships</span><svg class="sc-dibcMh eVNtOe" version="1.1" viewBox="0 0 24 24" x="0" xmlns="http://www.w3.org/2000/svg" y="0" aria-hidden="true"><polygon points="17.3 8.3 12 13.6 6.7 8.3 5.3 9.7 12 16.4 18.7 9.7 "></polygon></svg></button></td><td class="sc-groBii dzPbxx"><div><div><span class="sc-byRegH sc-budtlk eyIabj jGdyyu">Array of </span><span class="sc-byRegH sc-fJvEKN eyIabj fcQkkm">objects</span><span class="sc-byRegH sc-hsXxFb eyIabj iZVMho"> (<!-- -->Relationship<!-- -->) </span></div> <div><div html="&lt;p&gt;The list of relationships in the data model.
Each relationship represents a connection between entities or entity elements.&lt;/p&gt;
" class="sc-faJlkc sc-fdduAw kOqeZB csbUjU"><p>The list of relationships in the data model.
Each relationship represents a connection between entities or entity elements.</p>
</div></div></div></td></tr></tbody></table><div><h3 class="sc-gSVmdp jWGRrJ">Responses</h3><div><button class="sc-jthNAk gfsyZD"><svg class="sc-dibcMh dpIKEE" version="1.1" viewBox="0 0 24 24" x="0" xmlns="http://www.w3.org/2000/svg" y="0" aria-hidden="true"><polygon points="17.3 8.3 12 13.6 6.7 8.3 5.3 9.7 12 16.4 18.7 9.7 "></polygon></svg><strong class="sc-eOObWA ecXPYN">200<!-- --> </strong><div html="&lt;p&gt;Sucessfully imported.&lt;/p&gt;
" class="sc-faJlkc sc-fdduAw sc-eINXvP kOqeZB kkltVg fbuMAW"><p>Sucessfully imported.</p>
</div></button></div><div><button class="sc-jthNAk eNjFmB"><svg class="sc-dibcMh jsRtRU" version="1.1" viewBox="0 0 24 24" x="0" xmlns="http://www.w3.org/2000/svg" y="0" aria-hidden="true"><polygon points="17.3 8.3 12 13.6 6.7 8.3 5.3 9.7 12 16.4 18.7 9.7 "></polygon></svg><strong class="sc-eOObWA ecXPYN">400<!-- --> </strong><div html="&lt;p&gt;Bad Request.&lt;/p&gt;
" class="sc-faJlkc sc-fdduAw sc-eINXvP kOqeZB kkltVg fbuMAW"><p>Bad Request.</p>
</div></button></div><div><button class="sc-jthNAk kdKxoT" disabled=""><strong class="sc-eOObWA ecXPYN">401<!-- --> </strong><div html="&lt;p&gt;Authentication information is expired, missing, or invalid.
Ensure that you have authenticated correctly.&lt;/p&gt;
" class="sc-faJlkc sc-fdduAw sc-eINXvP kOqeZB kkltVg fbuMAW"><p>Authentication information is expired, missing, or invalid.
Ensure that you have authenticated correctly.</p>
</div></button></div><div><button class="sc-jthNAk kdKxoT" disabled=""><strong class="sc-eOObWA ecXPYN">403<!-- --> </strong><div html="&lt;p&gt;Authorization failed or access denied error.
Ensure that you have the necessary permissions.&lt;/p&gt;
" class="sc-faJlkc sc-fdduAw sc-eINXvP kOqeZB kkltVg fbuMAW"><p>Authorization failed or access denied error.
Ensure that you have the necessary permissions.</p>
</div></button></div></div></div><div class="sc-jMpmlX sc-hoLldG kCcXaS jpPJrn"><div class="sc-eUzzst cgvUrj"><button class="sc-bpAAPv ecNgtW"><span type="post" class="sc-fBpBLu bxmdGF http-verb post">post</span><span class="sc-jgoAos hgvsuc">/datamodel-import</span><svg class="sc-dibcMh NOyYc" style="margin-right:-25px" version="1.1" viewBox="0 0 24 24" x="0" xmlns="http://www.w3.org/2000/svg" y="0" aria-hidden="true"><polygon points="17.3 8.3 12 13.6 6.7 8.3 5.3 9.7 12 16.4 18.7 9.7 "></polygon></svg></button><div aria-hidden="true" class="sc-dXqqap bVkkkk"><div class="sc-ijfqWo jlEXoB"><div html="" class="sc-faJlkc sc-fdduAw kOqeZB csbUjU"></div><div tabindex="0" role="button"><div class="sc-srrxr zPpwX"><span>http://catalog.govern.svc.cluster.local</span>/datamodel-import</div></div></div><div class="sc-ijfqWo jlEXoB"><div html="" class="sc-faJlkc sc-fdduAw kOqeZB csbUjU"></div><div tabindex="0" role="button"><div class="sc-srrxr zPpwX"><span>https://ct.mgt.syniti-dev.com</span>/datamodel-import</div></div></div></div></div><div><h3 class="sc-llIIlC fdkIVZ"> <!-- -->Request samples<!-- --> </h3><div class="sc-cTIdZS dFpYBv" data-rttabs="true"><ul class="react-tabs__tab-list" role="tablist"><li class="react-tabs__tab react-tabs__tab--selected" role="tab" id="tab«R4kt4q»0" aria-selected="true" aria-disabled="false" aria-controls="panel«R4kt4q»0" tabindex="0" data-rttab="true">Payload</li></ul><div class="react-tabs__tab-panel react-tabs__tab-panel--selected" role="tabpanel" id="panel«R4kt4q»0" aria-labelledby="tab«R4kt4q»0"><div><div class="sc-bXYrjy fpFYoG"><span class="sc-fUPiRJ TqlOY">Content type</span><div class="sc-bFwXsg caaBgn">application/json</div></div><div class="sc-bgpKpp kuvBhH"><div class="sc-dRHutB iAOzgg"><div class="sc-VILhF itwxyW"><button><div class="sc-fIQtvO indwFD">Copy</div></button><button> Expand all </button><button> Collapse all </button></div><div tabindex="0" class="sc-faJlkc kOqeZB sc-eRJQtA eudsMA"><div class="redoc-json"><code><button class="collapser" aria-label="collapse"></button><span class="token punctuation">{</span><span class="ellipsis"></span><ul class="obj collapsible"><li><div class="hoverable "><span class="property token string">"data_model"</span>: <button class="collapser" aria-label="collapse"></button><span class="token punctuation">[</span><span class="ellipsis"></span><ul class="array collapsible"><li><div class="hoverable collapsed"><button class="collapser" aria-label="expand"></button><span class="token punctuation">{</span><span class="ellipsis"></span><ul class="obj collapsible"><li><div class="hoverable collapsed"><span class="property token string">"model_element_type"</span>: <span class="token string">&quot;entity&quot;</span><span class="token punctuation">,</span></div></li><li><div class="hoverable collapsed"><span class="property token string">"model_name"</span>: <span class="token string">&quot;ns1.Product&quot;</span><span class="token punctuation">,</span></div></li><li><div class="hoverable collapsed"><span class="property token string">"name"</span>: <span class="token string">&quot;Product&quot;</span><span class="token punctuation">,</span></div></li><li><div class="hoverable collapsed"><span class="property token string">"type"</span>: <span class="token string">&quot;TABLE&quot;</span><span class="token punctuation">,</span></div></li><li><div class="hoverable collapsed"><span class="property token string">"consumption_type"</span>: <span class="token string">&quot;dataset&quot;</span><span class="token punctuation">,</span></div></li><li><div class="hoverable collapsed"><span class="property token string">"active"</span>: <span class="token boolean">true</span><span class="token punctuation">,</span></div></li><li><div class="hoverable collapsed"><span class="property token string">"properties"</span>: <button class="collapser" aria-label="expand"></button><span class="token punctuation">{</span><span class="ellipsis"></span><ul class="obj collapsible"><li><div class="hoverable collapsed"><span class="property token string">"schema"</span>: <span class="token string">&quot;Production&quot;</span></div></li></ul><span class="token punctuation">}</span></div></li></ul><span class="token punctuation">}</span>,</div></li><li><div class="hoverable collapsed"><button class="collapser" aria-label="expand"></button><span class="token punctuation">{</span><span class="ellipsis"></span><ul class="obj collapsible"><li><div class="hoverable collapsed"><span class="property token string">"model_element_type"</span>: <span class="token string">&quot;element&quot;</span><span class="token punctuation">,</span></div></li><li><div class="hoverable collapsed"><span class="property token string">"entity"</span>: <span class="token string">&quot;ns1.Product&quot;</span><span class="token punctuation">,</span></div></li><li><div class="hoverable collapsed"><span class="property token string">"model_name"</span>: <span class="token string">&quot;ns1.Product.Shipmentid&quot;</span><span class="token punctuation">,</span></div></li><li><div class="hoverable collapsed"><span class="property token string">"name"</span>: <span class="token string">&quot;ShipmentId&quot;</span><span class="token punctuation">,</span></div></li><li><div class="hoverable collapsed"><span class="property token string">"type"</span>: <span class="token string">&quot;COLUMN&quot;</span><span class="token punctuation">,</span></div></li><li><div class="hoverable collapsed"><span class="property token string">"consumption_type"</span>: <span class="token string">&quot;field&quot;</span><span class="token punctuation">,</span></div></li><li><div class="hoverable collapsed"><span class="property token string">"active"</span>: <span class="token boolean">true</span><span class="token punctuation">,</span></div></li><li><div class="hoverable collapsed"><span class="property token string">"index"</span>: <span class="token number">1</span><span class="token punctuation">,</span></div></li><li><div class="hoverable collapsed"><span class="property token string">"data_type"</span>: <span class="token string">&quot;int&quot;</span><span class="token punctuation">,</span></div></li><li><div class="hoverable collapsed"><span class="property token string">"default_value"</span>: <span class="token string">&quot;&quot;</span><span class="token punctuation">,</span></div></li><li><div class="hoverable collapsed"><span class="property token string">"nulls_allowed"</span>: <span class="token boolean">false</span></div></li></ul><span class="token punctuation">}</span>,</div></li><li><div class="hoverable collapsed"><button class="collapser" aria-label="expand"></button><span class="token punctuation">{</span><span class="ellipsis"></span><ul class="obj collapsible"><li><div class="hoverable collapsed"><span class="property token string">"model_element_type"</span>: <span class="token string">&quot;element&quot;</span><span class="token punctuation">,</span></div></li><li><div class="hoverable collapsed"><span class="property token string">"entity"</span>: <span class="token string">&quot;ns1.Product&quot;</span><span class="token punctuation">,</span></div></li><li><div class="hoverable collapsed"><span class="property token string">"model_name"</span>: <span class="token string">&quot;ns1.Production.Product.PK_Shipmentid&quot;</span><span class="token punctuation">,</span></div></li><li><div class="hoverable collapsed"><span class="property token string">"name"</span>: <span class="token string">&quot;PK_ShipmentId&quot;</span><span class="token punctuation">,</span></div></li><li><div class="hoverable collapsed"><span class="property token string">"type"</span>: <span class="token string">&quot;PRIMARY-KEY&quot;</span><span class="token punctuation">,</span></div></li><li><div class="hoverable collapsed"><span class="property token string">"consumption_type"</span>: <span class="token string">&quot;key&quot;</span></div></li></ul><span class="token punctuation">}</span>,</div></li><li><div class="hoverable collapsed"><button class="collapser" aria-label="expand"></button><span class="token punctuation">{</span><span class="ellipsis"></span><ul class="obj collapsible"><li><div class="hoverable collapsed"><span class="property token string">"model_element_type"</span>: <span class="token string">&quot;relation&quot;</span><span class="token punctuation">,</span></div></li><li><div class="hoverable collapsed"><span class="property token string">"type"</span>: <span class="token string">&quot;PK_REF&quot;</span><span class="token punctuation">,</span></div></li><li><div class="hoverable collapsed"><span class="property token string">"description"</span>: <span class="token string">&quot;&quot;</span><span class="token punctuation">,</span></div></li><li><div class="hoverable collapsed"><span class="property token string">"source"</span>: <span class="token string">&quot;#element/ns1.Product.PK_Shipmentid&quot;</span><span class="token punctuation">,</span></div></li><li><div class="hoverable collapsed"><span class="property token string">"target"</span>: <span class="token string">&quot;#element/ns1.Product.Shipmentid&quot;</span></div></li></ul><span class="token punctuation">}</span>,</div></li><li><div class="hoverable collapsed"><button class="collapser" aria-label="expand"></button><span class="token punctuation">{</span><span class="ellipsis"></span><ul class="obj collapsible"><li><div class="hoverable collapsed"><span class="property token string">"model_element_type"</span>: <span class="token string">&quot;entity&quot;</span><span class="token punctuation">,</span></div></li><li><div class="hoverable collapsed"><span class="property token string">"operation"</span>: <span class="token string">&quot;delete&quot;</span><span class="token punctuation">,</span></div></li><li><div class="hoverable collapsed"><span class="property token string">"name"</span>: <span class="token string">&quot;Employee&quot;</span></div></li></ul><span class="token punctuation">}</span></div></li></ul><span class="token punctuation">]</span></div></li></ul><span class="token punctuation">}</span></code></div></div></div></div></div></div></div></div><div><h3 class="sc-llIIlC fdkIVZ"> <!-- -->Response samples<!-- --> </h3><div class="sc-cTIdZS dFpYBv" data-rttabs="true"><ul class="react-tabs__tab-list" role="tablist"><li class="tab-success react-tabs__tab--selected" role="tab" id="tab«R4st4q»0" aria-selected="true" aria-disabled="false" aria-controls="panel«R4st4q»0" tabindex="0" data-rttab="true">200</li><li class="tab-error" role="tab" id="tab«R4st4q»1" aria-selected="false" aria-disabled="false" aria-controls="panel«R4st4q»1" data-rttab="true">400</li></ul><div class="react-tabs__tab-panel react-tabs__tab-panel--selected" role="tabpanel" id="panel«R4st4q»0" aria-labelledby="tab«R4st4q»0"><div><div class="sc-bXYrjy fpFYoG"><span class="sc-fUPiRJ TqlOY">Content type</span><div class="sc-bFwXsg caaBgn">application/json</div></div><div class="sc-bgpKpp kuvBhH"><div class="sc-dRHutB iAOzgg"><div class="sc-VILhF itwxyW"><button><div class="sc-fIQtvO indwFD">Copy</div></button></div><div tabindex="0" class="sc-faJlkc kOqeZB sc-eRJQtA eudsMA"><div class="redoc-json"><code><button class="collapser" aria-label="collapse"></button><span class="token punctuation">{</span><span class="ellipsis"></span><ul class="obj collapsible"><li><div class="hoverable "><span class="property token string">"new_model_version"</span>: <span class="token string">&quot;20250201-002&quot;</span></div></li></ul><span class="token punctuation">}</span></code></div></div></div></div></div></div><div class="react-tabs__tab-panel" role="tabpanel" id="panel«R4st4q»1" aria-labelledby="tab«R4st4q»1"></div></div></div></div></div></div><div id="tag/Schemas" data-section-id="tag/Schemas" class="sc-dYOLZc kDuBQd"><div class="sc-jZhnRx tMeMv"><div class="sc-gwsNht iRoSfe"><h2 class="sc-kThouk jkwlQD"><a class="sc-kieALA jOWzfS" href="#tag/Schemas" aria-label="tag/Schemas"></a>Schemas</h2></div></div></div><div id="schema/EntityElement" data-section-id="schema/EntityElement" class="sc-dYOLZc kDuBQd"><div class="sc-jZhnRx tMeMv"><div class="sc-gwsNht iRoSfe"><h2 class="sc-kThouk jkwlQD"><a class="sc-kieALA jOWzfS" href="#schema/EntityElement" aria-label="schema/EntityElement"></a>EntityElement</h2></div></div><div class="sc-dYOLZc kDuBQd"><div class="sc-jZhnRx tMeMv"><div class="sc-gwsNht iRoSfe"><table class="sc-eGjrzz cGiuNU"><tbody><tr class=""><td kind="field" title="id" class="sc-kHNKno sc-frmfij cDFxGr kNAbAx"><span class="sc-hLyRwt lhCIDE"></span><span class="property-name">id</span></td><td class="sc-groBii dzPbxx"><div><div><span class="sc-byRegH sc-budtlk eyIabj jGdyyu"></span><span class="sc-byRegH sc-fJvEKN eyIabj fcQkkm">string</span></div> <div><div html="&lt;p&gt;The unique identifier of the element&lt;/p&gt;
" class="sc-faJlkc sc-fdduAw kOqeZB csbUjU"><p>The unique identifier of the element</p>
</div></div></div></td></tr><tr class=""><td kind="field" title="model_name" class="sc-kHNKno sc-frmfij cDFxGr kNAbAx"><span class="sc-hLyRwt lhCIDE"></span><span class="property-name">model_name</span></td><td class="sc-groBii dzPbxx"><div><div><span class="sc-byRegH sc-budtlk eyIabj jGdyyu"></span><span class="sc-byRegH sc-fJvEKN eyIabj fcQkkm">string</span></div> <div><div html="&lt;p&gt;The canonical name of the entity element.
It is used to identify the entity element in the context of the data model and for cross references.&lt;/p&gt;
" class="sc-faJlkc sc-fdduAw kOqeZB csbUjU"><p>The canonical name of the entity element.
It is used to identify the entity element in the context of the data model and for cross references.</p>
</div></div></div></td></tr><tr class=""><td kind="field" title="name" class="sc-kHNKno sc-frmfij cDFxGr kNAbAx"><span class="sc-hLyRwt lhCIDE"></span><span class="property-name">name</span><div class="sc-byRegH sc-isZTFa eyIabj iUhYwd">required</div></td><td class="sc-groBii dzPbxx"><div><div><span class="sc-byRegH sc-budtlk eyIabj jGdyyu"></span><span class="sc-byRegH sc-fJvEKN eyIabj fcQkkm">string</span></div> <div><div html="&lt;p&gt;The name of the entity element.
It should be unique within the Entity.
The name should follow the naming conventions and should not contain any special characters or spaces.&lt;/p&gt;
" class="sc-faJlkc sc-fdduAw kOqeZB csbUjU"><p>The name of the entity element.
It should be unique within the Entity.
The name should follow the naming conventions and should not contain any special characters or spaces.</p>
</div></div></div></td></tr><tr class=""><td kind="field" title="description" class="sc-kHNKno sc-frmfij cDFxGr kNAbAx"><span class="sc-hLyRwt lhCIDE"></span><span class="property-name">description</span></td><td class="sc-groBii dzPbxx"><div><div><span class="sc-byRegH sc-budtlk eyIabj jGdyyu"></span><span class="sc-byRegH sc-fJvEKN eyIabj fcQkkm">string</span></div> <div><div html="&lt;p&gt;A description of the element.&lt;/p&gt;
" class="sc-faJlkc sc-fdduAw kOqeZB csbUjU"><p>A description of the element.</p>
</div></div></div></td></tr><tr class=""><td kind="field" title="type" class="sc-kHNKno sc-frmfij cDFxGr kNAbAx"><span class="sc-hLyRwt lhCIDE"></span><span class="property-name">type</span><div class="sc-byRegH sc-isZTFa eyIabj iUhYwd">required</div></td><td class="sc-groBii dzPbxx"><div><div><span class="sc-byRegH sc-budtlk eyIabj jGdyyu"></span><span class="sc-byRegH sc-fJvEKN eyIabj fcQkkm">string</span></div> <div><div html="&lt;p&gt;The actual source type of element.
It can be a Column, Primary Key, or any other object that is relevant to the data model.
Please refer the meta_schema_version documentation for the list of valid entity types.&lt;/p&gt;
" class="sc-faJlkc sc-fdduAw kOqeZB csbUjU"><p>The actual source type of element.
It can be a Column, Primary Key, or any other object that is relevant to the data model.
Please refer the meta_schema_version documentation for the list of valid entity types.</p>
</div></div></div></td></tr><tr class=""><td kind="field" title="consumption_type" class="sc-kHNKno sc-frmfij cDFxGr kNAbAx"><span class="sc-hLyRwt lhCIDE"></span><span class="property-name">consumption_type</span><div class="sc-byRegH sc-isZTFa eyIabj iUhYwd">required</div></td><td class="sc-groBii dzPbxx"><div><div><span class="sc-byRegH sc-budtlk eyIabj jGdyyu"></span><span class="sc-byRegH sc-fJvEKN eyIabj fcQkkm">string</span></div> <div><div html="&lt;p&gt;The normalized type of element as consumed by various SKP applications.
Please refer the meta_schema_version documentation for the list of valid entity element types.&lt;/p&gt;
" class="sc-faJlkc sc-fdduAw kOqeZB csbUjU"><p>The normalized type of element as consumed by various SKP applications.
Please refer the meta_schema_version documentation for the list of valid entity element types.</p>
</div></div></div></td></tr><tr class=""><td kind="field" title="index" class="sc-kHNKno sc-frmfij cDFxGr kNAbAx"><span class="sc-hLyRwt lhCIDE"></span><span class="property-name">index</span></td><td class="sc-groBii dzPbxx"><div><div><span class="sc-byRegH sc-budtlk eyIabj jGdyyu"></span><span class="sc-byRegH sc-fJvEKN eyIabj fcQkkm">integer</span></div> <div><div html="&lt;p&gt;Indicates the ordinal position of this element.
Default is 0.&lt;/p&gt;
" class="sc-faJlkc sc-fdduAw kOqeZB csbUjU"><p>Indicates the ordinal position of this element.
Default is 0.</p>
</div></div></div></td></tr><tr class=""><td kind="field" title="data_type" class="sc-kHNKno sc-frmfij cDFxGr kNAbAx"><span class="sc-hLyRwt lhCIDE"></span><span class="property-name">data_type</span></td><td class="sc-groBii dzPbxx"><div><div><span class="sc-byRegH sc-budtlk eyIabj jGdyyu"></span><span class="sc-byRegH sc-fJvEKN eyIabj fcQkkm">string</span></div> <div><div html="&lt;p&gt;The underlying native data type associated with this element.
It can be a string, integer, float, or any other data type that is relevant to the data model.
Please refer the meta_schema_version documentation for the list of valid data types.&lt;/p&gt;
" class="sc-faJlkc sc-fdduAw kOqeZB csbUjU"><p>The underlying native data type associated with this element.
It can be a string, integer, float, or any other data type that is relevant to the data model.
Please refer the meta_schema_version documentation for the list of valid data types.</p>
</div></div></div></td></tr><tr class=""><td kind="field" title="default_value" class="sc-kHNKno sc-frmfij cDFxGr kNAbAx"><span class="sc-hLyRwt lhCIDE"></span><span class="property-name">default_value</span></td><td class="sc-groBii dzPbxx"><div><div><span class="sc-byRegH sc-budtlk eyIabj jGdyyu"></span><span class="sc-byRegH sc-fJvEKN eyIabj fcQkkm">string</span></div> <div><div html="&lt;p&gt;The default value to be used for this element&lt;/p&gt;
" class="sc-faJlkc sc-fdduAw kOqeZB csbUjU"><p>The default value to be used for this element</p>
</div></div></div></td></tr><tr class=""><td kind="field" title="enum" class="sc-kHNKno sc-frmfij cDFxGr kNAbAx"><span class="sc-hLyRwt lhCIDE"></span><span class="property-name">enum</span></td><td class="sc-groBii dzPbxx"><div><div><span class="sc-byRegH sc-budtlk eyIabj jGdyyu">Array of </span><span class="sc-byRegH sc-fJvEKN eyIabj fcQkkm">strings</span></div> <div><div html="&lt;p&gt;A list of possible values for the element.
This is used to restrict the values that can be assigned to the element.&lt;/p&gt;
" class="sc-faJlkc sc-fdduAw kOqeZB csbUjU"><p>A list of possible values for the element.
This is used to restrict the values that can be assigned to the element.</p>
</div></div></div></td></tr><tr class=""><td kind="field" title="is_required" class="sc-kHNKno sc-frmfij cDFxGr kNAbAx"><span class="sc-hLyRwt lhCIDE"></span><span class="property-name">is_required</span></td><td class="sc-groBii dzPbxx"><div><div><span class="sc-byRegH sc-budtlk eyIabj jGdyyu"></span><span class="sc-byRegH sc-fJvEKN eyIabj fcQkkm">boolean</span></div> <div><div html="&lt;p&gt;Indicates if the element is a required property.
Default is false.&lt;/p&gt;
" class="sc-faJlkc sc-fdduAw kOqeZB csbUjU"><p>Indicates if the element is a required property.
Default is false.</p>
</div></div></div></td></tr><tr class=""><td kind="field" title="is_array" class="sc-kHNKno sc-frmfij cDFxGr kNAbAx"><span class="sc-hLyRwt lhCIDE"></span><span class="property-name">is_array</span></td><td class="sc-groBii dzPbxx"><div><div><span class="sc-byRegH sc-budtlk eyIabj jGdyyu"></span><span class="sc-byRegH sc-fJvEKN eyIabj fcQkkm">boolean</span></div> <div><div html="&lt;p&gt;Indicates if the element is an array.
Default is false.&lt;/p&gt;
" class="sc-faJlkc sc-fdduAw kOqeZB csbUjU"><p>Indicates if the element is an array.
Default is false.</p>
</div></div></div></td></tr><tr class=""><td kind="field" title="max_length" class="sc-kHNKno sc-frmfij cDFxGr kNAbAx"><span class="sc-hLyRwt lhCIDE"></span><span class="property-name">max_length</span></td><td class="sc-groBii dzPbxx"><div><div><span class="sc-byRegH sc-budtlk eyIabj jGdyyu"></span><span class="sc-byRegH sc-fJvEKN eyIabj fcQkkm">integer</span></div> <div><div html="&lt;p&gt;The maximum length of the element.
This is used to specify the maximum number of characters that can be stored in the element.&lt;/p&gt;
" class="sc-faJlkc sc-fdduAw kOqeZB csbUjU"><p>The maximum length of the element.
This is used to specify the maximum number of characters that can be stored in the element.</p>
</div></div></div></td></tr><tr class=""><td kind="field" title="precision" class="sc-kHNKno sc-frmfij cDFxGr kNAbAx"><span class="sc-hLyRwt lhCIDE"></span><span class="property-name">precision</span></td><td class="sc-groBii dzPbxx"><div><div><span class="sc-byRegH sc-budtlk eyIabj jGdyyu"></span><span class="sc-byRegH sc-fJvEKN eyIabj fcQkkm">integer</span></div> <div><div html="&lt;p&gt;The precision of the element.
This is used to specify the number of digits that can be stored in the element.&lt;/p&gt;
" class="sc-faJlkc sc-fdduAw kOqeZB csbUjU"><p>The precision of the element.
This is used to specify the number of digits that can be stored in the element.</p>
</div></div></div></td></tr><tr class=""><td kind="field" title="scale" class="sc-kHNKno sc-frmfij cDFxGr kNAbAx"><span class="sc-hLyRwt lhCIDE"></span><span class="property-name">scale</span></td><td class="sc-groBii dzPbxx"><div><div><span class="sc-byRegH sc-budtlk eyIabj jGdyyu"></span><span class="sc-byRegH sc-fJvEKN eyIabj fcQkkm">integer</span></div> <div><div html="&lt;p&gt;The scale of the element.
This is used to specify the number of digits that can be stored in the element after the decimal point.&lt;/p&gt;
" class="sc-faJlkc sc-fdduAw kOqeZB csbUjU"><p>The scale of the element.
This is used to specify the number of digits that can be stored in the element after the decimal point.</p>
</div></div></div></td></tr><tr class=""><td kind="field" title="properties" class="sc-kHNKno sc-frmfij cDFxGr kNAbAx"><span class="sc-hLyRwt lhCIDE"></span><span class="property-name">properties</span></td><td class="sc-groBii dzPbxx"><div><div><span class="sc-byRegH sc-budtlk eyIabj jGdyyu"></span><span class="sc-byRegH sc-fJvEKN eyIabj fcQkkm">object</span></div> <div><div html="&lt;p&gt;Type specific properties.
A map of key value pairs that captures the properties specific to the element type.
For example, if the type=INDEX, then we capture &lt;code&gt;is_clustered&lt;/code&gt; key value.
Please see the meta_schema_version documentation for the list of valid keys.&lt;/p&gt;
" class="sc-faJlkc sc-fdduAw kOqeZB csbUjU"><p>Type specific properties.
A map of key value pairs that captures the properties specific to the element type.
For example, if the type=INDEX, then we capture <code>is_clustered</code> key value.
Please see the meta_schema_version documentation for the list of valid keys.</p>
</div></div></div></td></tr><tr class=""><td kind="field" title="custom_properties" class="sc-kHNKno sc-frmfij cDFxGr kNAbAx"><span class="sc-hLyRwt lhCIDE"></span><span class="property-name">custom_properties</span></td><td class="sc-groBii dzPbxx"><div><div><span class="sc-byRegH sc-budtlk eyIabj jGdyyu"></span><span class="sc-byRegH sc-fJvEKN eyIabj fcQkkm">object</span></div> <div><div html="&lt;p&gt;Custom properties for the entity element. &lt;code&gt;Check with John if this is required&lt;/code&gt;.`
A map of key value pairs that captures the custom properties specific to the element.
The keys are defined by the consumer and can be used to capture any additional information about the element.&lt;/p&gt;
" class="sc-faJlkc sc-fdduAw kOqeZB csbUjU"><p>Custom properties for the entity element. <code>Check with John if this is required</code>.`
A map of key value pairs that captures the custom properties specific to the element.
The keys are defined by the consumer and can be used to capture any additional information about the element.</p>
</div></div></div></td></tr><tr class=""><td kind="field" title="tags" class="sc-kHNKno sc-frmfij cDFxGr kNAbAx"><span class="sc-hLyRwt lhCIDE"></span><span class="property-name">tags</span></td><td class="sc-groBii dzPbxx"><div><div><span class="sc-byRegH sc-budtlk eyIabj jGdyyu">Array of </span><span class="sc-byRegH sc-fJvEKN eyIabj fcQkkm">strings</span></div> <div><div html="&lt;p&gt;A list of tags associated with the entity element.
Tags can be used to categorize or label the element for easier identification and management.
Tags can be used for filtering and searching elements within the data model.&lt;/p&gt;
" class="sc-faJlkc sc-fdduAw kOqeZB csbUjU"><p>A list of tags associated with the entity element.
Tags can be used to categorize or label the element for easier identification and management.
Tags can be used for filtering and searching elements within the data model.</p>
</div></div></div></td></tr><tr class="last "><td kind="field" title="created_by" class="sc-kHNKno sc-frmfij cDFxGr kNAbAx"><span class="sc-hLyRwt lhCIDE"></span><span class="property-name">created_by</span></td><td class="sc-groBii dzPbxx"><div><div><span class="sc-byRegH sc-budtlk eyIabj jGdyyu"></span><span class="sc-byRegH sc-fJvEKN eyIabj fcQkkm">string</span></div> <div><div html="&lt;p&gt;The user who created the entity element.
This is usually a system generated value and is used for auditing purposes.&lt;/p&gt;
" class="sc-faJlkc sc-fdduAw kOqeZB csbUjU"><p>The user who created the entity element.
This is usually a system generated value and is used for auditing purposes.</p>
</div></div></div></td></tr></tbody></table></div><div class="sc-jMpmlX sc-hoLldG kCcXaS jpPJrn"><div class="sc-cnVfeA eJpgoQ"><div class="sc-bgpKpp kuvBhH"><div class="sc-dRHutB iAOzgg"><div class="sc-VILhF itwxyW"><button><div class="sc-fIQtvO indwFD">Copy</div></button><button> Expand all </button><button> Collapse all </button></div><div tabindex="0" class="sc-faJlkc kOqeZB sc-eRJQtA eudsMA"><div class="redoc-json"><code><button class="collapser" aria-label="collapse"></button><span class="token punctuation">{</span><span class="ellipsis"></span><ul class="obj collapsible"><li><div class="hoverable "><span class="property token string">"id"</span>: <span class="token string">&quot;string&quot;</span><span class="token punctuation">,</span></div></li><li><div class="hoverable "><span class="property token string">"model_name"</span>: <span class="token string">&quot;string&quot;</span><span class="token punctuation">,</span></div></li><li><div class="hoverable "><span class="property token string">"name"</span>: <span class="token string">&quot;string&quot;</span><span class="token punctuation">,</span></div></li><li><div class="hoverable "><span class="property token string">"description"</span>: <span class="token string">&quot;string&quot;</span><span class="token punctuation">,</span></div></li><li><div class="hoverable "><span class="property token string">"type"</span>: <span class="token string">&quot;string&quot;</span><span class="token punctuation">,</span></div></li><li><div class="hoverable "><span class="property token string">"consumption_type"</span>: <span class="token string">&quot;string&quot;</span><span class="token punctuation">,</span></div></li><li><div class="hoverable "><span class="property token string">"index"</span>: <span class="token number">0</span><span class="token punctuation">,</span></div></li><li><div class="hoverable "><span class="property token string">"data_type"</span>: <span class="token string">&quot;string&quot;</span><span class="token punctuation">,</span></div></li><li><div class="hoverable "><span class="property token string">"default_value"</span>: <span class="token string">&quot;string&quot;</span><span class="token punctuation">,</span></div></li><li><div class="hoverable "><span class="property token string">"enum"</span>: <button class="collapser" aria-label="collapse"></button><span class="token punctuation">[</span><span class="ellipsis"></span><ul class="array collapsible"><li><div class="hoverable collapsed"><span class="token string">&quot;string&quot;</span></div></li></ul><span class="token punctuation">]</span><span class="token punctuation">,</span></div></li><li><div class="hoverable "><span class="property token string">"is_required"</span>: <span class="token boolean">true</span><span class="token punctuation">,</span></div></li><li><div class="hoverable "><span class="property token string">"is_array"</span>: <span class="token boolean">true</span><span class="token punctuation">,</span></div></li><li><div class="hoverable "><span class="property token string">"max_length"</span>: <span class="token number">0</span><span class="token punctuation">,</span></div></li><li><div class="hoverable "><span class="property token string">"precision"</span>: <span class="token number">0</span><span class="token punctuation">,</span></div></li><li><div class="hoverable "><span class="property token string">"scale"</span>: <span class="token number">0</span><span class="token punctuation">,</span></div></li><li><div class="hoverable "><span class="property token string">"properties"</span>: <span class="token punctuation">{ }</span><span class="token punctuation">,</span></div></li><li><div class="hoverable "><span class="property token string">"custom_properties"</span>: <span class="token punctuation">{ }</span><span class="token punctuation">,</span></div></li><li><div class="hoverable "><span class="property token string">"tags"</span>: <button class="collapser" aria-label="collapse"></button><span class="token punctuation">[</span><span class="ellipsis"></span><ul class="array collapsible"><li><div class="hoverable collapsed"><span class="token string">&quot;string&quot;</span></div></li></ul><span class="token punctuation">]</span><span class="token punctuation">,</span></div></li><li><div class="hoverable "><span class="property token string">"created_by"</span>: <span class="token string">&quot;string&quot;</span></div></li></ul><span class="token punctuation">}</span></code></div></div></div></div></div></div></div></div></div><div id="schema/Entity" data-section-id="schema/Entity" class="sc-dYOLZc kDuBQd"><div class="sc-jZhnRx tMeMv"><div class="sc-gwsNht iRoSfe"><h2 class="sc-kThouk jkwlQD"><a class="sc-kieALA jOWzfS" href="#schema/Entity" aria-label="schema/Entity"></a>Entity</h2></div></div><div class="sc-dYOLZc kDuBQd"><div class="sc-jZhnRx tMeMv"><div class="sc-gwsNht iRoSfe"><table class="sc-eGjrzz cGiuNU"><tbody><tr class=""><td kind="field" title="id" class="sc-kHNKno sc-frmfij cDFxGr kNAbAx"><span class="sc-hLyRwt lhCIDE"></span><span class="property-name">id</span></td><td class="sc-groBii dzPbxx"><div><div><span class="sc-byRegH sc-budtlk eyIabj jGdyyu"></span><span class="sc-byRegH sc-fJvEKN eyIabj fcQkkm">string</span></div> <div><div html="&lt;p&gt;The unique identifier of the Entity&lt;/p&gt;
" class="sc-faJlkc sc-fdduAw kOqeZB csbUjU"><p>The unique identifier of the Entity</p>
</div></div></div></td></tr><tr class=""><td kind="field" title="model_name" class="sc-kHNKno sc-frmfij cDFxGr kNAbAx"><span class="sc-hLyRwt lhCIDE"></span><span class="property-name">model_name</span></td><td class="sc-groBii dzPbxx"><div><div><span class="sc-byRegH sc-budtlk eyIabj jGdyyu"></span><span class="sc-byRegH sc-fJvEKN eyIabj fcQkkm">string</span></div> <div><div html="&lt;p&gt;The canonical name of the entity.
It is used to identify the entity in the context of the data model and for cross references.&lt;/p&gt;
" class="sc-faJlkc sc-fdduAw kOqeZB csbUjU"><p>The canonical name of the entity.
It is used to identify the entity in the context of the data model and for cross references.</p>
</div></div></div></td></tr><tr class=""><td kind="field" title="name" class="sc-kHNKno sc-frmfij cDFxGr kNAbAx"><span class="sc-hLyRwt lhCIDE"></span><span class="property-name">name</span><div class="sc-byRegH sc-isZTFa eyIabj iUhYwd">required</div></td><td class="sc-groBii dzPbxx"><div><div><span class="sc-byRegH sc-budtlk eyIabj jGdyyu"></span><span class="sc-byRegH sc-fJvEKN eyIabj fcQkkm">string</span></div> <div><div html="&lt;p&gt;The name of the entity.
It should be unique within the context of the data model.
The name should follow the naming conventions and should not contain any special characters or spaces.&lt;/p&gt;
" class="sc-faJlkc sc-fdduAw kOqeZB csbUjU"><p>The name of the entity.
It should be unique within the context of the data model.
The name should follow the naming conventions and should not contain any special characters or spaces.</p>
</div></div></div></td></tr><tr class=""><td kind="field" title="description" class="sc-kHNKno sc-frmfij cDFxGr kNAbAx"><span class="sc-hLyRwt lhCIDE"></span><span class="property-name">description</span></td><td class="sc-groBii dzPbxx"><div><div><span class="sc-byRegH sc-budtlk eyIabj jGdyyu"></span><span class="sc-byRegH sc-fJvEKN eyIabj fcQkkm">string</span></div> <div><div html="&lt;p&gt;A description of the entity.&lt;/p&gt;
" class="sc-faJlkc sc-fdduAw kOqeZB csbUjU"><p>A description of the entity.</p>
</div></div></div></td></tr><tr class=""><td kind="field" title="type" class="sc-kHNKno sc-frmfij cDFxGr kNAbAx"><span class="sc-hLyRwt lhCIDE"></span><span class="property-name">type</span><div class="sc-byRegH sc-isZTFa eyIabj iUhYwd">required</div></td><td class="sc-groBii dzPbxx"><div><div><span class="sc-byRegH sc-budtlk eyIabj jGdyyu"></span><span class="sc-byRegH sc-fJvEKN eyIabj fcQkkm">string</span></div> <div><div html="&lt;p&gt;The actual source type of entity.
It can be a table, view, or any other object that is relevant to the data model.
Please refer the meta_schema_version documentation for the list of valid entity types.&lt;/p&gt;
" class="sc-faJlkc sc-fdduAw kOqeZB csbUjU"><p>The actual source type of entity.
It can be a table, view, or any other object that is relevant to the data model.
Please refer the meta_schema_version documentation for the list of valid entity types.</p>
</div></div></div></td></tr><tr class=""><td kind="field" title="consumption_type" class="sc-kHNKno sc-frmfij cDFxGr kNAbAx"><span class="sc-hLyRwt lhCIDE"></span><span class="property-name">consumption_type</span><div class="sc-byRegH sc-isZTFa eyIabj iUhYwd">required</div></td><td class="sc-groBii dzPbxx"><div><div><span class="sc-byRegH sc-budtlk eyIabj jGdyyu"></span><span class="sc-byRegH sc-fJvEKN eyIabj fcQkkm">string</span></div> <div><div html="&lt;p&gt;The normalized type of entity as consumed by various SKP applications.
Please refer the meta_schema_version documentation for the list of valid entity types.&lt;/p&gt;
" class="sc-faJlkc sc-fdduAw kOqeZB csbUjU"><p>The normalized type of entity as consumed by various SKP applications.
Please refer the meta_schema_version documentation for the list of valid entity types.</p>
</div></div></div></td></tr><tr class=""><td kind="field" title="index" class="sc-kHNKno sc-frmfij cDFxGr kNAbAx"><span class="sc-hLyRwt lhCIDE"></span><span class="property-name">index</span></td><td class="sc-groBii dzPbxx"><div><div><span class="sc-byRegH sc-budtlk eyIabj jGdyyu"></span><span class="sc-byRegH sc-fJvEKN eyIabj fcQkkm">integer</span></div> <div><div html="&lt;p&gt;Indicates the ordinal position of this entity.
Default is 0.&lt;/p&gt;
" class="sc-faJlkc sc-fdduAw kOqeZB csbUjU"><p>Indicates the ordinal position of this entity.
Default is 0.</p>
</div></div></div></td></tr><tr class=""><td kind="field" title="properties" class="sc-kHNKno sc-frmfij cDFxGr kNAbAx"><span class="sc-hLyRwt lhCIDE"></span><span class="property-name">properties</span></td><td class="sc-groBii dzPbxx"><div><div><span class="sc-byRegH sc-budtlk eyIabj jGdyyu"></span><span class="sc-byRegH sc-fJvEKN eyIabj fcQkkm">object</span></div> <div><div html="&lt;p&gt;Type specific properties.
A map of key value pairs that captures the properties specific to the entity type.
For example, if the type=TABLE, then we capture &lt;code&gt;schema&lt;/code&gt; key value.
Please see the meta_schema_version documentation for the list of valid keys.&lt;/p&gt;
" class="sc-faJlkc sc-fdduAw kOqeZB csbUjU"><p>Type specific properties.
A map of key value pairs that captures the properties specific to the entity type.
For example, if the type=TABLE, then we capture <code>schema</code> key value.
Please see the meta_schema_version documentation for the list of valid keys.</p>
</div></div></div></td></tr><tr class=""><td kind="field" title="custom_properties" class="sc-kHNKno sc-frmfij cDFxGr kNAbAx"><span class="sc-hLyRwt lhCIDE"></span><span class="property-name">custom_properties</span></td><td class="sc-groBii dzPbxx"><div><div><span class="sc-byRegH sc-budtlk eyIabj jGdyyu"></span><span class="sc-byRegH sc-fJvEKN eyIabj fcQkkm">object</span></div> <div><div html="&lt;p&gt;Custom properties for the entity.
A map of key value pairs that captures the custom properties specific to the entity.
The keys are defined by the consumer and can be used to capture any additional information about the entity.&lt;/p&gt;
" class="sc-faJlkc sc-fdduAw kOqeZB csbUjU"><p>Custom properties for the entity.
A map of key value pairs that captures the custom properties specific to the entity.
The keys are defined by the consumer and can be used to capture any additional information about the entity.</p>
</div></div></div></td></tr><tr class=""><td kind="field" title="tags" class="sc-kHNKno sc-frmfij cDFxGr kNAbAx"><span class="sc-hLyRwt lhCIDE"></span><span class="property-name">tags</span></td><td class="sc-groBii dzPbxx"><div><div><span class="sc-byRegH sc-budtlk eyIabj jGdyyu">Array of </span><span class="sc-byRegH sc-fJvEKN eyIabj fcQkkm">strings</span></div> <div><div html="&lt;p&gt;A list of tags associated with the entity.
Tags can be used to categorize or label the entity for easier identification and management.
Tags can be used for filtering and searching entities within the data model.&lt;/p&gt;
" class="sc-faJlkc sc-fdduAw kOqeZB csbUjU"><p>A list of tags associated with the entity.
Tags can be used to categorize or label the entity for easier identification and management.
Tags can be used for filtering and searching entities within the data model.</p>
</div></div></div></td></tr><tr class=""><td kind="field" title="created_by" class="sc-kHNKno sc-frmfij cDFxGr kNAbAx"><span class="sc-hLyRwt lhCIDE"></span><span class="property-name">created_by</span></td><td class="sc-groBii dzPbxx"><div><div><span class="sc-byRegH sc-budtlk eyIabj jGdyyu"></span><span class="sc-byRegH sc-fJvEKN eyIabj fcQkkm">string</span></div> <div><div html="&lt;p&gt;The user who created the entity.
This is usually a system generated value and is used for auditing purposes.&lt;/p&gt;
" class="sc-faJlkc sc-fdduAw kOqeZB csbUjU"><p>The user who created the entity.
This is usually a system generated value and is used for auditing purposes.</p>
</div></div></div></td></tr><tr class="last "><td kind="field" title="entity_elements" class="sc-kHNKno sc-frmfij sc-iyUCga cDFxGr kNAbAx fmWEAp"><span class="sc-hLyRwt lhCIDE"></span><button aria-label="expand entity_elements"><span class="property-name">entity_elements</span><svg class="sc-dibcMh eVNtOe" version="1.1" viewBox="0 0 24 24" x="0" xmlns="http://www.w3.org/2000/svg" y="0" aria-hidden="true"><polygon points="17.3 8.3 12 13.6 6.7 8.3 5.3 9.7 12 16.4 18.7 9.7 "></polygon></svg></button></td><td class="sc-groBii dzPbxx"><div><div><span class="sc-byRegH sc-budtlk eyIabj jGdyyu">Array of </span><span class="sc-byRegH sc-fJvEKN eyIabj fcQkkm">objects</span><span class="sc-byRegH sc-hsXxFb eyIabj iZVMho"> (<!-- -->EntityElement<!-- -->) </span></div> <div><div html="&lt;p&gt;The list of entity elements (fields) associated with the entity.
For example, in case of relation database model, a database Column is represented as an entity element.
Each entity element has a unique name.&lt;/p&gt;
" class="sc-faJlkc sc-fdduAw kOqeZB csbUjU"><p>The list of entity elements (fields) associated with the entity.
For example, in case of relation database model, a database Column is represented as an entity element.
Each entity element has a unique name.</p>
</div></div></div></td></tr></tbody></table></div><div class="sc-jMpmlX sc-hoLldG kCcXaS jpPJrn"><div class="sc-cnVfeA eJpgoQ"><div class="sc-bgpKpp kuvBhH"><div class="sc-dRHutB iAOzgg"><div class="sc-VILhF itwxyW"><button><div class="sc-fIQtvO indwFD">Copy</div></button><button> Expand all </button><button> Collapse all </button></div><div tabindex="0" class="sc-faJlkc kOqeZB sc-eRJQtA eudsMA"><div class="redoc-json"><code><button class="collapser" aria-label="collapse"></button><span class="token punctuation">{</span><span class="ellipsis"></span><ul class="obj collapsible"><li><div class="hoverable "><span class="property token string">"id"</span>: <span class="token string">&quot;string&quot;</span><span class="token punctuation">,</span></div></li><li><div class="hoverable "><span class="property token string">"model_name"</span>: <span class="token string">&quot;string&quot;</span><span class="token punctuation">,</span></div></li><li><div class="hoverable "><span class="property token string">"name"</span>: <span class="token string">&quot;string&quot;</span><span class="token punctuation">,</span></div></li><li><div class="hoverable "><span class="property token string">"description"</span>: <span class="token string">&quot;string&quot;</span><span class="token punctuation">,</span></div></li><li><div class="hoverable "><span class="property token string">"type"</span>: <span class="token string">&quot;string&quot;</span><span class="token punctuation">,</span></div></li><li><div class="hoverable "><span class="property token string">"consumption_type"</span>: <span class="token string">&quot;string&quot;</span><span class="token punctuation">,</span></div></li><li><div class="hoverable "><span class="property token string">"index"</span>: <span class="token number">0</span><span class="token punctuation">,</span></div></li><li><div class="hoverable "><span class="property token string">"properties"</span>: <span class="token punctuation">{ }</span><span class="token punctuation">,</span></div></li><li><div class="hoverable "><span class="property token string">"custom_properties"</span>: <span class="token punctuation">{ }</span><span class="token punctuation">,</span></div></li><li><div class="hoverable "><span class="property token string">"tags"</span>: <button class="collapser" aria-label="collapse"></button><span class="token punctuation">[</span><span class="ellipsis"></span><ul class="array collapsible"><li><div class="hoverable collapsed"><span class="token string">&quot;string&quot;</span></div></li></ul><span class="token punctuation">]</span><span class="token punctuation">,</span></div></li><li><div class="hoverable "><span class="property token string">"created_by"</span>: <span class="token string">&quot;string&quot;</span><span class="token punctuation">,</span></div></li><li><div class="hoverable "><span class="property token string">"entity_elements"</span>: <button class="collapser" aria-label="collapse"></button><span class="token punctuation">[</span><span class="ellipsis"></span><ul class="array collapsible"><li><div class="hoverable collapsed"><button class="collapser" aria-label="expand"></button><span class="token punctuation">{</span><span class="ellipsis"></span><ul class="obj collapsible"><li><div class="hoverable collapsed"><span class="property token string">"id"</span>: <span class="token string">&quot;string&quot;</span><span class="token punctuation">,</span></div></li><li><div class="hoverable collapsed"><span class="property token string">"model_name"</span>: <span class="token string">&quot;string&quot;</span><span class="token punctuation">,</span></div></li><li><div class="hoverable collapsed"><span class="property token string">"name"</span>: <span class="token string">&quot;string&quot;</span><span class="token punctuation">,</span></div></li><li><div class="hoverable collapsed"><span class="property token string">"description"</span>: <span class="token string">&quot;string&quot;</span><span class="token punctuation">,</span></div></li><li><div class="hoverable collapsed"><span class="property token string">"type"</span>: <span class="token string">&quot;string&quot;</span><span class="token punctuation">,</span></div></li><li><div class="hoverable collapsed"><span class="property token string">"consumption_type"</span>: <span class="token string">&quot;string&quot;</span><span class="token punctuation">,</span></div></li><li><div class="hoverable collapsed"><span class="property token string">"index"</span>: <span class="token number">0</span><span class="token punctuation">,</span></div></li><li><div class="hoverable collapsed"><span class="property token string">"data_type"</span>: <span class="token string">&quot;string&quot;</span><span class="token punctuation">,</span></div></li><li><div class="hoverable collapsed"><span class="property token string">"default_value"</span>: <span class="token string">&quot;string&quot;</span><span class="token punctuation">,</span></div></li><li><div class="hoverable collapsed"><span class="property token string">"enum"</span>: <button class="collapser" aria-label="expand"></button><span class="token punctuation">[</span><span class="ellipsis"></span><ul class="array collapsible"><li><div class="hoverable collapsed"><span class="token string">&quot;string&quot;</span></div></li></ul><span class="token punctuation">]</span><span class="token punctuation">,</span></div></li><li><div class="hoverable collapsed"><span class="property token string">"is_required"</span>: <span class="token boolean">true</span><span class="token punctuation">,</span></div></li><li><div class="hoverable collapsed"><span class="property token string">"is_array"</span>: <span class="token boolean">true</span><span class="token punctuation">,</span></div></li><li><div class="hoverable collapsed"><span class="property token string">"max_length"</span>: <span class="token number">0</span><span class="token punctuation">,</span></div></li><li><div class="hoverable collapsed"><span class="property token string">"precision"</span>: <span class="token number">0</span><span class="token punctuation">,</span></div></li><li><div class="hoverable collapsed"><span class="property token string">"scale"</span>: <span class="token number">0</span><span class="token punctuation">,</span></div></li><li><div class="hoverable collapsed"><span class="property token string">"properties"</span>: <span class="token punctuation">{ }</span><span class="token punctuation">,</span></div></li><li><div class="hoverable collapsed"><span class="property token string">"custom_properties"</span>: <span class="token punctuation">{ }</span><span class="token punctuation">,</span></div></li><li><div class="hoverable collapsed"><span class="property token string">"tags"</span>: <button class="collapser" aria-label="expand"></button><span class="token punctuation">[</span><span class="ellipsis"></span><ul class="array collapsible"><li><div class="hoverable collapsed"><span class="token string">&quot;string&quot;</span></div></li></ul><span class="token punctuation">]</span><span class="token punctuation">,</span></div></li><li><div class="hoverable collapsed"><span class="property token string">"created_by"</span>: <span class="token string">&quot;string&quot;</span></div></li></ul><span class="token punctuation">}</span></div></li></ul><span class="token punctuation">]</span></div></li></ul><span class="token punctuation">}</span></code></div></div></div></div></div></div></div></div></div><div id="schema/ModelElementRef" data-section-id="schema/ModelElementRef" class="sc-dYOLZc kDuBQd"><div class="sc-jZhnRx tMeMv"><div class="sc-gwsNht iRoSfe"><h2 class="sc-kThouk jkwlQD"><a class="sc-kieALA jOWzfS" href="#schema/ModelElementRef" aria-label="schema/ModelElementRef"></a>ModelElementRef</h2></div></div><div class="sc-dYOLZc kDuBQd"><div class="sc-jZhnRx tMeMv"><div class="sc-gwsNht iRoSfe"><table class="sc-eGjrzz cGiuNU"><tbody><tr class=""><td kind="field" title="id" class="sc-kHNKno sc-frmfij cDFxGr kNAbAx"><span class="sc-hLyRwt lhCIDE"></span><span class="property-name">id</span></td><td class="sc-groBii dzPbxx"><div><div><span class="sc-byRegH sc-budtlk eyIabj jGdyyu"></span><span class="sc-byRegH sc-fJvEKN eyIabj fcQkkm">string</span></div> <div><div html="&lt;p&gt;unique identifier of the Component&lt;/p&gt;
" class="sc-faJlkc sc-fdduAw kOqeZB csbUjU"><p>unique identifier of the Component</p>
</div></div></div></td></tr><tr class="last "><td kind="field" title="$ref" class="sc-kHNKno sc-frmfij cDFxGr kNAbAx"><span class="sc-hLyRwt lhCIDE"></span><span class="property-name">$ref</span></td><td class="sc-groBii dzPbxx"><div><div><span class="sc-byRegH sc-budtlk eyIabj jGdyyu"></span><span class="sc-byRegH sc-fJvEKN eyIabj fcQkkm">string</span></div> <div><div html="&lt;p&gt;the&lt;/p&gt;
" class="sc-faJlkc sc-fdduAw kOqeZB csbUjU"><p>the</p>
</div></div></div></td></tr></tbody></table></div><div class="sc-jMpmlX sc-hoLldG kCcXaS jpPJrn"><div class="sc-cnVfeA eJpgoQ"><div class="sc-bgpKpp kuvBhH"><div class="sc-dRHutB iAOzgg"><div class="sc-VILhF itwxyW"><button><div class="sc-fIQtvO indwFD">Copy</div></button></div><div tabindex="0" class="sc-faJlkc kOqeZB sc-eRJQtA eudsMA"><div class="redoc-json"><code><button class="collapser" aria-label="collapse"></button><span class="token punctuation">{</span><span class="ellipsis"></span><ul class="obj collapsible"><li><div class="hoverable "><span class="property token string">"id"</span>: <span class="token string">&quot;cpt_xkHRtSJrPh4JLgeZQLfA7VdLsbi&quot;</span><span class="token punctuation">,</span></div></li><li><div class="hoverable "><span class="property token string">"$ref"</span>: <span class="token string">&quot;string&quot;</span></div></li></ul><span class="token punctuation">}</span></code></div></div></div></div></div></div></div></div></div><div id="schema/Relationship" data-section-id="schema/Relationship" class="sc-dYOLZc kDuBQd"><div class="sc-jZhnRx tMeMv"><div class="sc-gwsNht iRoSfe"><h2 class="sc-kThouk jkwlQD"><a class="sc-kieALA jOWzfS" href="#schema/Relationship" aria-label="schema/Relationship"></a>Relationship</h2></div></div><div class="sc-dYOLZc kDuBQd"><div class="sc-jZhnRx tMeMv"><div class="sc-gwsNht iRoSfe"><table class="sc-eGjrzz cGiuNU"><tbody><tr class=""><td kind="field" title="id" class="sc-kHNKno sc-frmfij cDFxGr kNAbAx"><span class="sc-hLyRwt lhCIDE"></span><span class="property-name">id</span></td><td class="sc-groBii dzPbxx"><div><div><span class="sc-byRegH sc-budtlk eyIabj jGdyyu"></span><span class="sc-byRegH sc-fJvEKN eyIabj fcQkkm">string</span></div> <div><div html="&lt;p&gt;The unique identifier of the Relationship&lt;/p&gt;
" class="sc-faJlkc sc-fdduAw kOqeZB csbUjU"><p>The unique identifier of the Relationship</p>
</div></div></div></td></tr><tr class=""><td kind="field" title="name" class="sc-kHNKno sc-frmfij cDFxGr kNAbAx"><span class="sc-hLyRwt lhCIDE"></span><span class="property-name">name</span></td><td class="sc-groBii dzPbxx"><div><div><span class="sc-byRegH sc-budtlk eyIabj jGdyyu"></span><span class="sc-byRegH sc-fJvEKN eyIabj fcQkkm">string</span></div> <div><div html="&lt;p&gt;The name of the relationship.
It should be unique within the context of the data model.
The name should follow the naming conventions and should not contain any special characters or spaces.&lt;/p&gt;
" class="sc-faJlkc sc-fdduAw kOqeZB csbUjU"><p>The name of the relationship.
It should be unique within the context of the data model.
The name should follow the naming conventions and should not contain any special characters or spaces.</p>
</div></div></div></td></tr><tr class=""><td kind="field" title="description" class="sc-kHNKno sc-frmfij cDFxGr kNAbAx"><span class="sc-hLyRwt lhCIDE"></span><span class="property-name">description</span></td><td class="sc-groBii dzPbxx"><div><div><span class="sc-byRegH sc-budtlk eyIabj jGdyyu"></span><span class="sc-byRegH sc-fJvEKN eyIabj fcQkkm">string</span></div> <div><div html="&lt;p&gt;A description of the relationship.
This should provide additional context or information about the relationship.&lt;/p&gt;
" class="sc-faJlkc sc-fdduAw kOqeZB csbUjU"><p>A description of the relationship.
This should provide additional context or information about the relationship.</p>
</div></div></div></td></tr><tr class=""><td kind="field" title="type" class="sc-kHNKno sc-frmfij cDFxGr kNAbAx"><span class="sc-hLyRwt lhCIDE"></span><span class="property-name">type</span><div class="sc-byRegH sc-isZTFa eyIabj iUhYwd">required</div></td><td class="sc-groBii dzPbxx"><div><div><span class="sc-byRegH sc-budtlk eyIabj jGdyyu"></span><span class="sc-byRegH sc-fJvEKN eyIabj fcQkkm">string</span></div> <div><div html="&lt;p&gt;The type of relationship.
Please the meta_schema_version documentation for the list of valid relationship types.&lt;/p&gt;
" class="sc-faJlkc sc-fdduAw kOqeZB csbUjU"><p>The type of relationship.
Please the meta_schema_version documentation for the list of valid relationship types.</p>
</div></div></div></td></tr><tr class=""><td kind="field" title="index" class="sc-kHNKno sc-frmfij cDFxGr kNAbAx"><span class="sc-hLyRwt lhCIDE"></span><span class="property-name">index</span></td><td class="sc-groBii dzPbxx"><div><div><span class="sc-byRegH sc-budtlk eyIabj jGdyyu"></span><span class="sc-byRegH sc-fJvEKN eyIabj fcQkkm">integer</span></div> <div><div html="&lt;p&gt;Indicates the ordinal position of this relationship.
Default is 0.&lt;/p&gt;
" class="sc-faJlkc sc-fdduAw kOqeZB csbUjU"><p>Indicates the ordinal position of this relationship.
Default is 0.</p>
</div></div></div></td></tr><tr class=""><td kind="field" title="source" class="sc-kHNKno sc-frmfij sc-iyUCga cDFxGr kNAbAx fmWEAp"><span class="sc-hLyRwt lhCIDE"></span><button aria-label="expand source"><span class="property-name">source</span><svg class="sc-dibcMh eVNtOe" version="1.1" viewBox="0 0 24 24" x="0" xmlns="http://www.w3.org/2000/svg" y="0" aria-hidden="true"><polygon points="17.3 8.3 12 13.6 6.7 8.3 5.3 9.7 12 16.4 18.7 9.7 "></polygon></svg></button><div class="sc-byRegH sc-isZTFa eyIabj iUhYwd">required</div></td><td class="sc-groBii dzPbxx"><div><div><span class="sc-byRegH sc-budtlk eyIabj jGdyyu"></span><span class="sc-byRegH sc-fJvEKN eyIabj fcQkkm">object</span><span class="sc-byRegH sc-hsXxFb eyIabj iZVMho"> (<!-- -->ModelElementRef<!-- -->) </span></div> <div><div html="&lt;p&gt;An object representing a component that requires metadata scanning.&lt;/p&gt;
" class="sc-faJlkc sc-fdduAw kOqeZB csbUjU"><p>An object representing a component that requires metadata scanning.</p>
</div></div></div></td></tr><tr class=""><td kind="field" title="target" class="sc-kHNKno sc-frmfij sc-iyUCga cDFxGr kNAbAx fmWEAp"><span class="sc-hLyRwt lhCIDE"></span><button aria-label="expand target"><span class="property-name">target</span><svg class="sc-dibcMh eVNtOe" version="1.1" viewBox="0 0 24 24" x="0" xmlns="http://www.w3.org/2000/svg" y="0" aria-hidden="true"><polygon points="17.3 8.3 12 13.6 6.7 8.3 5.3 9.7 12 16.4 18.7 9.7 "></polygon></svg></button><div class="sc-byRegH sc-isZTFa eyIabj iUhYwd">required</div></td><td class="sc-groBii dzPbxx"><div><div><span class="sc-byRegH sc-budtlk eyIabj jGdyyu"></span><span class="sc-byRegH sc-fJvEKN eyIabj fcQkkm">object</span><span class="sc-byRegH sc-hsXxFb eyIabj iZVMho"> (<!-- -->ModelElementRef<!-- -->) </span></div> <div><div html="&lt;p&gt;An object representing a component that requires metadata scanning.&lt;/p&gt;
" class="sc-faJlkc sc-fdduAw kOqeZB csbUjU"><p>An object representing a component that requires metadata scanning.</p>
</div></div></div></td></tr><tr class=""><td kind="field" title="properties" class="sc-kHNKno sc-frmfij cDFxGr kNAbAx"><span class="sc-hLyRwt lhCIDE"></span><span class="property-name">properties</span></td><td class="sc-groBii dzPbxx"><div><div><span class="sc-byRegH sc-budtlk eyIabj jGdyyu"></span><span class="sc-byRegH sc-fJvEKN eyIabj fcQkkm">object</span></div> <div><div html="&lt;p&gt;Type specific properties.
A map of key value pairs that captures the properties specific to the relationship.&lt;/p&gt;
" class="sc-faJlkc sc-fdduAw kOqeZB csbUjU"><p>Type specific properties.
A map of key value pairs that captures the properties specific to the relationship.</p>
</div></div></div></td></tr><tr class=""><td kind="field" title="tags" class="sc-kHNKno sc-frmfij cDFxGr kNAbAx"><span class="sc-hLyRwt lhCIDE"></span><span class="property-name">tags</span></td><td class="sc-groBii dzPbxx"><div><div><span class="sc-byRegH sc-budtlk eyIabj jGdyyu">Array of </span><span class="sc-byRegH sc-fJvEKN eyIabj fcQkkm">strings</span></div> <div><div html="&lt;p&gt;A list of tags associated with the relationship.
Tags can be used to categorize or label the relationship for easier identification and management.
Tags can be used for filtering and searching relationships within the data model.&lt;/p&gt;
" class="sc-faJlkc sc-fdduAw kOqeZB csbUjU"><p>A list of tags associated with the relationship.
Tags can be used to categorize or label the relationship for easier identification and management.
Tags can be used for filtering and searching relationships within the data model.</p>
</div></div></div></td></tr><tr class="last "><td kind="field" title="created_by" class="sc-kHNKno sc-frmfij cDFxGr kNAbAx"><span class="sc-hLyRwt lhCIDE"></span><span class="property-name">created_by</span></td><td class="sc-groBii dzPbxx"><div><div><span class="sc-byRegH sc-budtlk eyIabj jGdyyu"></span><span class="sc-byRegH sc-fJvEKN eyIabj fcQkkm">string</span></div> <div><div html="&lt;p&gt;The user who created the relationship.
This is usually a system generated value and is used for auditing purposes.&lt;/p&gt;
" class="sc-faJlkc sc-fdduAw kOqeZB csbUjU"><p>The user who created the relationship.
This is usually a system generated value and is used for auditing purposes.</p>
</div></div></div></td></tr></tbody></table></div><div class="sc-jMpmlX sc-hoLldG kCcXaS jpPJrn"><div class="sc-cnVfeA eJpgoQ"><div class="sc-bgpKpp kuvBhH"><div class="sc-dRHutB iAOzgg"><div class="sc-VILhF itwxyW"><button><div class="sc-fIQtvO indwFD">Copy</div></button><button> Expand all </button><button> Collapse all </button></div><div tabindex="0" class="sc-faJlkc kOqeZB sc-eRJQtA eudsMA"><div class="redoc-json"><code><button class="collapser" aria-label="collapse"></button><span class="token punctuation">{</span><span class="ellipsis"></span><ul class="obj collapsible"><li><div class="hoverable "><span class="property token string">"id"</span>: <span class="token string">&quot;string&quot;</span><span class="token punctuation">,</span></div></li><li><div class="hoverable "><span class="property token string">"name"</span>: <span class="token string">&quot;string&quot;</span><span class="token punctuation">,</span></div></li><li><div class="hoverable "><span class="property token string">"description"</span>: <span class="token string">&quot;string&quot;</span><span class="token punctuation">,</span></div></li><li><div class="hoverable "><span class="property token string">"type"</span>: <span class="token string">&quot;string&quot;</span><span class="token punctuation">,</span></div></li><li><div class="hoverable "><span class="property token string">"index"</span>: <span class="token number">0</span><span class="token punctuation">,</span></div></li><li><div class="hoverable "><span class="property token string">"source"</span>: <button class="collapser" aria-label="collapse"></button><span class="token punctuation">{</span><span class="ellipsis"></span><ul class="obj collapsible"><li><div class="hoverable collapsed"><span class="property token string">"id"</span>: <span class="token string">&quot;cpt_xkHRtSJrPh4JLgeZQLfA7VdLsbi&quot;</span><span class="token punctuation">,</span></div></li><li><div class="hoverable collapsed"><span class="property token string">"$ref"</span>: <span class="token string">&quot;string&quot;</span></div></li></ul><span class="token punctuation">}</span><span class="token punctuation">,</span></div></li><li><div class="hoverable "><span class="property token string">"target"</span>: <button class="collapser" aria-label="collapse"></button><span class="token punctuation">{</span><span class="ellipsis"></span><ul class="obj collapsible"><li><div class="hoverable collapsed"><span class="property token string">"id"</span>: <span class="token string">&quot;cpt_xkHRtSJrPh4JLgeZQLfA7VdLsbi&quot;</span><span class="token punctuation">,</span></div></li><li><div class="hoverable collapsed"><span class="property token string">"$ref"</span>: <span class="token string">&quot;string&quot;</span></div></li></ul><span class="token punctuation">}</span><span class="token punctuation">,</span></div></li><li><div class="hoverable "><span class="property token string">"properties"</span>: <span class="token punctuation">{ }</span><span class="token punctuation">,</span></div></li><li><div class="hoverable "><span class="property token string">"tags"</span>: <button class="collapser" aria-label="collapse"></button><span class="token punctuation">[</span><span class="ellipsis"></span><ul class="array collapsible"><li><div class="hoverable collapsed"><span class="token string">&quot;string&quot;</span></div></li></ul><span class="token punctuation">]</span><span class="token punctuation">,</span></div></li><li><div class="hoverable "><span class="property token string">"created_by"</span>: <span class="token string">&quot;string&quot;</span></div></li></ul><span class="token punctuation">}</span></code></div></div></div></div></div></div></div></div></div><div id="schema/DataModelRequest" data-section-id="schema/DataModelRequest" class="sc-dYOLZc kDuBQd"><div class="sc-jZhnRx tMeMv"><div class="sc-gwsNht iRoSfe"><h2 class="sc-kThouk jkwlQD"><a class="sc-kieALA jOWzfS" href="#schema/DataModelRequest" aria-label="schema/DataModelRequest"></a>DataModelRequest</h2></div></div><div class="sc-dYOLZc kDuBQd"><div class="sc-jZhnRx tMeMv"><div class="sc-gwsNht iRoSfe"><table class="sc-eGjrzz cGiuNU"><tbody><tr class=""><td kind="field" title="meta_schema_version" class="sc-kHNKno sc-frmfij cDFxGr kNAbAx"><span class="sc-hLyRwt lhCIDE"></span><span class="property-name">meta_schema_version</span><div class="sc-byRegH sc-isZTFa eyIabj iUhYwd">required</div></td><td class="sc-groBii dzPbxx"><div><div><span class="sc-byRegH sc-budtlk eyIabj jGdyyu"></span><span class="sc-byRegH sc-fJvEKN eyIabj fcQkkm">string</span></div> <div><div html="&lt;p&gt;The meta-meta schema version.
It controls the rules and structure of the data model, including the allowed fields, their types, and other constraints
The value of this field ensures that the system understands how to interpret the data and which validation rules to apply.
For example, the version &lt;code&gt;relational-20250501&lt;/code&gt; informs the system that the data model defined in the current API is representing the database schama.
Hence the system can validate that the allowed entity types are only TABLE and VIEW.&lt;/p&gt;
" class="sc-faJlkc sc-fdduAw kOqeZB csbUjU"><p>The meta-meta schema version.
It controls the rules and structure of the data model, including the allowed fields, their types, and other constraints
The value of this field ensures that the system understands how to interpret the data and which validation rules to apply.
For example, the version <code>relational-20250501</code> informs the system that the data model defined in the current API is representing the database schama.
Hence the system can validate that the allowed entity types are only TABLE and VIEW.</p>
</div></div></div></td></tr><tr class=""><td kind="field" title="entities" class="sc-kHNKno sc-frmfij sc-iyUCga cDFxGr kNAbAx fmWEAp"><span class="sc-hLyRwt lhCIDE"></span><button aria-label="expand entities"><span class="property-name">entities</span><svg class="sc-dibcMh eVNtOe" version="1.1" viewBox="0 0 24 24" x="0" xmlns="http://www.w3.org/2000/svg" y="0" aria-hidden="true"><polygon points="17.3 8.3 12 13.6 6.7 8.3 5.3 9.7 12 16.4 18.7 9.7 "></polygon></svg></button></td><td class="sc-groBii dzPbxx"><div><div><span class="sc-byRegH sc-budtlk eyIabj jGdyyu">Array of </span><span class="sc-byRegH sc-fJvEKN eyIabj fcQkkm">objects</span><span class="sc-byRegH sc-hsXxFb eyIabj iZVMho"> (<!-- -->Entity<!-- -->) </span></div> <div><div html="&lt;p&gt;The list of entities in the data model.
Each entity represents a distinct object or concept within the data model.
The entity can be a table, view, or any other object that is relevant to the data model.
Each entity has a unique name and can have multiple entity elements (fields) associated with it.&lt;/p&gt;
" class="sc-faJlkc sc-fdduAw kOqeZB csbUjU"><p>The list of entities in the data model.
Each entity represents a distinct object or concept within the data model.
The entity can be a table, view, or any other object that is relevant to the data model.
Each entity has a unique name and can have multiple entity elements (fields) associated with it.</p>
</div></div></div></td></tr><tr class="last "><td kind="field" title="relationships" class="sc-kHNKno sc-frmfij sc-iyUCga cDFxGr kNAbAx fmWEAp"><span class="sc-hLyRwt lhCIDE"></span><button aria-label="expand relationships"><span class="property-name">relationships</span><svg class="sc-dibcMh eVNtOe" version="1.1" viewBox="0 0 24 24" x="0" xmlns="http://www.w3.org/2000/svg" y="0" aria-hidden="true"><polygon points="17.3 8.3 12 13.6 6.7 8.3 5.3 9.7 12 16.4 18.7 9.7 "></polygon></svg></button></td><td class="sc-groBii dzPbxx"><div><div><span class="sc-byRegH sc-budtlk eyIabj jGdyyu">Array of </span><span class="sc-byRegH sc-fJvEKN eyIabj fcQkkm">objects</span><span class="sc-byRegH sc-hsXxFb eyIabj iZVMho"> (<!-- -->Relationship<!-- -->) </span></div> <div><div html="&lt;p&gt;The list of relationships in the data model.
Each relationship represents a connection between entities or entity elements.&lt;/p&gt;
" class="sc-faJlkc sc-fdduAw kOqeZB csbUjU"><p>The list of relationships in the data model.
Each relationship represents a connection between entities or entity elements.</p>
</div></div></div></td></tr></tbody></table></div><div class="sc-jMpmlX sc-hoLldG kCcXaS jpPJrn"><div class="sc-cnVfeA eJpgoQ"><div class="sc-bgpKpp kuvBhH"><div class="sc-dRHutB iAOzgg"><div class="sc-VILhF itwxyW"><button><div class="sc-fIQtvO indwFD">Copy</div></button><button> Expand all </button><button> Collapse all </button></div><div tabindex="0" class="sc-faJlkc kOqeZB sc-eRJQtA eudsMA"><div class="redoc-json"><code><button class="collapser" aria-label="collapse"></button><span class="token punctuation">{</span><span class="ellipsis"></span><ul class="obj collapsible"><li><div class="hoverable "><span class="property token string">"meta_schema_version"</span>: <span class="token string">&quot;string&quot;</span><span class="token punctuation">,</span></div></li><li><div class="hoverable "><span class="property token string">"entities"</span>: <button class="collapser" aria-label="collapse"></button><span class="token punctuation">[</span><span class="ellipsis"></span><ul class="array collapsible"><li><div class="hoverable collapsed"><button class="collapser" aria-label="expand"></button><span class="token punctuation">{</span><span class="ellipsis"></span><ul class="obj collapsible"><li><div class="hoverable collapsed"><span class="property token string">"id"</span>: <span class="token string">&quot;string&quot;</span><span class="token punctuation">,</span></div></li><li><div class="hoverable collapsed"><span class="property token string">"model_name"</span>: <span class="token string">&quot;string&quot;</span><span class="token punctuation">,</span></div></li><li><div class="hoverable collapsed"><span class="property token string">"name"</span>: <span class="token string">&quot;string&quot;</span><span class="token punctuation">,</span></div></li><li><div class="hoverable collapsed"><span class="property token string">"description"</span>: <span class="token string">&quot;string&quot;</span><span class="token punctuation">,</span></div></li><li><div class="hoverable collapsed"><span class="property token string">"type"</span>: <span class="token string">&quot;string&quot;</span><span class="token punctuation">,</span></div></li><li><div class="hoverable collapsed"><span class="property token string">"consumption_type"</span>: <span class="token string">&quot;string&quot;</span><span class="token punctuation">,</span></div></li><li><div class="hoverable collapsed"><span class="property token string">"index"</span>: <span class="token number">0</span><span class="token punctuation">,</span></div></li><li><div class="hoverable collapsed"><span class="property token string">"properties"</span>: <span class="token punctuation">{ }</span><span class="token punctuation">,</span></div></li><li><div class="hoverable collapsed"><span class="property token string">"custom_properties"</span>: <span class="token punctuation">{ }</span><span class="token punctuation">,</span></div></li><li><div class="hoverable collapsed"><span class="property token string">"tags"</span>: <button class="collapser" aria-label="expand"></button><span class="token punctuation">[</span><span class="ellipsis"></span><ul class="array collapsible"><li><div class="hoverable collapsed"><span class="token string">&quot;string&quot;</span></div></li></ul><span class="token punctuation">]</span><span class="token punctuation">,</span></div></li><li><div class="hoverable collapsed"><span class="property token string">"created_by"</span>: <span class="token string">&quot;string&quot;</span><span class="token punctuation">,</span></div></li><li><div class="hoverable collapsed"><span class="property token string">"entity_elements"</span>: <button class="collapser" aria-label="expand"></button><span class="token punctuation">[</span><span class="ellipsis"></span><ul class="array collapsible"><li><div class="hoverable collapsed"><button class="collapser" aria-label="expand"></button><span class="token punctuation">{</span><span class="ellipsis"></span><ul class="obj collapsible"><li><div class="hoverable collapsed"><span class="property token string">"id"</span>: <span class="token string">&quot;string&quot;</span><span class="token punctuation">,</span></div></li><li><div class="hoverable collapsed"><span class="property token string">"model_name"</span>: <span class="token string">&quot;string&quot;</span><span class="token punctuation">,</span></div></li><li><div class="hoverable collapsed"><span class="property token string">"name"</span>: <span class="token string">&quot;string&quot;</span><span class="token punctuation">,</span></div></li><li><div class="hoverable collapsed"><span class="property token string">"description"</span>: <span class="token string">&quot;string&quot;</span><span class="token punctuation">,</span></div></li><li><div class="hoverable collapsed"><span class="property token string">"type"</span>: <span class="token string">&quot;string&quot;</span><span class="token punctuation">,</span></div></li><li><div class="hoverable collapsed"><span class="property token string">"consumption_type"</span>: <span class="token string">&quot;string&quot;</span><span class="token punctuation">,</span></div></li><li><div class="hoverable collapsed"><span class="property token string">"index"</span>: <span class="token number">0</span><span class="token punctuation">,</span></div></li><li><div class="hoverable collapsed"><span class="property token string">"data_type"</span>: <span class="token string">&quot;string&quot;</span><span class="token punctuation">,</span></div></li><li><div class="hoverable collapsed"><span class="property token string">"default_value"</span>: <span class="token string">&quot;string&quot;</span><span class="token punctuation">,</span></div></li><li><div class="hoverable collapsed"><span class="property token string">"enum"</span>: <button class="collapser" aria-label="expand"></button><span class="token punctuation">[</span><span class="ellipsis"></span><ul class="array collapsible"><li><div class="hoverable collapsed"><span class="token string">&quot;string&quot;</span></div></li></ul><span class="token punctuation">]</span><span class="token punctuation">,</span></div></li><li><div class="hoverable collapsed"><span class="property token string">"is_required"</span>: <span class="token boolean">true</span><span class="token punctuation">,</span></div></li><li><div class="hoverable collapsed"><span class="property token string">"is_array"</span>: <span class="token boolean">true</span><span class="token punctuation">,</span></div></li><li><div class="hoverable collapsed"><span class="property token string">"max_length"</span>: <span class="token number">0</span><span class="token punctuation">,</span></div></li><li><div class="hoverable collapsed"><span class="property token string">"precision"</span>: <span class="token number">0</span><span class="token punctuation">,</span></div></li><li><div class="hoverable collapsed"><span class="property token string">"scale"</span>: <span class="token number">0</span><span class="token punctuation">,</span></div></li><li><div class="hoverable collapsed"><span class="property token string">"properties"</span>: <span class="token punctuation">{ }</span><span class="token punctuation">,</span></div></li><li><div class="hoverable collapsed"><span class="property token string">"custom_properties"</span>: <span class="token punctuation">{ }</span><span class="token punctuation">,</span></div></li><li><div class="hoverable collapsed"><span class="property token string">"tags"</span>: <button class="collapser" aria-label="expand"></button><span class="token punctuation">[</span><span class="ellipsis"></span><ul class="array collapsible"><li><div class="hoverable collapsed"><span class="token string">&quot;string&quot;</span></div></li></ul><span class="token punctuation">]</span><span class="token punctuation">,</span></div></li><li><div class="hoverable collapsed"><span class="property token string">"created_by"</span>: <span class="token string">&quot;string&quot;</span></div></li></ul><span class="token punctuation">}</span></div></li></ul><span class="token punctuation">]</span></div></li></ul><span class="token punctuation">}</span></div></li></ul><span class="token punctuation">]</span><span class="token punctuation">,</span></div></li><li><div class="hoverable "><span class="property token string">"relationships"</span>: <button class="collapser" aria-label="collapse"></button><span class="token punctuation">[</span><span class="ellipsis"></span><ul class="array collapsible"><li><div class="hoverable collapsed"><button class="collapser" aria-label="expand"></button><span class="token punctuation">{</span><span class="ellipsis"></span><ul class="obj collapsible"><li><div class="hoverable collapsed"><span class="property token string">"id"</span>: <span class="token string">&quot;string&quot;</span><span class="token punctuation">,</span></div></li><li><div class="hoverable collapsed"><span class="property token string">"name"</span>: <span class="token string">&quot;string&quot;</span><span class="token punctuation">,</span></div></li><li><div class="hoverable collapsed"><span class="property token string">"description"</span>: <span class="token string">&quot;string&quot;</span><span class="token punctuation">,</span></div></li><li><div class="hoverable collapsed"><span class="property token string">"type"</span>: <span class="token string">&quot;string&quot;</span><span class="token punctuation">,</span></div></li><li><div class="hoverable collapsed"><span class="property token string">"index"</span>: <span class="token number">0</span><span class="token punctuation">,</span></div></li><li><div class="hoverable collapsed"><span class="property token string">"source"</span>: <button class="collapser" aria-label="expand"></button><span class="token punctuation">{</span><span class="ellipsis"></span><ul class="obj collapsible"><li><div class="hoverable collapsed"><span class="property token string">"id"</span>: <span class="token string">&quot;cpt_xkHRtSJrPh4JLgeZQLfA7VdLsbi&quot;</span><span class="token punctuation">,</span></div></li><li><div class="hoverable collapsed"><span class="property token string">"$ref"</span>: <span class="token string">&quot;string&quot;</span></div></li></ul><span class="token punctuation">}</span><span class="token punctuation">,</span></div></li><li><div class="hoverable collapsed"><span class="property token string">"target"</span>: <button class="collapser" aria-label="expand"></button><span class="token punctuation">{</span><span class="ellipsis"></span><ul class="obj collapsible"><li><div class="hoverable collapsed"><span class="property token string">"id"</span>: <span class="token string">&quot;cpt_xkHRtSJrPh4JLgeZQLfA7VdLsbi&quot;</span><span class="token punctuation">,</span></div></li><li><div class="hoverable collapsed"><span class="property token string">"$ref"</span>: <span class="token string">&quot;string&quot;</span></div></li></ul><span class="token punctuation">}</span><span class="token punctuation">,</span></div></li><li><div class="hoverable collapsed"><span class="property token string">"properties"</span>: <span class="token punctuation">{ }</span><span class="token punctuation">,</span></div></li><li><div class="hoverable collapsed"><span class="property token string">"tags"</span>: <button class="collapser" aria-label="expand"></button><span class="token punctuation">[</span><span class="ellipsis"></span><ul class="array collapsible"><li><div class="hoverable collapsed"><span class="token string">&quot;string&quot;</span></div></li></ul><span class="token punctuation">]</span><span class="token punctuation">,</span></div></li><li><div class="hoverable collapsed"><span class="property token string">"created_by"</span>: <span class="token string">&quot;string&quot;</span></div></li></ul><span class="token punctuation">}</span></div></li></ul><span class="token punctuation">]</span></div></li></ul><span class="token punctuation">}</span></code></div></div></div></div></div></div></div></div></div><div id="schema/DataModelResponse" data-section-id="schema/DataModelResponse" class="sc-dYOLZc kDuBQd"><div class="sc-jZhnRx tMeMv"><div class="sc-gwsNht iRoSfe"><h2 class="sc-kThouk jkwlQD"><a class="sc-kieALA jOWzfS" href="#schema/DataModelResponse" aria-label="schema/DataModelResponse"></a>DataModelResponse</h2></div></div><div class="sc-dYOLZc kDuBQd"><div class="sc-jZhnRx tMeMv"><div class="sc-gwsNht iRoSfe"><table class="sc-eGjrzz cGiuNU"><tbody><tr class="last "><td kind="field" title="new_version" class="sc-kHNKno sc-frmfij cDFxGr kNAbAx"><span class="sc-hLyRwt lhCIDE"></span><span class="property-name">new_version</span><div class="sc-byRegH sc-isZTFa eyIabj iUhYwd">required</div></td><td class="sc-groBii dzPbxx"><div><div><span class="sc-byRegH sc-budtlk eyIabj jGdyyu"></span><span class="sc-byRegH sc-fJvEKN eyIabj fcQkkm">string</span></div> <div><div html="&lt;p&gt;The new version of the data model.&lt;/p&gt;
" class="sc-faJlkc sc-fdduAw kOqeZB csbUjU"><p>The new version of the data model.</p>
</div></div></div></td></tr></tbody></table></div><div class="sc-jMpmlX sc-hoLldG kCcXaS jpPJrn"><div class="sc-cnVfeA eJpgoQ"><div class="sc-bgpKpp kuvBhH"><div class="sc-dRHutB iAOzgg"><div class="sc-VILhF itwxyW"><button><div class="sc-fIQtvO indwFD">Copy</div></button></div><div tabindex="0" class="sc-faJlkc kOqeZB sc-eRJQtA eudsMA"><div class="redoc-json"><code><button class="collapser" aria-label="collapse"></button><span class="token punctuation">{</span><span class="ellipsis"></span><ul class="obj collapsible"><li><div class="hoverable "><span class="property token string">"new_version"</span>: <span class="token string">&quot;string&quot;</span></div></li></ul><span class="token punctuation">}</span></code></div></div></div></div></div></div></div></div></div><div id="schema/ErrorDetail" data-section-id="schema/ErrorDetail" class="sc-dYOLZc kDuBQd"><div class="sc-jZhnRx tMeMv"><div class="sc-gwsNht iRoSfe"><h2 class="sc-kThouk jkwlQD"><a class="sc-kieALA jOWzfS" href="#schema/ErrorDetail" aria-label="schema/ErrorDetail"></a>ErrorDetail</h2></div></div><div class="sc-dYOLZc kDuBQd"><div class="sc-jZhnRx tMeMv"><div class="sc-gwsNht iRoSfe"><table class="sc-eGjrzz cGiuNU"><tbody><tr class=""><td kind="field" title="message" class="sc-kHNKno sc-frmfij cDFxGr kNAbAx"><span class="sc-hLyRwt lhCIDE"></span><span class="property-name">message</span><div class="sc-byRegH sc-isZTFa eyIabj iUhYwd">required</div></td><td class="sc-groBii dzPbxx"><div><div><span class="sc-byRegH sc-budtlk eyIabj jGdyyu"></span><span class="sc-byRegH sc-fJvEKN eyIabj fcQkkm">string</span></div> <div><div html="&lt;p&gt;Specific error message.&lt;/p&gt;
" class="sc-faJlkc sc-fdduAw kOqeZB csbUjU"><p>Specific error message.</p>
</div></div></div></td></tr><tr class="last "><td kind="field" title="field" class="sc-kHNKno sc-frmfij cDFxGr kNAbAx"><span class="sc-hLyRwt lhCIDE"></span><span class="property-name">field</span></td><td class="sc-groBii dzPbxx"><div><div><span class="sc-byRegH sc-budtlk eyIabj jGdyyu"></span><span class="sc-byRegH sc-fJvEKN eyIabj fcQkkm">string</span></div> <div><div html="&lt;p&gt;The field that caused the error (if applicable).&lt;/p&gt;
" class="sc-faJlkc sc-fdduAw kOqeZB csbUjU"><p>The field that caused the error (if applicable).</p>
</div></div></div></td></tr></tbody></table></div><div class="sc-jMpmlX sc-hoLldG kCcXaS jpPJrn"><div class="sc-cnVfeA eJpgoQ"><div class="sc-bgpKpp kuvBhH"><div class="sc-dRHutB iAOzgg"><div class="sc-VILhF itwxyW"><button><div class="sc-fIQtvO indwFD">Copy</div></button></div><div tabindex="0" class="sc-faJlkc kOqeZB sc-eRJQtA eudsMA"><div class="redoc-json"><code><button class="collapser" aria-label="collapse"></button><span class="token punctuation">{</span><span class="ellipsis"></span><ul class="obj collapsible"><li><div class="hoverable "><span class="property token string">"field"</span>: <span class="token string">&quot;name&quot;</span><span class="token punctuation">,</span></div></li><li><div class="hoverable "><span class="property token string">"message"</span>: <span class="token string">&quot;name is required.&quot;</span></div></li></ul><span class="token punctuation">}</span></code></div></div></div></div></div></div></div></div></div><div id="schema/ApiError" data-section-id="schema/ApiError" class="sc-dYOLZc kDuBQd"><div class="sc-jZhnRx tMeMv"><div class="sc-gwsNht iRoSfe"><h2 class="sc-kThouk jkwlQD"><a class="sc-kieALA jOWzfS" href="#schema/ApiError" aria-label="schema/ApiError"></a>ApiError</h2></div></div><div class="sc-dYOLZc kDuBQd"><div class="sc-jZhnRx tMeMv"><div class="sc-gwsNht iRoSfe"><table class="sc-eGjrzz cGiuNU"><tbody><tr class=""><td kind="field" title="trace_id" class="sc-kHNKno sc-frmfij cDFxGr kNAbAx"><span class="sc-hLyRwt lhCIDE"></span><span class="property-name">trace_id</span><div class="sc-byRegH sc-isZTFa eyIabj iUhYwd">required</div></td><td class="sc-groBii dzPbxx"><div><div><span class="sc-byRegH sc-budtlk eyIabj jGdyyu"></span><span class="sc-byRegH sc-fJvEKN eyIabj fcQkkm">string</span></div> <div><div html="&lt;p&gt;The unique identifier associated with the API request to assist with troubleshooting.&lt;/p&gt;
" class="sc-faJlkc sc-fdduAw kOqeZB csbUjU"><p>The unique identifier associated with the API request to assist with troubleshooting.</p>
</div></div></div></td></tr><tr class=""><td kind="field" title="type" class="sc-kHNKno sc-frmfij cDFxGr kNAbAx"><span class="sc-hLyRwt lhCIDE"></span><span class="property-name">type</span><div class="sc-byRegH sc-isZTFa eyIabj iUhYwd">required</div></td><td class="sc-groBii dzPbxx"><div><div><span class="sc-byRegH sc-budtlk eyIabj jGdyyu"></span><span class="sc-byRegH sc-fJvEKN eyIabj fcQkkm">string</span></div> <div><div html="&lt;p&gt;A URI reference [URI] that identifies the error type.&lt;/p&gt;
" class="sc-faJlkc sc-fdduAw kOqeZB csbUjU"><p>A URI reference [URI] that identifies the error type.</p>
</div></div></div></td></tr><tr class=""><td kind="field" title="status" class="sc-kHNKno sc-frmfij cDFxGr kNAbAx"><span class="sc-hLyRwt lhCIDE"></span><span class="property-name">status</span></td><td class="sc-groBii dzPbxx"><div><div><span class="sc-byRegH sc-budtlk eyIabj jGdyyu"></span><span class="sc-byRegH sc-fJvEKN eyIabj fcQkkm">int</span></div> <div><div html="&lt;p&gt;The HTTP status code associated with the error.&lt;/p&gt;
" class="sc-faJlkc sc-fdduAw kOqeZB csbUjU"><p>The HTTP status code associated with the error.</p>
</div></div></div></td></tr><tr class=""><td kind="field" title="title" class="sc-kHNKno sc-frmfij cDFxGr kNAbAx"><span class="sc-hLyRwt lhCIDE"></span><span class="property-name">title</span><div class="sc-byRegH sc-isZTFa eyIabj iUhYwd">required</div></td><td class="sc-groBii dzPbxx"><div><div><span class="sc-byRegH sc-budtlk eyIabj jGdyyu"></span><span class="sc-byRegH sc-fJvEKN eyIabj fcQkkm">string</span></div> <div><div html="&lt;p&gt;A short, human-readable summary of the error type.&lt;/p&gt;
" class="sc-faJlkc sc-fdduAw kOqeZB csbUjU"><p>A short, human-readable summary of the error type.</p>
</div></div></div></td></tr><tr class=""><td kind="field" title="detail" class="sc-kHNKno sc-frmfij cDFxGr kNAbAx"><span class="sc-hLyRwt lhCIDE"></span><span class="property-name">detail</span></td><td class="sc-groBii dzPbxx"><div><div><span class="sc-byRegH sc-budtlk eyIabj jGdyyu"></span><span class="sc-byRegH sc-fJvEKN eyIabj fcQkkm">string</span></div> <div><div html="&lt;p&gt;A human-readable explanation specific to this occurrence of the error.&lt;/p&gt;
" class="sc-faJlkc sc-fdduAw kOqeZB csbUjU"><p>A human-readable explanation specific to this occurrence of the error.</p>
</div></div></div></td></tr><tr class=""><td kind="field" title="message" class="sc-kHNKno sc-frmfij cDFxGr kNAbAx"><span class="sc-hLyRwt lhCIDE"></span><span class="property-name">message</span></td><td class="sc-groBii dzPbxx"><div><div><span class="sc-byRegH sc-budtlk eyIabj jGdyyu"></span><span class="sc-byRegH sc-fJvEKN eyIabj fcQkkm">string</span></div> <div><div html="&lt;p&gt;Duplicate of detail field for backward compatibility.&lt;/p&gt;
" class="sc-faJlkc sc-fdduAw kOqeZB csbUjU"><p>Duplicate of detail field for backward compatibility.</p>
</div></div></div></td></tr><tr class="last "><td kind="field" title="errors" class="sc-kHNKno sc-frmfij sc-iyUCga cDFxGr kNAbAx fmWEAp"><span class="sc-hLyRwt lhCIDE"></span><button aria-label="expand errors"><span class="property-name">errors</span><svg class="sc-dibcMh eVNtOe" version="1.1" viewBox="0 0 24 24" x="0" xmlns="http://www.w3.org/2000/svg" y="0" aria-hidden="true"><polygon points="17.3 8.3 12 13.6 6.7 8.3 5.3 9.7 12 16.4 18.7 9.7 "></polygon></svg></button></td><td class="sc-groBii dzPbxx"><div><div><span class="sc-byRegH sc-budtlk eyIabj jGdyyu">Array of </span><span class="sc-byRegH sc-fJvEKN eyIabj fcQkkm">objects</span><span class="sc-byRegH sc-hsXxFb eyIabj iZVMho"> (<!-- -->ErrorDetail<!-- -->) </span></div> <div><div html="&lt;p&gt;An optional list of individual errors when multiple errors occur.&lt;/p&gt;
" class="sc-faJlkc sc-fdduAw kOqeZB csbUjU"><p>An optional list of individual errors when multiple errors occur.</p>
</div></div></div></td></tr></tbody></table></div><div class="sc-jMpmlX sc-hoLldG kCcXaS jpPJrn"><div class="sc-cnVfeA eJpgoQ"><div class="sc-bgpKpp kuvBhH"><div class="sc-dRHutB iAOzgg"><div class="sc-VILhF itwxyW"><button><div class="sc-fIQtvO indwFD">Copy</div></button><button> Expand all </button><button> Collapse all </button></div><div tabindex="0" class="sc-faJlkc kOqeZB sc-eRJQtA eudsMA"><div class="redoc-json"><code><button class="collapser" aria-label="collapse"></button><span class="token punctuation">{</span><span class="ellipsis"></span><ul class="obj collapsible"><li><div class="hoverable "><span class="property token string">"trace_id"</span>: <span class="token string">&quot;123e4567-e89b-12d3-a456-426614174000&quot;</span><span class="token punctuation">,</span></div></li><li><div class="hoverable "><span class="property token string">"type"</span>: <span class="token string">&quot;</span><a href="https://catalog.syniti.com/errors/validation-error">https://catalog.syniti.com/errors/validation-error</a><span class="token string">&quot;</span><span class="token punctuation">,</span></div></li><li><div class="hoverable "><span class="property token string">"status"</span>: <span class="token number">400</span><span class="token punctuation">,</span></div></li><li><div class="hoverable "><span class="property token string">"title"</span>: <span class="token string">&quot;Validation Error&quot;</span><span class="token punctuation">,</span></div></li><li><div class="hoverable "><span class="property token string">"detail"</span>: <span class="token string">&quot;One or more validation errors occurred. Errors: name: [name is required], age: [must be a positive integer]&quot;</span><span class="token punctuation">,</span></div></li><li><div class="hoverable "><span class="property token string">"errors"</span>: <button class="collapser" aria-label="collapse"></button><span class="token punctuation">[</span><span class="ellipsis"></span><ul class="array collapsible"><li><div class="hoverable collapsed"><button class="collapser" aria-label="expand"></button><span class="token punctuation">{</span><span class="ellipsis"></span><ul class="obj collapsible"><li><div class="hoverable collapsed"><span class="property token string">"field"</span>: <span class="token string">&quot;name&quot;</span><span class="token punctuation">,</span></div></li><li><div class="hoverable collapsed"><span class="property token string">"message"</span>: <span class="token string">&quot;name is required.&quot;</span></div></li></ul><span class="token punctuation">}</span>,</div></li><li><div class="hoverable collapsed"><button class="collapser" aria-label="expand"></button><span class="token punctuation">{</span><span class="ellipsis"></span><ul class="obj collapsible"><li><div class="hoverable collapsed"><span class="property token string">"field"</span>: <span class="token string">&quot;age&quot;</span><span class="token punctuation">,</span></div></li><li><div class="hoverable collapsed"><span class="property token string">"message"</span>: <span class="token string">&quot;must be a positive integer.&quot;</span></div></li></ul><span class="token punctuation">}</span></div></li></ul><span class="token punctuation">]</span></div></li></ul><span class="token punctuation">}</span></code></div></div></div></div></div></div></div></div></div><div id="schema/DeleteDataModelResponse" data-section-id="schema/DeleteDataModelResponse" class="sc-dYOLZc kDuBQd"><div class="sc-jZhnRx tMeMv"><div class="sc-gwsNht iRoSfe"><h2 class="sc-kThouk jkwlQD"><a class="sc-kieALA jOWzfS" href="#schema/DeleteDataModelResponse" aria-label="schema/DeleteDataModelResponse"></a>DeleteDataModelResponse</h2></div></div><div class="sc-dYOLZc kDuBQd"><div class="sc-jZhnRx tMeMv"><div class="sc-gwsNht iRoSfe"><table class="sc-eGjrzz cGiuNU"><tbody><tr class="last "><td kind="field" title="results" class="sc-kHNKno sc-frmfij sc-iyUCga cDFxGr kNAbAx fmWEAp"><span class="sc-hLyRwt lhCIDE"></span><button aria-label="expand results"><span class="property-name">results</span><svg class="sc-dibcMh eVNtOe" version="1.1" viewBox="0 0 24 24" x="0" xmlns="http://www.w3.org/2000/svg" y="0" aria-hidden="true"><polygon points="17.3 8.3 12 13.6 6.7 8.3 5.3 9.7 12 16.4 18.7 9.7 "></polygon></svg></button></td><td class="sc-groBii dzPbxx"><div><div><span class="sc-byRegH sc-budtlk eyIabj jGdyyu"></span><span class="sc-byRegH sc-fJvEKN eyIabj fcQkkm">object</span></div> <div><div html="" class="sc-faJlkc sc-fdduAw kOqeZB csbUjU"></div></div></div></td></tr></tbody></table></div><div class="sc-jMpmlX sc-hoLldG kCcXaS jpPJrn"><div class="sc-cnVfeA eJpgoQ"><div class="sc-bgpKpp kuvBhH"><div class="sc-dRHutB iAOzgg"><div class="sc-VILhF itwxyW"><button><div class="sc-fIQtvO indwFD">Copy</div></button><button> Expand all </button><button> Collapse all </button></div><div tabindex="0" class="sc-faJlkc kOqeZB sc-eRJQtA eudsMA"><div class="redoc-json"><code><button class="collapser" aria-label="collapse"></button><span class="token punctuation">{</span><span class="ellipsis"></span><ul class="obj collapsible"><li><div class="hoverable "><span class="property token string">"results"</span>: <button class="collapser" aria-label="collapse"></button><span class="token punctuation">{</span><span class="ellipsis"></span><ul class="obj collapsible"><li><div class="hoverable collapsed"><span class="property token string">"context_id"</span>: <span class="token string">&quot;string&quot;</span><span class="token punctuation">,</span></div></li><li><div class="hoverable collapsed"><span class="property token string">"entities_deleted"</span>: <span class="token number">0</span><span class="token punctuation">,</span></div></li><li><div class="hoverable collapsed"><span class="property token string">"elements_deleted"</span>: <span class="token number">0</span><span class="token punctuation">,</span></div></li><li><div class="hoverable collapsed"><span class="property token string">"relationships_deleted"</span>: <span class="token number">0</span><span class="token punctuation">,</span></div></li><li><div class="hoverable collapsed"><span class="property token string">"versions_deleted"</span>: <span class="token number">0</span></div></li></ul><span class="token punctuation">}</span></div></li></ul><span class="token punctuation">}</span></code></div></div></div></div></div></div></div></div></div><div id="schema/NavigateResponse" data-section-id="schema/NavigateResponse" class="sc-dYOLZc kDuBQd"><div class="sc-jZhnRx tMeMv"><div class="sc-gwsNht iRoSfe"><h2 class="sc-kThouk jkwlQD"><a class="sc-kieALA jOWzfS" href="#schema/NavigateResponse" aria-label="schema/NavigateResponse"></a>NavigateResponse</h2></div></div><div class="sc-dYOLZc kDuBQd"><div class="sc-jZhnRx tMeMv"><div class="sc-gwsNht iRoSfe"><table class="sc-eGjrzz cGiuNU"><tbody><tr class=""><td kind="field" title="results" class="sc-kHNKno sc-frmfij sc-iyUCga cDFxGr kNAbAx fmWEAp"><span class="sc-hLyRwt lhCIDE"></span><button aria-label="expand results"><span class="property-name">results</span><svg class="sc-dibcMh eVNtOe" version="1.1" viewBox="0 0 24 24" x="0" xmlns="http://www.w3.org/2000/svg" y="0" aria-hidden="true"><polygon points="17.3 8.3 12 13.6 6.7 8.3 5.3 9.7 12 16.4 18.7 9.7 "></polygon></svg></button></td><td class="sc-groBii dzPbxx"><div><div><span class="sc-byRegH sc-budtlk eyIabj jGdyyu"></span><span class="sc-byRegH sc-fJvEKN eyIabj fcQkkm">object</span></div> <div><div html="" class="sc-faJlkc sc-fdduAw kOqeZB csbUjU"></div></div></div></td></tr><tr class="last "><td kind="field" title="next_token" class="sc-kHNKno sc-frmfij cDFxGr kNAbAx"><span class="sc-hLyRwt lhCIDE"></span><span class="property-name">next_token</span></td><td class="sc-groBii dzPbxx"><div><div><span class="sc-byRegH sc-budtlk eyIabj jGdyyu"></span><span class="sc-byRegH sc-fJvEKN eyIabj fcQkkm">string</span></div> <div><div html="&lt;p&gt;The token to be used to fetch the next page of results. This is a base64 encoded value.
If there are no more results, this field will not be present in the response.&lt;/p&gt;
" class="sc-faJlkc sc-fdduAw kOqeZB csbUjU"><p>The token to be used to fetch the next page of results. This is a base64 encoded value.
If there are no more results, this field will not be present in the response.</p>
</div></div></div></td></tr></tbody></table></div><div class="sc-jMpmlX sc-hoLldG kCcXaS jpPJrn"><div class="sc-cnVfeA eJpgoQ"><div class="sc-bgpKpp kuvBhH"><div class="sc-dRHutB iAOzgg"><div class="sc-VILhF itwxyW"><button><div class="sc-fIQtvO indwFD">Copy</div></button><button> Expand all </button><button> Collapse all </button></div><div tabindex="0" class="sc-faJlkc kOqeZB sc-eRJQtA eudsMA"><div class="redoc-json"><code><button class="collapser" aria-label="collapse"></button><span class="token punctuation">{</span><span class="ellipsis"></span><ul class="obj collapsible"><li><div class="hoverable "><span class="property token string">"results"</span>: <button class="collapser" aria-label="collapse"></button><span class="token punctuation">{</span><span class="ellipsis"></span><ul class="obj collapsible"><li><div class="hoverable collapsed"><span class="property token string">"entities"</span>: <button class="collapser" aria-label="expand"></button><span class="token punctuation">[</span><span class="ellipsis"></span><ul class="array collapsible"><li><div class="hoverable collapsed"><button class="collapser" aria-label="expand"></button><span class="token punctuation">{</span><span class="ellipsis"></span><ul class="obj collapsible"><li><div class="hoverable collapsed"><span class="property token string">"id"</span>: <span class="token string">&quot;string&quot;</span><span class="token punctuation">,</span></div></li><li><div class="hoverable collapsed"><span class="property token string">"model_name"</span>: <span class="token string">&quot;string&quot;</span><span class="token punctuation">,</span></div></li><li><div class="hoverable collapsed"><span class="property token string">"name"</span>: <span class="token string">&quot;string&quot;</span><span class="token punctuation">,</span></div></li><li><div class="hoverable collapsed"><span class="property token string">"description"</span>: <span class="token string">&quot;string&quot;</span><span class="token punctuation">,</span></div></li><li><div class="hoverable collapsed"><span class="property token string">"type"</span>: <span class="token string">&quot;string&quot;</span><span class="token punctuation">,</span></div></li><li><div class="hoverable collapsed"><span class="property token string">"consumption_type"</span>: <span class="token string">&quot;string&quot;</span><span class="token punctuation">,</span></div></li><li><div class="hoverable collapsed"><span class="property token string">"index"</span>: <span class="token number">0</span><span class="token punctuation">,</span></div></li><li><div class="hoverable collapsed"><span class="property token string">"properties"</span>: <span class="token punctuation">{ }</span><span class="token punctuation">,</span></div></li><li><div class="hoverable collapsed"><span class="property token string">"custom_properties"</span>: <span class="token punctuation">{ }</span><span class="token punctuation">,</span></div></li><li><div class="hoverable collapsed"><span class="property token string">"tags"</span>: <button class="collapser" aria-label="expand"></button><span class="token punctuation">[</span><span class="ellipsis"></span><ul class="array collapsible"><li><div class="hoverable collapsed"><span class="token string">&quot;string&quot;</span></div></li></ul><span class="token punctuation">]</span><span class="token punctuation">,</span></div></li><li><div class="hoverable collapsed"><span class="property token string">"created_by"</span>: <span class="token string">&quot;string&quot;</span><span class="token punctuation">,</span></div></li><li><div class="hoverable collapsed"><span class="property token string">"entity_elements"</span>: <button class="collapser" aria-label="expand"></button><span class="token punctuation">[</span><span class="ellipsis"></span><ul class="array collapsible"><li><div class="hoverable collapsed"><button class="collapser" aria-label="expand"></button><span class="token punctuation">{</span><span class="ellipsis"></span><ul class="obj collapsible"><li><div class="hoverable collapsed"><span class="property token string">"id"</span>: <span class="token string">&quot;string&quot;</span><span class="token punctuation">,</span></div></li><li><div class="hoverable collapsed"><span class="property token string">"model_name"</span>: <span class="token string">&quot;string&quot;</span><span class="token punctuation">,</span></div></li><li><div class="hoverable collapsed"><span class="property token string">"name"</span>: <span class="token string">&quot;string&quot;</span><span class="token punctuation">,</span></div></li><li><div class="hoverable collapsed"><span class="property token string">"description"</span>: <span class="token string">&quot;string&quot;</span><span class="token punctuation">,</span></div></li><li><div class="hoverable collapsed"><span class="property token string">"type"</span>: <span class="token string">&quot;string&quot;</span><span class="token punctuation">,</span></div></li><li><div class="hoverable collapsed"><span class="property token string">"consumption_type"</span>: <span class="token string">&quot;string&quot;</span><span class="token punctuation">,</span></div></li><li><div class="hoverable collapsed"><span class="property token string">"index"</span>: <span class="token number">0</span><span class="token punctuation">,</span></div></li><li><div class="hoverable collapsed"><span class="property token string">"data_type"</span>: <span class="token string">&quot;string&quot;</span><span class="token punctuation">,</span></div></li><li><div class="hoverable collapsed"><span class="property token string">"default_value"</span>: <span class="token string">&quot;string&quot;</span><span class="token punctuation">,</span></div></li><li><div class="hoverable collapsed"><span class="property token string">"enum"</span>: <button class="collapser" aria-label="expand"></button><span class="token punctuation">[</span><span class="ellipsis"></span><ul class="array collapsible"><li><div class="hoverable collapsed"><span class="token string">&quot;string&quot;</span></div></li></ul><span class="token punctuation">]</span><span class="token punctuation">,</span></div></li><li><div class="hoverable collapsed"><span class="property token string">"is_required"</span>: <span class="token boolean">true</span><span class="token punctuation">,</span></div></li><li><div class="hoverable collapsed"><span class="property token string">"is_array"</span>: <span class="token boolean">true</span><span class="token punctuation">,</span></div></li><li><div class="hoverable collapsed"><span class="property token string">"max_length"</span>: <span class="token number">0</span><span class="token punctuation">,</span></div></li><li><div class="hoverable collapsed"><span class="property token string">"precision"</span>: <span class="token number">0</span><span class="token punctuation">,</span></div></li><li><div class="hoverable collapsed"><span class="property token string">"scale"</span>: <span class="token number">0</span><span class="token punctuation">,</span></div></li><li><div class="hoverable collapsed"><span class="property token string">"properties"</span>: <span class="token punctuation">{ }</span><span class="token punctuation">,</span></div></li><li><div class="hoverable collapsed"><span class="property token string">"custom_properties"</span>: <span class="token punctuation">{ }</span><span class="token punctuation">,</span></div></li><li><div class="hoverable collapsed"><span class="property token string">"tags"</span>: <button class="collapser" aria-label="expand"></button><span class="token punctuation">[</span><span class="ellipsis"></span><ul class="array collapsible"><li><div class="hoverable collapsed"><span class="token string">&quot;string&quot;</span></div></li></ul><span class="token punctuation">]</span><span class="token punctuation">,</span></div></li><li><div class="hoverable collapsed"><span class="property token string">"created_by"</span>: <span class="token string">&quot;string&quot;</span></div></li></ul><span class="token punctuation">}</span></div></li></ul><span class="token punctuation">]</span></div></li></ul><span class="token punctuation">}</span></div></li></ul><span class="token punctuation">]</span><span class="token punctuation">,</span></div></li><li><div class="hoverable collapsed"><span class="property token string">"entity_elements"</span>: <button class="collapser" aria-label="expand"></button><span class="token punctuation">[</span><span class="ellipsis"></span><ul class="array collapsible"><li><div class="hoverable collapsed"><button class="collapser" aria-label="expand"></button><span class="token punctuation">{</span><span class="ellipsis"></span><ul class="obj collapsible"><li><div class="hoverable collapsed"><span class="property token string">"id"</span>: <span class="token string">&quot;string&quot;</span><span class="token punctuation">,</span></div></li><li><div class="hoverable collapsed"><span class="property token string">"model_name"</span>: <span class="token string">&quot;string&quot;</span><span class="token punctuation">,</span></div></li><li><div class="hoverable collapsed"><span class="property token string">"name"</span>: <span class="token string">&quot;string&quot;</span><span class="token punctuation">,</span></div></li><li><div class="hoverable collapsed"><span class="property token string">"description"</span>: <span class="token string">&quot;string&quot;</span><span class="token punctuation">,</span></div></li><li><div class="hoverable collapsed"><span class="property token string">"type"</span>: <span class="token string">&quot;string&quot;</span><span class="token punctuation">,</span></div></li><li><div class="hoverable collapsed"><span class="property token string">"consumption_type"</span>: <span class="token string">&quot;string&quot;</span><span class="token punctuation">,</span></div></li><li><div class="hoverable collapsed"><span class="property token string">"index"</span>: <span class="token number">0</span><span class="token punctuation">,</span></div></li><li><div class="hoverable collapsed"><span class="property token string">"data_type"</span>: <span class="token string">&quot;string&quot;</span><span class="token punctuation">,</span></div></li><li><div class="hoverable collapsed"><span class="property token string">"default_value"</span>: <span class="token string">&quot;string&quot;</span><span class="token punctuation">,</span></div></li><li><div class="hoverable collapsed"><span class="property token string">"enum"</span>: <button class="collapser" aria-label="expand"></button><span class="token punctuation">[</span><span class="ellipsis"></span><ul class="array collapsible"><li><div class="hoverable collapsed"><span class="token string">&quot;string&quot;</span></div></li></ul><span class="token punctuation">]</span><span class="token punctuation">,</span></div></li><li><div class="hoverable collapsed"><span class="property token string">"is_required"</span>: <span class="token boolean">true</span><span class="token punctuation">,</span></div></li><li><div class="hoverable collapsed"><span class="property token string">"is_array"</span>: <span class="token boolean">true</span><span class="token punctuation">,</span></div></li><li><div class="hoverable collapsed"><span class="property token string">"max_length"</span>: <span class="token number">0</span><span class="token punctuation">,</span></div></li><li><div class="hoverable collapsed"><span class="property token string">"precision"</span>: <span class="token number">0</span><span class="token punctuation">,</span></div></li><li><div class="hoverable collapsed"><span class="property token string">"scale"</span>: <span class="token number">0</span><span class="token punctuation">,</span></div></li><li><div class="hoverable collapsed"><span class="property token string">"properties"</span>: <span class="token punctuation">{ }</span><span class="token punctuation">,</span></div></li><li><div class="hoverable collapsed"><span class="property token string">"custom_properties"</span>: <span class="token punctuation">{ }</span><span class="token punctuation">,</span></div></li><li><div class="hoverable collapsed"><span class="property token string">"tags"</span>: <button class="collapser" aria-label="expand"></button><span class="token punctuation">[</span><span class="ellipsis"></span><ul class="array collapsible"><li><div class="hoverable collapsed"><span class="token string">&quot;string&quot;</span></div></li></ul><span class="token punctuation">]</span><span class="token punctuation">,</span></div></li><li><div class="hoverable collapsed"><span class="property token string">"created_by"</span>: <span class="token string">&quot;string&quot;</span></div></li></ul><span class="token punctuation">}</span></div></li></ul><span class="token punctuation">]</span><span class="token punctuation">,</span></div></li><li><div class="hoverable collapsed"><span class="property token string">"relationships"</span>: <button class="collapser" aria-label="expand"></button><span class="token punctuation">[</span><span class="ellipsis"></span><ul class="array collapsible"><li><div class="hoverable collapsed"><button class="collapser" aria-label="expand"></button><span class="token punctuation">{</span><span class="ellipsis"></span><ul class="obj collapsible"><li><div class="hoverable collapsed"><span class="property token string">"id"</span>: <span class="token string">&quot;string&quot;</span><span class="token punctuation">,</span></div></li><li><div class="hoverable collapsed"><span class="property token string">"name"</span>: <span class="token string">&quot;string&quot;</span><span class="token punctuation">,</span></div></li><li><div class="hoverable collapsed"><span class="property token string">"description"</span>: <span class="token string">&quot;string&quot;</span><span class="token punctuation">,</span></div></li><li><div class="hoverable collapsed"><span class="property token string">"type"</span>: <span class="token string">&quot;string&quot;</span><span class="token punctuation">,</span></div></li><li><div class="hoverable collapsed"><span class="property token string">"index"</span>: <span class="token number">0</span><span class="token punctuation">,</span></div></li><li><div class="hoverable collapsed"><span class="property token string">"source"</span>: <button class="collapser" aria-label="expand"></button><span class="token punctuation">{</span><span class="ellipsis"></span><ul class="obj collapsible"><li><div class="hoverable collapsed"><span class="property token string">"id"</span>: <span class="token string">&quot;cpt_xkHRtSJrPh4JLgeZQLfA7VdLsbi&quot;</span><span class="token punctuation">,</span></div></li><li><div class="hoverable collapsed"><span class="property token string">"$ref"</span>: <span class="token string">&quot;string&quot;</span></div></li></ul><span class="token punctuation">}</span><span class="token punctuation">,</span></div></li><li><div class="hoverable collapsed"><span class="property token string">"target"</span>: <button class="collapser" aria-label="expand"></button><span class="token punctuation">{</span><span class="ellipsis"></span><ul class="obj collapsible"><li><div class="hoverable collapsed"><span class="property token string">"id"</span>: <span class="token string">&quot;cpt_xkHRtSJrPh4JLgeZQLfA7VdLsbi&quot;</span><span class="token punctuation">,</span></div></li><li><div class="hoverable collapsed"><span class="property token string">"$ref"</span>: <span class="token string">&quot;string&quot;</span></div></li></ul><span class="token punctuation">}</span><span class="token punctuation">,</span></div></li><li><div class="hoverable collapsed"><span class="property token string">"properties"</span>: <span class="token punctuation">{ }</span><span class="token punctuation">,</span></div></li><li><div class="hoverable collapsed"><span class="property token string">"tags"</span>: <button class="collapser" aria-label="expand"></button><span class="token punctuation">[</span><span class="ellipsis"></span><ul class="array collapsible"><li><div class="hoverable collapsed"><span class="token string">&quot;string&quot;</span></div></li></ul><span class="token punctuation">]</span><span class="token punctuation">,</span></div></li><li><div class="hoverable collapsed"><span class="property token string">"created_by"</span>: <span class="token string">&quot;string&quot;</span></div></li></ul><span class="token punctuation">}</span></div></li></ul><span class="token punctuation">]</span></div></li></ul><span class="token punctuation">}</span><span class="token punctuation">,</span></div></li><li><div class="hoverable "><span class="property token string">"next_token"</span>: <span class="token string">&quot;string&quot;</span></div></li></ul><span class="token punctuation">}</span></code></div></div></div></div></div></div></div></div></div><div id="schema/ImportDataModelRequest" data-section-id="schema/ImportDataModelRequest" class="sc-dYOLZc kDuBQd"><div class="sc-jZhnRx tMeMv"><div class="sc-gwsNht iRoSfe"><h2 class="sc-kThouk jkwlQD"><a class="sc-kieALA jOWzfS" href="#schema/ImportDataModelRequest" aria-label="schema/ImportDataModelRequest"></a>ImportDataModelRequest</h2></div></div><div class="sc-dYOLZc kDuBQd"><div class="sc-jZhnRx tMeMv"><div class="sc-gwsNht iRoSfe"><table class="sc-eGjrzz cGiuNU"><tbody><tr class=""><td kind="field" title="entities" class="sc-kHNKno sc-frmfij sc-iyUCga cDFxGr kNAbAx fmWEAp"><span class="sc-hLyRwt lhCIDE"></span><button aria-label="expand entities"><span class="property-name">entities</span><svg class="sc-dibcMh eVNtOe" version="1.1" viewBox="0 0 24 24" x="0" xmlns="http://www.w3.org/2000/svg" y="0" aria-hidden="true"><polygon points="17.3 8.3 12 13.6 6.7 8.3 5.3 9.7 12 16.4 18.7 9.7 "></polygon></svg></button></td><td class="sc-groBii dzPbxx"><div><div><span class="sc-byRegH sc-budtlk eyIabj jGdyyu">Array of </span><span class="sc-byRegH sc-fJvEKN eyIabj fcQkkm">objects</span><span class="sc-byRegH sc-hsXxFb eyIabj iZVMho"> (<!-- -->Entity<!-- -->) </span></div> <div><div html="&lt;p&gt;The list of entities in the data model.
Each entity represents a distinct object or concept within the data model.
The entity can be a table, view, or any other object that is relevant to the data model.
Each entity has a unique name and can have multiple entity elements (fields) associated with it.&lt;/p&gt;
" class="sc-faJlkc sc-fdduAw kOqeZB csbUjU"><p>The list of entities in the data model.
Each entity represents a distinct object or concept within the data model.
The entity can be a table, view, or any other object that is relevant to the data model.
Each entity has a unique name and can have multiple entity elements (fields) associated with it.</p>
</div></div></div></td></tr><tr class="last "><td kind="field" title="relationships" class="sc-kHNKno sc-frmfij sc-iyUCga cDFxGr kNAbAx fmWEAp"><span class="sc-hLyRwt lhCIDE"></span><button aria-label="expand relationships"><span class="property-name">relationships</span><svg class="sc-dibcMh eVNtOe" version="1.1" viewBox="0 0 24 24" x="0" xmlns="http://www.w3.org/2000/svg" y="0" aria-hidden="true"><polygon points="17.3 8.3 12 13.6 6.7 8.3 5.3 9.7 12 16.4 18.7 9.7 "></polygon></svg></button></td><td class="sc-groBii dzPbxx"><div><div><span class="sc-byRegH sc-budtlk eyIabj jGdyyu">Array of </span><span class="sc-byRegH sc-fJvEKN eyIabj fcQkkm">objects</span><span class="sc-byRegH sc-hsXxFb eyIabj iZVMho"> (<!-- -->Relationship<!-- -->) </span></div> <div><div html="&lt;p&gt;The list of relationships in the data model.
Each relationship represents a connection between entities or entity elements.&lt;/p&gt;
" class="sc-faJlkc sc-fdduAw kOqeZB csbUjU"><p>The list of relationships in the data model.
Each relationship represents a connection between entities or entity elements.</p>
</div></div></div></td></tr></tbody></table></div><div class="sc-jMpmlX sc-hoLldG kCcXaS jpPJrn"><div class="sc-cnVfeA eJpgoQ"><div class="sc-bgpKpp kuvBhH"><div class="sc-dRHutB iAOzgg"><div class="sc-VILhF itwxyW"><button><div class="sc-fIQtvO indwFD">Copy</div></button><button> Expand all </button><button> Collapse all </button></div><div tabindex="0" class="sc-faJlkc kOqeZB sc-eRJQtA eudsMA"><div class="redoc-json"><code><button class="collapser" aria-label="collapse"></button><span class="token punctuation">{</span><span class="ellipsis"></span><ul class="obj collapsible"><li><div class="hoverable "><span class="property token string">"entities"</span>: <button class="collapser" aria-label="collapse"></button><span class="token punctuation">[</span><span class="ellipsis"></span><ul class="array collapsible"><li><div class="hoverable collapsed"><button class="collapser" aria-label="expand"></button><span class="token punctuation">{</span><span class="ellipsis"></span><ul class="obj collapsible"><li><div class="hoverable collapsed"><span class="property token string">"id"</span>: <span class="token string">&quot;string&quot;</span><span class="token punctuation">,</span></div></li><li><div class="hoverable collapsed"><span class="property token string">"model_name"</span>: <span class="token string">&quot;string&quot;</span><span class="token punctuation">,</span></div></li><li><div class="hoverable collapsed"><span class="property token string">"name"</span>: <span class="token string">&quot;string&quot;</span><span class="token punctuation">,</span></div></li><li><div class="hoverable collapsed"><span class="property token string">"description"</span>: <span class="token string">&quot;string&quot;</span><span class="token punctuation">,</span></div></li><li><div class="hoverable collapsed"><span class="property token string">"type"</span>: <span class="token string">&quot;string&quot;</span><span class="token punctuation">,</span></div></li><li><div class="hoverable collapsed"><span class="property token string">"consumption_type"</span>: <span class="token string">&quot;string&quot;</span><span class="token punctuation">,</span></div></li><li><div class="hoverable collapsed"><span class="property token string">"index"</span>: <span class="token number">0</span><span class="token punctuation">,</span></div></li><li><div class="hoverable collapsed"><span class="property token string">"properties"</span>: <span class="token punctuation">{ }</span><span class="token punctuation">,</span></div></li><li><div class="hoverable collapsed"><span class="property token string">"custom_properties"</span>: <span class="token punctuation">{ }</span><span class="token punctuation">,</span></div></li><li><div class="hoverable collapsed"><span class="property token string">"tags"</span>: <button class="collapser" aria-label="expand"></button><span class="token punctuation">[</span><span class="ellipsis"></span><ul class="array collapsible"><li><div class="hoverable collapsed"><span class="token string">&quot;string&quot;</span></div></li></ul><span class="token punctuation">]</span><span class="token punctuation">,</span></div></li><li><div class="hoverable collapsed"><span class="property token string">"created_by"</span>: <span class="token string">&quot;string&quot;</span><span class="token punctuation">,</span></div></li><li><div class="hoverable collapsed"><span class="property token string">"entity_elements"</span>: <button class="collapser" aria-label="expand"></button><span class="token punctuation">[</span><span class="ellipsis"></span><ul class="array collapsible"><li><div class="hoverable collapsed"><button class="collapser" aria-label="expand"></button><span class="token punctuation">{</span><span class="ellipsis"></span><ul class="obj collapsible"><li><div class="hoverable collapsed"><span class="property token string">"id"</span>: <span class="token string">&quot;string&quot;</span><span class="token punctuation">,</span></div></li><li><div class="hoverable collapsed"><span class="property token string">"model_name"</span>: <span class="token string">&quot;string&quot;</span><span class="token punctuation">,</span></div></li><li><div class="hoverable collapsed"><span class="property token string">"name"</span>: <span class="token string">&quot;string&quot;</span><span class="token punctuation">,</span></div></li><li><div class="hoverable collapsed"><span class="property token string">"description"</span>: <span class="token string">&quot;string&quot;</span><span class="token punctuation">,</span></div></li><li><div class="hoverable collapsed"><span class="property token string">"type"</span>: <span class="token string">&quot;string&quot;</span><span class="token punctuation">,</span></div></li><li><div class="hoverable collapsed"><span class="property token string">"consumption_type"</span>: <span class="token string">&quot;string&quot;</span><span class="token punctuation">,</span></div></li><li><div class="hoverable collapsed"><span class="property token string">"index"</span>: <span class="token number">0</span><span class="token punctuation">,</span></div></li><li><div class="hoverable collapsed"><span class="property token string">"data_type"</span>: <span class="token string">&quot;string&quot;</span><span class="token punctuation">,</span></div></li><li><div class="hoverable collapsed"><span class="property token string">"default_value"</span>: <span class="token string">&quot;string&quot;</span><span class="token punctuation">,</span></div></li><li><div class="hoverable collapsed"><span class="property token string">"enum"</span>: <button class="collapser" aria-label="expand"></button><span class="token punctuation">[</span><span class="ellipsis"></span><ul class="array collapsible"><li><div class="hoverable collapsed"><span class="token string">&quot;string&quot;</span></div></li></ul><span class="token punctuation">]</span><span class="token punctuation">,</span></div></li><li><div class="hoverable collapsed"><span class="property token string">"is_required"</span>: <span class="token boolean">true</span><span class="token punctuation">,</span></div></li><li><div class="hoverable collapsed"><span class="property token string">"is_array"</span>: <span class="token boolean">true</span><span class="token punctuation">,</span></div></li><li><div class="hoverable collapsed"><span class="property token string">"max_length"</span>: <span class="token number">0</span><span class="token punctuation">,</span></div></li><li><div class="hoverable collapsed"><span class="property token string">"precision"</span>: <span class="token number">0</span><span class="token punctuation">,</span></div></li><li><div class="hoverable collapsed"><span class="property token string">"scale"</span>: <span class="token number">0</span><span class="token punctuation">,</span></div></li><li><div class="hoverable collapsed"><span class="property token string">"properties"</span>: <span class="token punctuation">{ }</span><span class="token punctuation">,</span></div></li><li><div class="hoverable collapsed"><span class="property token string">"custom_properties"</span>: <span class="token punctuation">{ }</span><span class="token punctuation">,</span></div></li><li><div class="hoverable collapsed"><span class="property token string">"tags"</span>: <button class="collapser" aria-label="expand"></button><span class="token punctuation">[</span><span class="ellipsis"></span><ul class="array collapsible"><li><div class="hoverable collapsed"><span class="token string">&quot;string&quot;</span></div></li></ul><span class="token punctuation">]</span><span class="token punctuation">,</span></div></li><li><div class="hoverable collapsed"><span class="property token string">"created_by"</span>: <span class="token string">&quot;string&quot;</span></div></li></ul><span class="token punctuation">}</span></div></li></ul><span class="token punctuation">]</span></div></li></ul><span class="token punctuation">}</span></div></li></ul><span class="token punctuation">]</span><span class="token punctuation">,</span></div></li><li><div class="hoverable "><span class="property token string">"relationships"</span>: <button class="collapser" aria-label="collapse"></button><span class="token punctuation">[</span><span class="ellipsis"></span><ul class="array collapsible"><li><div class="hoverable collapsed"><button class="collapser" aria-label="expand"></button><span class="token punctuation">{</span><span class="ellipsis"></span><ul class="obj collapsible"><li><div class="hoverable collapsed"><span class="property token string">"id"</span>: <span class="token string">&quot;string&quot;</span><span class="token punctuation">,</span></div></li><li><div class="hoverable collapsed"><span class="property token string">"name"</span>: <span class="token string">&quot;string&quot;</span><span class="token punctuation">,</span></div></li><li><div class="hoverable collapsed"><span class="property token string">"description"</span>: <span class="token string">&quot;string&quot;</span><span class="token punctuation">,</span></div></li><li><div class="hoverable collapsed"><span class="property token string">"type"</span>: <span class="token string">&quot;string&quot;</span><span class="token punctuation">,</span></div></li><li><div class="hoverable collapsed"><span class="property token string">"index"</span>: <span class="token number">0</span><span class="token punctuation">,</span></div></li><li><div class="hoverable collapsed"><span class="property token string">"source"</span>: <button class="collapser" aria-label="expand"></button><span class="token punctuation">{</span><span class="ellipsis"></span><ul class="obj collapsible"><li><div class="hoverable collapsed"><span class="property token string">"id"</span>: <span class="token string">&quot;cpt_xkHRtSJrPh4JLgeZQLfA7VdLsbi&quot;</span><span class="token punctuation">,</span></div></li><li><div class="hoverable collapsed"><span class="property token string">"$ref"</span>: <span class="token string">&quot;string&quot;</span></div></li></ul><span class="token punctuation">}</span><span class="token punctuation">,</span></div></li><li><div class="hoverable collapsed"><span class="property token string">"target"</span>: <button class="collapser" aria-label="expand"></button><span class="token punctuation">{</span><span class="ellipsis"></span><ul class="obj collapsible"><li><div class="hoverable collapsed"><span class="property token string">"id"</span>: <span class="token string">&quot;cpt_xkHRtSJrPh4JLgeZQLfA7VdLsbi&quot;</span><span class="token punctuation">,</span></div></li><li><div class="hoverable collapsed"><span class="property token string">"$ref"</span>: <span class="token string">&quot;string&quot;</span></div></li></ul><span class="token punctuation">}</span><span class="token punctuation">,</span></div></li><li><div class="hoverable collapsed"><span class="property token string">"properties"</span>: <span class="token punctuation">{ }</span><span class="token punctuation">,</span></div></li><li><div class="hoverable collapsed"><span class="property token string">"tags"</span>: <button class="collapser" aria-label="expand"></button><span class="token punctuation">[</span><span class="ellipsis"></span><ul class="array collapsible"><li><div class="hoverable collapsed"><span class="token string">&quot;string&quot;</span></div></li></ul><span class="token punctuation">]</span><span class="token punctuation">,</span></div></li><li><div class="hoverable collapsed"><span class="property token string">"created_by"</span>: <span class="token string">&quot;string&quot;</span></div></li></ul><span class="token punctuation">}</span></div></li></ul><span class="token punctuation">]</span></div></li></ul><span class="token punctuation">}</span></code></div></div></div></div></div></div></div></div></div><div id="schema/ImportDataModelResponse" data-section-id="schema/ImportDataModelResponse" class="sc-dYOLZc kDuBQd"><div class="sc-jZhnRx tMeMv"><div class="sc-gwsNht iRoSfe"><h2 class="sc-kThouk jkwlQD"><a class="sc-kieALA jOWzfS" href="#schema/ImportDataModelResponse" aria-label="schema/ImportDataModelResponse"></a>ImportDataModelResponse</h2></div></div><div class="sc-dYOLZc kDuBQd"><div class="sc-jZhnRx tMeMv"><div class="sc-gwsNht iRoSfe"><table class="sc-eGjrzz cGiuNU"><tbody><tr class="last "><td kind="field" title="new_version" class="sc-kHNKno sc-frmfij cDFxGr kNAbAx"><span class="sc-hLyRwt lhCIDE"></span><span class="property-name">new_version</span><div class="sc-byRegH sc-isZTFa eyIabj iUhYwd">required</div></td><td class="sc-groBii dzPbxx"><div><div><span class="sc-byRegH sc-budtlk eyIabj jGdyyu"></span><span class="sc-byRegH sc-fJvEKN eyIabj fcQkkm">string</span></div> <div><div html="&lt;p&gt;The new version of the data model.&lt;/p&gt;
" class="sc-faJlkc sc-fdduAw kOqeZB csbUjU"><p>The new version of the data model.</p>
</div></div></div></td></tr></tbody></table></div><div class="sc-jMpmlX sc-hoLldG kCcXaS jpPJrn"><div class="sc-cnVfeA eJpgoQ"><div class="sc-bgpKpp kuvBhH"><div class="sc-dRHutB iAOzgg"><div class="sc-VILhF itwxyW"><button><div class="sc-fIQtvO indwFD">Copy</div></button></div><div tabindex="0" class="sc-faJlkc kOqeZB sc-eRJQtA eudsMA"><div class="redoc-json"><code><button class="collapser" aria-label="collapse"></button><span class="token punctuation">{</span><span class="ellipsis"></span><ul class="obj collapsible"><li><div class="hoverable "><span class="property token string">"new_version"</span>: <span class="token string">&quot;string&quot;</span></div></li></ul><span class="token punctuation">}</span></code></div></div></div></div></div></div></div></div></div><div id="schema/CatalogError" data-section-id="schema/CatalogError" class="sc-dYOLZc kDuBQd"><div class="sc-jZhnRx tMeMv"><div class="sc-gwsNht iRoSfe"><h2 class="sc-kThouk jkwlQD"><a class="sc-kieALA jOWzfS" href="#schema/CatalogError" aria-label="schema/CatalogError"></a>CatalogError</h2></div></div><div class="sc-dYOLZc kDuBQd"><div class="sc-jZhnRx tMeMv"><div class="sc-gwsNht iRoSfe"><table class="sc-eGjrzz cGiuNU"><tbody><tr class=""><td kind="field" title="trace_id" class="sc-kHNKno sc-frmfij cDFxGr kNAbAx"><span class="sc-hLyRwt lhCIDE"></span><span class="property-name">trace_id</span><div class="sc-byRegH sc-isZTFa eyIabj iUhYwd">required</div></td><td class="sc-groBii dzPbxx"><div><div><span class="sc-byRegH sc-budtlk eyIabj jGdyyu"></span><span class="sc-byRegH sc-fJvEKN eyIabj fcQkkm">string</span></div> <div><div html="&lt;p&gt;The unique identifier associated with the API request to assist with troubleshooting.&lt;/p&gt;
" class="sc-faJlkc sc-fdduAw kOqeZB csbUjU"><p>The unique identifier associated with the API request to assist with troubleshooting.</p>
</div></div></div></td></tr><tr class=""><td kind="field" title="type" class="sc-kHNKno sc-frmfij cDFxGr kNAbAx"><span class="sc-hLyRwt lhCIDE"></span><span class="property-name">type</span><div class="sc-byRegH sc-isZTFa eyIabj iUhYwd">required</div></td><td class="sc-groBii dzPbxx"><div><div><span class="sc-byRegH sc-budtlk eyIabj jGdyyu"></span><span class="sc-byRegH sc-fJvEKN eyIabj fcQkkm">string</span></div> <div><div html="&lt;p&gt;A URI reference [URI] that identifies the error type.&lt;/p&gt;
" class="sc-faJlkc sc-fdduAw kOqeZB csbUjU"><p>A URI reference [URI] that identifies the error type.</p>
</div></div></div></td></tr><tr class=""><td kind="field" title="status" class="sc-kHNKno sc-frmfij cDFxGr kNAbAx"><span class="sc-hLyRwt lhCIDE"></span><span class="property-name">status</span></td><td class="sc-groBii dzPbxx"><div><div><span class="sc-byRegH sc-budtlk eyIabj jGdyyu"></span><span class="sc-byRegH sc-fJvEKN eyIabj fcQkkm">int</span></div> <div><div html="&lt;p&gt;The HTTP status code associated with the error.&lt;/p&gt;
" class="sc-faJlkc sc-fdduAw kOqeZB csbUjU"><p>The HTTP status code associated with the error.</p>
</div></div></div></td></tr><tr class=""><td kind="field" title="title" class="sc-kHNKno sc-frmfij cDFxGr kNAbAx"><span class="sc-hLyRwt lhCIDE"></span><span class="property-name">title</span><div class="sc-byRegH sc-isZTFa eyIabj iUhYwd">required</div></td><td class="sc-groBii dzPbxx"><div><div><span class="sc-byRegH sc-budtlk eyIabj jGdyyu"></span><span class="sc-byRegH sc-fJvEKN eyIabj fcQkkm">string</span></div> <div><div html="&lt;p&gt;A short, human-readable summary of the error type.&lt;/p&gt;
" class="sc-faJlkc sc-fdduAw kOqeZB csbUjU"><p>A short, human-readable summary of the error type.</p>
</div></div></div></td></tr><tr class=""><td kind="field" title="detail" class="sc-kHNKno sc-frmfij cDFxGr kNAbAx"><span class="sc-hLyRwt lhCIDE"></span><span class="property-name">detail</span></td><td class="sc-groBii dzPbxx"><div><div><span class="sc-byRegH sc-budtlk eyIabj jGdyyu"></span><span class="sc-byRegH sc-fJvEKN eyIabj fcQkkm">string</span></div> <div><div html="&lt;p&gt;A human-readable explanation specific to this occurrence of the error.&lt;/p&gt;
" class="sc-faJlkc sc-fdduAw kOqeZB csbUjU"><p>A human-readable explanation specific to this occurrence of the error.</p>
</div></div></div></td></tr><tr class=""><td kind="field" title="message" class="sc-kHNKno sc-frmfij cDFxGr kNAbAx"><span class="sc-hLyRwt lhCIDE"></span><span class="property-name">message</span></td><td class="sc-groBii dzPbxx"><div><div><span class="sc-byRegH sc-budtlk eyIabj jGdyyu"></span><span class="sc-byRegH sc-fJvEKN eyIabj fcQkkm">string</span></div> <div><div html="&lt;p&gt;Duplicate of detail field for backward compatibility.&lt;/p&gt;
" class="sc-faJlkc sc-fdduAw kOqeZB csbUjU"><p>Duplicate of detail field for backward compatibility.</p>
</div></div></div></td></tr><tr class="last "><td kind="field" title="errors" class="sc-kHNKno sc-frmfij sc-iyUCga cDFxGr kNAbAx fmWEAp"><span class="sc-hLyRwt lhCIDE"></span><button aria-label="expand errors"><span class="property-name">errors</span><svg class="sc-dibcMh eVNtOe" version="1.1" viewBox="0 0 24 24" x="0" xmlns="http://www.w3.org/2000/svg" y="0" aria-hidden="true"><polygon points="17.3 8.3 12 13.6 6.7 8.3 5.3 9.7 12 16.4 18.7 9.7 "></polygon></svg></button></td><td class="sc-groBii dzPbxx"><div><div><span class="sc-byRegH sc-budtlk eyIabj jGdyyu">Array of </span><span class="sc-byRegH sc-fJvEKN eyIabj fcQkkm">objects</span><span class="sc-byRegH sc-hsXxFb eyIabj iZVMho"> (<!-- -->ErrorDetail<!-- -->) </span></div> <div><div html="&lt;p&gt;An optional list of individual errors when multiple errors occur.&lt;/p&gt;
" class="sc-faJlkc sc-fdduAw kOqeZB csbUjU"><p>An optional list of individual errors when multiple errors occur.</p>
</div></div></div></td></tr></tbody></table></div><div class="sc-jMpmlX sc-hoLldG kCcXaS jpPJrn"><div class="sc-cnVfeA eJpgoQ"><div class="sc-bgpKpp kuvBhH"><div class="sc-dRHutB iAOzgg"><div class="sc-VILhF itwxyW"><button><div class="sc-fIQtvO indwFD">Copy</div></button><button> Expand all </button><button> Collapse all </button></div><div tabindex="0" class="sc-faJlkc kOqeZB sc-eRJQtA eudsMA"><div class="redoc-json"><code><button class="collapser" aria-label="collapse"></button><span class="token punctuation">{</span><span class="ellipsis"></span><ul class="obj collapsible"><li><div class="hoverable "><span class="property token string">"trace_id"</span>: <span class="token string">&quot;123e4567-e89b-12d3-a456-426614174000&quot;</span><span class="token punctuation">,</span></div></li><li><div class="hoverable "><span class="property token string">"type"</span>: <span class="token string">&quot;</span><a href="https://catalog.syniti.com/errors/validation-error">https://catalog.syniti.com/errors/validation-error</a><span class="token string">&quot;</span><span class="token punctuation">,</span></div></li><li><div class="hoverable "><span class="property token string">"status"</span>: <span class="token number">400</span><span class="token punctuation">,</span></div></li><li><div class="hoverable "><span class="property token string">"title"</span>: <span class="token string">&quot;Validation Error&quot;</span><span class="token punctuation">,</span></div></li><li><div class="hoverable "><span class="property token string">"detail"</span>: <span class="token string">&quot;One or more validation errors occurred. Errors: name: [name is required], age: [must be a positive integer]&quot;</span><span class="token punctuation">,</span></div></li><li><div class="hoverable "><span class="property token string">"errors"</span>: <button class="collapser" aria-label="collapse"></button><span class="token punctuation">[</span><span class="ellipsis"></span><ul class="array collapsible"><li><div class="hoverable collapsed"><button class="collapser" aria-label="expand"></button><span class="token punctuation">{</span><span class="ellipsis"></span><ul class="obj collapsible"><li><div class="hoverable collapsed"><span class="property token string">"field"</span>: <span class="token string">&quot;name&quot;</span><span class="token punctuation">,</span></div></li><li><div class="hoverable collapsed"><span class="property token string">"message"</span>: <span class="token string">&quot;name is required.&quot;</span></div></li></ul><span class="token punctuation">}</span>,</div></li><li><div class="hoverable collapsed"><button class="collapser" aria-label="expand"></button><span class="token punctuation">{</span><span class="ellipsis"></span><ul class="obj collapsible"><li><div class="hoverable collapsed"><span class="property token string">"field"</span>: <span class="token string">&quot;age&quot;</span><span class="token punctuation">,</span></div></li><li><div class="hoverable collapsed"><span class="property token string">"message"</span>: <span class="token string">&quot;must be a positive integer.&quot;</span></div></li></ul><span class="token punctuation">}</span></div></li></ul><span class="token punctuation">]</span></div></li></ul><span class="token punctuation">}</span></code></div></div></div></div></div></div></div></div></div></div><div class="sc-epRJRH haiOeP"></div></div></div>
      <script>
      const __redoc_state = {"menu":{"activeItemIdx":-1},"spec":{"data":{"openapi":"3.0.0","info":{"version":"0.0.1","title":"Catalog Service API","description":"The Syniti Knowledge Platform (SKP) Catalog is the collection of technical and business data definitions that is used to document and describe the systems and business processes of our clients.\nThe data definitons stored in the Catalog is used to enable several functionalities within SKP like data profiling, data migration, data quality, matching, data replication, reporting, etc.\nThis catalog exists primarily as a means to enable this functionality, but should also be considered as a knowledge repository for the business users.\n\nKey features include:\n\n- Create and Manage Technical Data Definitions retrieved from the MetaScanner (Database Table, Database View)\n- Create and Manage Business Data Definitions (View, Rule, Validation)\n- Capture data profiling information\n- Ablity to version and manage the lifecycle of the Data Definitions\n- Support to catpure the schema of future polyglot Datasources (ODATA, OpenAPI, etc.)\n"},"servers":[{"url":"http://catalog.govern.svc.cluster.local"},{"url":"https://ct.mgt.syniti-dev.com"}],"security":[{"bearerAuth":[]}],"tags":[{"name":"DataModel","description":"Data Model Operations"},{"name":"Entity","description":"Entity Operations"},{"name":"EntityElement","description":"Element Operations"},{"name":"Import DataModel","description":"Import Data Model Operations"}],"paths":{"/contexts/{context_id}/datamodel":{"post":{"tags":["DataModel"],"summary":"Create Data Model","operationId":"createDataModel","description":"The API allows you to create a new data model with a complete, authoritative representation.\n\n\n**How It Works**\n- Creation: Use the POST method to create a new data model. The server stores it as the authoritative version.\n- Full Update: Use the PUT method to replace an existing data model entirely with an updated version.\n- Partial Update: Use the PATCH method for partial updates, specifying only the changes to be made.\n\n**Key Points for Consumers**\n- Complete Representation: The PUT method requires the entire data model. Missing sections will be removed or reset.\n- Versioning: Each full update creates a new version. Include the current version to avoid conflicts. Updates in progress for the same version will be rejected.\n- Idempotency: Repeating the same PUT request will yield the same result.\n\n**Data Model Context**\n- The data model is defined within a context like a Datastore.\n- Wihthin a context,\n  - The name of the entity and relationship must be unique.\n  - The name of an entity element must be unique within the entity.\n\n**Naming Rules**\n- Names can contain\n  - alphanumeric characters\n  - case insenstive\n  - underscore\n  - hyphen\n- Entity / Relationship Name\n  - Must be unique within the namespace\n- Entity Element Name\n  - Must be unique within the entity.\n","parameters":[{"name":"context_id","in":"path","description":"The unique identifier for the datastore.\n","required":true,"schema":{"type":"string"}}],"requestBody":{"content":{"application/json":{"schema":{"$ref":"#/components/schemas/DataModelRequest"},"examples":{"example-1":{"summary":"Example of a Data Model","value":{"meta_schema_version":"relational-20250501","entities":[{"model_name":"ns1.Product","name":"Product","type":"TABLE","consumption_type":"dataset","description":"","active":true,"index":1,"tags":[],"properties":{"schema":"Production"},"custom_properties":{},"entity_elements":[{"model_name":"ns1.Product.ProductID","name":"ProductID","type":"COLUMN","consumption_type":"field","description":"","active":true,"index":1,"data_type":"int","default_value":"","precision":10,"scale":0,"is_required":false,"tags":[],"properties":{},"custom_properties":{}},{"model_name":"ns1.Product.Name","name":"Name","type":"COLUMN","consumption_type":"field","index":2,"data_type":"Name","default_value":"","max_length":50,"is_required":false,"properties":{},"custom_properties":{}},{"model_name":"ns1.Product.Model","name":"Model","type":"COLUMN","consumption_type":"field","description":"Stores the model GUID that is a foreign key to another table","index":3,"data_type":"nvarchar","default_value":"","max_length":24,"is_required":false,"properties":{},"custom_properties":{}},{"model_name":"ns1.Product.Description","name":"Description","type":"COLUMN","consumption_type":"field","index":4,"data_type":"nvarchar","default_value":"","max_length":400,"is_required":false,"properties":{},"custom_properties":{}},{"model_name":"ns1.Product.Makeflag","name":"Makeflag","type":"COLUMN","consumption_type":"field","index":5,"data_type":"Flag","default_value":"","precision":1,"scale":0,"is_required":false,"properties":{},"custom_properties":{}},{"model_name":"ns1.Product.PK_ProductID","name":"PK_ProductID","type":"PRIMARY-KEY","consumption_type":"key"},{"model_name":"ns1.Product.IDX_Name_Makeflag","name":"IDX_Name_MakeFlag","type":"INDEX","consumption_type":"index","properties":{"is_unique":true}},{"model_name":"ns1.Product.FK_Model","name":"FK_Model","type":"FOREIGN-KEY","consumption_type":"constraint","properties":{"on_update":"no_action","on_delete":"no_action"}}]},{"model_name":"ns1.ProductView","name":"ProductView","type":"VIEW","consumption_type":"dataset","properties":{"schema":"Production"},"entity_elements":[{"model_name":"ns1.ProductView.ProductID","name":"ProductID","type":"COLUMN","consumption_type":"field","index":1,"data_type":"int","default_value":"","precision":10,"scale":0,"is_required":false,"properties":{}},{"model_name":"ns1.ProductView.Name","name":"Name","type":"COLUMN","consumption_type":"field","index":2,"data_type":"Name","default_value":"","max_length":50,"is_required":false,"properties":{}}]}],"relationships":[{"name":"PK_Production_Product_ProductID","type":"PK_REF","description":"","source":"#element/ns1.Product.PK_ProductID","target":"#element/ns1.Product.ProductID"},{"name":"IDX_Production_Product_Name_MakeFlag_Name","type":"IDX_REF","description":"","index":10,"source":"#element/ns1.Product.IDX_Name_Makeflag","target":"#element/ns1.Product.Name"},{"name":"IDX_Production_Product_Name_MakeFlag_MakeFlag","type":"IDX_REF","description":"","index":20,"source":"#element/ns1.Product.IDX_Name_Makeflag","target":"#element/ns1.Product.Makeflag"},{"name":"FK_Production_Product_Model","type":"FK_REF_SOURCE","description":"","source":"#element/ns1.Product.FK_Model","target":"#element/ns1.Product.Model"},{"name":"FK_Production_Product_Model","type":"FK_REF_TARGET","description":"","source":"#element/ns1.Product.FK_Model","target":"#element/ns1.ProductModel.ModelID"}]}}}}}},"responses":{"200":{"description":"Created or Updated.\n","content":{"application/json":{"schema":{"$ref":"#/components/schemas/DataModelResponse"},"examples":{"example-1":{"summary":"Example of a Data Model Response","value":{"new_version":"20250201-002"}}}}}},"400":{"description":"Bad Request.","content":{"application/json":{"schema":{"$ref":"#/components/schemas/ApiError","examples":{"example-1":{"summary":"Example of a Bad Request","value":{"trace_id":"4567-987653-af456","type":"https://catalog.syniti.com/errors/unsupported-meta-schema-version","title":"Invalid Meta Schema Version","detail":"The provided meta-version in the request is not valid. Use 'relational-20250204'."}}}}}}},"401":{"$ref":"#/components/responses/UnAuthorizedError"},"403":{"$ref":"#/components/responses/ForbiddenError"}}},"delete":{"tags":["DataModel"],"summary":"Delete Data Model","operationId":"deleteDataModel","description":"This is a temporary API that allows synchronous cleanup of the full data model for the given context. It will eventually be changed into an asynchronous process in future.\n","parameters":[{"name":"context_id","in":"path","description":"The unique identifier for the datastore.\n","required":true,"schema":{"type":"string"}}],"responses":{"200":{"description":"Sucessfully deleted.\n","content":{"application/json":{"schema":{"$ref":"#/components/schemas/DeleteDataModelResponse"},"examples":{"example-1":{"summary":"Example of a Data Model Response","value":{"context_id":"dst_222"}}}}}},"400":{"description":"Bad Request.","content":{"application/json":{"schema":{"$ref":"#/components/schemas/ApiError","examples":{"example-1":{"summary":"Example of a Bad Request","value":{"context_id":"dst_111","errors":[{"code":"CONTEXT_ID_NOT_FOUND","message":"There exists no data model for the given context id."}]}}}}}}},"401":{"$ref":"#/components/responses/UnAuthorizedError"},"403":{"$ref":"#/components/responses/ForbiddenError"}}}},"/contexts/{context_id}/datamodel/navigation":{"get":{"tags":["DataModel"],"summary":"Navigate Data Model","operationId":"navigateDataModel","description":"Navigate the data model.\n\n\nEach data model (database schemas, OpenAPI definitions, or OData models) has its own unique structure and semantics for organizing its contents.\nWith this API, you can start at the root of the data model and traverse its nested structure, accessing the contents in their original form.\nThe API provides a consistent interface for navigating the data model, no matter its specific semantics or internal organization.\n\n\nYou begin by providing no parameters and fetching the root contents of the model.\nFrom there, you can traverse along each of the root elements, exploring their nested structures and accessing the data in its original form.\n\n\n**Sample navigation for Database schema**\n\n`/navigate?context_id=dst_1877e07898eb4581ade0b0db4bd98963111`\n- This query returns the root entities which are tables, and views.\n\n`/navigate?context_id=dst_1877e07898eb4581ade0b0db4bd98963111&type=table`\n- This query fetches only root level entities of type 'table'.\n\n`/navigate?context_id=dst_1877e07898eb4581ade0b0db4bd98963111&parent_id=table123`\n- This query fetches all child elements of table with id 'table123'.\n\n`/navigate?context_id=dst_1877e07898eb4581ade0b0db4bd98963111&parent_id=table123&type=column`\n- This query fetches only column child elements of table with id 'table123'. It automatically resolves all references ($ref) and any relationships where column is the source.\n\n`/navigate?context_id=dst_1877e07898eb4581ade0b0db4bd98963111&parent_id=table123&type=primary-key&type=foreign-key&type=index`\n- This query fetches only constraint elements of table with id 'table123'. It automatically resolves all relationships where column is the source.\n\n**Sample navigation for Open API**\n\n`/navigate`\n- This query returns the root entities which are all operations. For e.g. 'addPet'\n\n`/navigate?type=schemas`\n- This query fetches all root level entities which are the schemas defined in the Open API. For e.g. 'Pet'\n\n`/navigate?parent_id=operation1`\n- This query fetches all child elements (parameter, request, response, security) of operation with id 'operation1'.\n\n`/navigate?parent_id=petschema1`\n- This query fetches all child elements of Pet schema with id 'petschema1'. It automatically resolves all references ($ref).\n\n**Sample navigation for ODATA**\n\n`/navigate`\n- This query returns the root entities which are all EntitySet, FunctionImport, ActionImport. For e.g. 'People'.\n\n`/navigate?type=entity_type`\n- This query fetches all root level entities which are the EntityType defined in the ODATA. For e.g. 'Person'. We can also fetch more that one type : enum_type | complex_type | entity_type | function | action.\n\n`/navigate?parent_id=entityset1`\n- This query fetches all child elements (navigation bindings) of EntitySet with id 'entityset1'.\n\n`/navigate?parent_id=entitytype1`\n- This query fetches all child elements (properties) of EntityType with id 'entitytype1'. It automatically resolves all references ($ref).\n\n**Sample navigation for Business Catalog schema**\n\n`/navigate`\n- This query returns the root dataset definitions (UI, QUERY, REPORT) and record type definitions\n\n`/navigate?type=ui`\n- This query fetches only root level dataset definitions of type 'UI'\n\n`/navigate?parent_id=dataset_def_ui_001`\n- This query fetches all child elements of dataset definition with id 'dataset_def_ui_001'.\n\n`/navigate?parent_id=dataset_def_ui_001&type=property`\n- This query fetches only property child elements of dataset definition with id 'dataset_def_ui_001'. It automatically resolves all references.\n","parameters":[{"name":"context_id","description":"The identifier of the context for which the data model needs to be fetched.\nThe context can be a Datastore or a Subject Area. This is a mandatory parameter.\n","in":"path","schema":{"type":"string"},"required":true},{"name":"parent_id","description":"The identifier of the parent under which the child elements needs to be fetched.\nIf not specified, then the root level model elements are fetched.\n","in":"query","schema":{"type":"string"}},{"name":"type","in":"query","description":"Specify the type of contents to be fetched. Its value is specific to each type of data model being explored. \\\n\\\nDatabase: table, view, column, primary-key, index, foreign-key \\\nOpen API: operation, schema, security_scheme \\\nODATA: entity_set, function_import, action_import, entity_type, enum_type, complex_type, function, action\n","schema":{"type":"array","items":{"type":"string"}}},{"name":"first","in":"query","description":"Number of requested results for paging\n","schema":{"type":"integer"}},{"name":"offset","in":"query","description":"Encoded value used to indicate where in the result set to begin.\nThis is used for paging. To fetch the next page of results, use the value returned in the `next_token` field of the previous response.\n","schema":{"type":"string"}},{"name":"sort","in":"query","description":"A base64 encoded sort value","schema":{"type":"string"}}],"responses":{"200":{"description":"Model contents list.","content":{"application/json":{"schema":{"$ref":"#/components/schemas/NavigateResponse"}}}},"400":{"description":"Bad Request","content":{"application/json":{"schema":{"$ref":"#/components/schemas/ApiError"}}}},"401":{"$ref":"#/components/responses/UnAuthorizedError"},"403":{"$ref":"#/components/responses/ForbiddenError"}}}},"/contexts/{context_id}/datamodel/entities":{"get":{"tags":["Entity"],"summary":"Retrieve a List of Entities","operationId":"findEntities","description":"The API endpoint allows you to fetch the data model entities.\nThe API supports pagination and filtering options to manage large results effectively.\n","parameters":[{"name":"context_id","description":"Filter entities by the context like a datastore or subject areas identifier.\n","in":"path","schema":{"type":"string"}},{"name":"type","in":"query","description":"Filter by the type of entity.\nFor example `TABLE`.\nPlease review the meta-meta information for the data model for the valid types.\n","schema":{"type":"array","items":{"type":"string"}}},{"name":"consumption_type","in":"query","description":"Filter by the consumption_type of entity.\nFor example `table`.\n","schema":{"type":"array","items":{"type":"string"}}},{"name":"tags","in":"query","description":"Filter by the tags of entity.\n","schema":{"type":"array","items":{"type":"string"}}},{"name":"name","description":"Filter entities by the matching exact name.\n","in":"query","schema":{"type":"string"}},{"name":"name_text","description":"Filter entities using contains text search on the name of the entity.\n","in":"query","schema":{"type":"string"}},{"name":"description_text","description":"Filter entities using contains text search on the description of the entity.\n","in":"query","schema":{"type":"string"}},{"name":"first","in":"query","description":"Number of requested results for paging","schema":{"type":"integer"}},{"name":"offset","in":"query","description":"Encoded value used to indicate where in the result set to begin","schema":{"type":"string"}},{"name":"sort","in":"query","description":"A base64 encoded sort value","schema":{"type":"string"}},{"name":"filter","in":"query","description":"A base64 encoded filter value","schema":{"type":"string"}}],"responses":{"200":{"description":"Entities list.","content":{"application/json":{"schema":{"type":"object","properties":{"next_token":{"type":"string","description":"The token to fetch the next set of results.\n"},"items":{"description":"The list of entities.\nThe list is paginated and the next_token can be used to fetch the next set of results.\n","type":"array","items":{"$ref":"#/components/schemas/Entity"}}}}}}},"400":{"description":"Bad Request","content":{"application/json":{"schema":{"$ref":"#/components/schemas/ApiError"}}}},"401":{"$ref":"#/components/responses/UnAuthorizedError"},"403":{"$ref":"#/components/responses/ForbiddenError"}}}},"/contexts/{context_id}/datamodel/elements":{"get":{"tags":["EntityElement"],"summary":"Retrieve a List of Entity Elements","operationId":"findElements","description":"The API endpoint allows you to fetch the data model entity elements.\nThe API supports pagination and filtering options to manage large results effectively.\n","parameters":[{"name":"entity_id","description":"Fetch elements for the parent entity.\n","in":"path","required":true,"schema":{"type":"string"}},{"name":"type","in":"query","description":"Filter by the type of entity element.\nFor example `COLUMN`.\nPlease review the meta-meta information for the data model for the valid types.\n","schema":{"type":"array","items":{"type":"string"}}},{"name":"consumption_type","in":"query","description":"Filter by the consumption_type of entity element.\nFor example `columns`.\n","schema":{"type":"array","items":{"type":"string"}}},{"name":"tags","in":"query","description":"Filter by the tags of entity element.\n","schema":{"type":"array","items":{"type":"string"}}},{"name":"name","description":"Filter entity elements by the matching exact name.\n","in":"query","schema":{"type":"string"}},{"name":"name_text","description":"Filter entity elements using contains text search on the name of the entity.\n","in":"query","schema":{"type":"string"}},{"name":"description_text","description":"Filter entity elements using contains text search on the description of the entity.\n","in":"query","schema":{"type":"string"}},{"name":"first","in":"query","description":"Number of requested results for paging","schema":{"type":"integer"}},{"name":"offset","in":"query","description":"Encoded value used to indicate where in the result set to begin","schema":{"type":"string"}},{"name":"sort","in":"query","description":"A base64 encoded sort value","schema":{"type":"string"}},{"name":"filter","in":"query","description":"A base64 encoded filter value","schema":{"type":"string"}}],"responses":{"200":{"description":"Entity Elements list.","content":{"application/json":{"schema":{"type":"object","properties":{"next_token":{"type":"string","description":"The token to fetch the next set of results.\n"},"items":{"description":"The list of entity elements.\nThe list is paginated and the next_token can be used to fetch the next set of results.\n","type":"array","items":{"$ref":"#/components/schemas/EntityElement"}}}}}}},"400":{"description":"Bad Request","content":{"application/json":{"schema":{"$ref":"#/components/schemas/ApiError"}}}},"401":{"$ref":"#/components/responses/UnAuthorizedError"},"403":{"$ref":"#/components/responses/ForbiddenError"}}}},"/datamodel-import":{"post":{"tags":["Import DataModel"],"summary":"Import Data Model","operationId":"importDataModel","description":"The API enables batch import of data models, accommodating scenarios where the MetaScanner scans technical data models with thousands of tables and columns.\n\n\n**How It Works**\n\n1. Logical Transaction Identifier:  \n  The entire import process must be handled as a single transaction to ensure the local data model is correctly updated with the scanned data model.\n  A `scan_id` is generated as part of a SystemScan entry in the Knowledge Graph.\n  This `scan_id` ensures that all batches are imported as part of the same transaction.\n2. Batch Import:  \n  Use the import API with the `scan_id` to import all batches of the data model.\n3. Mark Import as Completed:  \n  Once all batches are imported, invoke the API to mark the transaction as complete.\n\n**Key Points for Consumers**\n\n- Idempotent: The import process is idempotent, ensuring that re-importing the same batch does not create duplicate data.\n- `scan_id`: The `scan_id` is critical for maintaining transactional integrity. It must be provided consistently for all batch imports.\n- Single Import: Only one batch import is allowed at a time for a specific data model context.\n","parameters":[{"name":"context_id","description":"The identifier of the context for which the data model needs to be imported.\nThe context can be a Datastore or a Subject Area. This is a mandatory parameter.\n","in":"path","schema":{"type":"string"},"required":true},{"name":"scan_id","in":"path","description":"The unique identifier for the scan.\nThis identifier is used to track the import process and ensure that all batches are part of the same transaction.\nThe `scan_id` is generated as part of a SystemScan entry in the Knowledge Graph.\nIt is critical for maintaining transactional integrity and must be provided consistently for all batch imports.\n","required":true,"schema":{"type":"string"}}],"requestBody":{"content":{"application/json":{"schema":{"$ref":"#/components/schemas/ImportDataModelRequest"},"examples":{"example-1":{"summary":"Example of importing a data model","value":{"data_model":[{"model_element_type":"entity","model_name":"ns1.Product","name":"Product","type":"TABLE","consumption_type":"dataset","active":true,"properties":{"schema":"Production"}},{"model_element_type":"element","entity":"ns1.Product","model_name":"ns1.Product.Shipmentid","name":"ShipmentId","type":"COLUMN","consumption_type":"field","active":true,"index":1,"data_type":"int","default_value":"","nulls_allowed":false},{"model_element_type":"element","entity":"ns1.Product","model_name":"ns1.Production.Product.PK_Shipmentid","name":"PK_ShipmentId","type":"PRIMARY-KEY","consumption_type":"key"},{"model_element_type":"relation","type":"PK_REF","description":"","source":"#element/ns1.Product.PK_Shipmentid","target":"#element/ns1.Product.Shipmentid"},{"model_element_type":"entity","operation":"delete","name":"Employee"}]}}}}}},"responses":{"200":{"description":"Sucessfully imported.\n","content":{"application/json":{"schema":{"$ref":"#/components/schemas/ImportDataModelResponse"},"examples":{"example-1":{"summary":"Example of a Data Model Response","value":{"new_model_version":"20250201-002"}}}}}},"400":{"description":"Bad Request.","content":{"application/json":{"schema":{"$ref":"#/components/schemas/ApiError","examples":{"example-1":{"summary":"Example of a Bad Request","value":{"request_id":"4567-987653-af456","errors":[{"code":"UNSUPPORTED_META_VERSION","message":"The provided meta-version in the request is not valid. Use 'relational-20250204'."}]}}}}}}},"401":{"$ref":"#/components/responses/UnAuthorizedError"},"403":{"$ref":"#/components/responses/ForbiddenError"}}}}},"components":{"parameters":{"limit":{"name":"limit","in":"query","description":"How many items to return at one time (max 100)","required":false,"schema":{"type":"integer","format":"int32"}}},"schemas":{"EntityElement":{"type":"object","description":"Represents the characteristics or parts that make up an entity (e.g., `Columns` in a Table).\n","required":["name","type","consumption_type"],"properties":{"id":{"type":"string","description":"The unique identifier of the element"},"model_name":{"type":"string","description":"The canonical name of the entity element.\nIt is used to identify the entity element in the context of the data model and for cross references.\n"},"name":{"type":"string","description":"The name of the entity element.\nIt should be unique within the Entity.\nThe name should follow the naming conventions and should not contain any special characters or spaces.\n"},"description":{"type":"string","description":"A description of the element."},"type":{"type":"string","description":"The actual source type of element.\nIt can be a Column, Primary Key, or any other object that is relevant to the data model.\nPlease refer the meta_schema_version documentation for the list of valid entity types.\n"},"consumption_type":{"type":"string","description":"The normalized type of element as consumed by various SKP applications.\nPlease refer the meta_schema_version documentation for the list of valid entity element types.\n"},"index":{"type":"integer","description":"Indicates the ordinal position of this element.\nDefault is 0.\n"},"data_type":{"type":"string","description":"The underlying native data type associated with this element.\nIt can be a string, integer, float, or any other data type that is relevant to the data model.\nPlease refer the meta_schema_version documentation for the list of valid data types.\n"},"default_value":{"type":"string","description":"The default value to be used for this element\n"},"enum":{"type":"array","items":{"type":"string"},"description":"A list of possible values for the element.\nThis is used to restrict the values that can be assigned to the element.\n"},"is_required":{"type":"boolean","description":"Indicates if the element is a required property.\nDefault is false.\n"},"is_array":{"type":"boolean","description":"Indicates if the element is an array.\nDefault is false.\n"},"max_length":{"type":"integer","description":"The maximum length of the element.\nThis is used to specify the maximum number of characters that can be stored in the element.\n"},"precision":{"type":"integer","description":"The precision of the element.\nThis is used to specify the number of digits that can be stored in the element.\n"},"scale":{"type":"integer","description":"The scale of the element.\nThis is used to specify the number of digits that can be stored in the element after the decimal point.\n"},"properties":{"type":"object","description":"Type specific properties.\nA map of key value pairs that captures the properties specific to the element type.\nFor example, if the type=INDEX, then we capture `is_clustered` key value.\nPlease see the meta_schema_version documentation for the list of valid keys.\n"},"custom_properties":{"type":"object","description":"Custom properties for the entity element. `Check with John if this is required`.`\nA map of key value pairs that captures the custom properties specific to the element.\nThe keys are defined by the consumer and can be used to capture any additional information about the element.\n"},"tags":{"type":"array","items":{"type":"string"},"description":"A list of tags associated with the entity element.\nTags can be used to categorize or label the element for easier identification and management.\nTags can be used for filtering and searching elements within the data model.\n"},"created_by":{"type":"string","description":"The user who created the entity element.\nThis is usually a system generated value and is used for auditing purposes.\n"}}},"Entity":{"type":"object","description":"Each entity represents a distinct object or concept within the data model.\nThe entity can be a table, view, or any other object that is relevant to the data model.\nEach entity has a unique name and can have multiple entity elements (fields) associated with it.\n","required":["name","type","consumption_type"],"properties":{"id":{"type":"string","description":"The unique identifier of the Entity"},"model_name":{"type":"string","description":"The canonical name of the entity.\nIt is used to identify the entity in the context of the data model and for cross references.\n"},"name":{"type":"string","description":"The name of the entity.\nIt should be unique within the context of the data model.\nThe name should follow the naming conventions and should not contain any special characters or spaces.\n"},"description":{"type":"string","description":"A description of the entity."},"type":{"type":"string","description":"The actual source type of entity.\nIt can be a table, view, or any other object that is relevant to the data model.\nPlease refer the meta_schema_version documentation for the list of valid entity types.\n"},"consumption_type":{"type":"string","description":"The normalized type of entity as consumed by various SKP applications.\nPlease refer the meta_schema_version documentation for the list of valid entity types.\n"},"index":{"type":"integer","description":"Indicates the ordinal position of this entity.\nDefault is 0.\n"},"properties":{"type":"object","description":"Type specific properties.\nA map of key value pairs that captures the properties specific to the entity type.\nFor example, if the type=TABLE, then we capture `schema` key value.\nPlease see the meta_schema_version documentation for the list of valid keys.\n"},"custom_properties":{"type":"object","description":"Custom properties for the entity.\nA map of key value pairs that captures the custom properties specific to the entity.\nThe keys are defined by the consumer and can be used to capture any additional information about the entity.\n"},"tags":{"type":"array","items":{"type":"string"},"description":"A list of tags associated with the entity.\nTags can be used to categorize or label the entity for easier identification and management.\nTags can be used for filtering and searching entities within the data model.\n"},"created_by":{"type":"string","description":"The user who created the entity.\nThis is usually a system generated value and is used for auditing purposes.\n"},"entity_elements":{"type":"array","items":{"$ref":"#/components/schemas/EntityElement"},"description":"The list of entity elements (fields) associated with the entity.\nFor example, in case of relation database model, a database Column is represented as an entity element.\nEach entity element has a unique name.\n"}}},"ModelElementRef":{"type":"object","description":"An object representing a component that requires metadata scanning.","properties":{"id":{"type":"string","description":"unique identifier of the Component","example":"cpt_xkHRtSJrPh4JLgeZQLfA7VdLsbi"},"$ref":{"type":"string","description":"the"}}},"Relationship":{"type":"object","description":"A relationship represents a connection between entities or entity elements\n","required":["type","source","target"],"properties":{"id":{"type":"string","description":"The unique identifier of the Relationship"},"name":{"type":"string","description":"The name of the relationship.\nIt should be unique within the context of the data model.\nThe name should follow the naming conventions and should not contain any special characters or spaces.\n"},"description":{"type":"string","description":"A description of the relationship.\nThis should provide additional context or information about the relationship.\n"},"type":{"type":"string","description":"The type of relationship.\nPlease the meta_schema_version documentation for the list of valid relationship types.\n"},"index":{"type":"integer","description":"Indicates the ordinal position of this relationship.\nDefault is 0.\n"},"source":{"$ref":"#/components/schemas/ModelElementRef","description":"The source of the relationship.\nIt can be an entity or an entity element.\nThe source is the starting point of the relationship and is used to identify the origin of the connection.\n"},"target":{"$ref":"#/components/schemas/ModelElementRef","description":"The target of the relationship.\nIt can be an entity or an entity element.\nThe target is the endpoint of the relationship and is used to identify the destination of the connection.\n"},"properties":{"type":"object","description":"Type specific properties.\nA map of key value pairs that captures the properties specific to the relationship.\n"},"tags":{"type":"array","items":{"type":"string"},"description":"A list of tags associated with the relationship.\nTags can be used to categorize or label the relationship for easier identification and management.\nTags can be used for filtering and searching relationships within the data model.\n"},"created_by":{"type":"string","description":"The user who created the relationship.\nThis is usually a system generated value and is used for auditing purposes.\n"}}},"DataModelRequest":{"type":"object","required":["meta_schema_version"],"properties":{"meta_schema_version":{"type":"string","description":"The meta-meta schema version.\nIt controls the rules and structure of the data model, including the allowed fields, their types, and other constraints\nThe value of this field ensures that the system understands how to interpret the data and which validation rules to apply.\nFor example, the version `relational-20250501` informs the system that the data model defined in the current API is representing the database schama.\nHence the system can validate that the allowed entity types are only TABLE and VIEW.\n"},"entities":{"type":"array","items":{"$ref":"#/components/schemas/Entity"},"description":"The list of entities in the data model.\nEach entity represents a distinct object or concept within the data model.\nThe entity can be a table, view, or any other object that is relevant to the data model.\nEach entity has a unique name and can have multiple entity elements (fields) associated with it.\n"},"relationships":{"type":"array","items":{"$ref":"#/components/schemas/Relationship"},"description":"The list of relationships in the data model.\nEach relationship represents a connection between entities or entity elements.\n"}}},"DataModelResponse":{"type":"object","description":"The DataModelResponse schema represents the response structure for a data model request.\nIt contains metadata about the data model, including its new version.\n","required":["new_version"],"properties":{"new_version":{"type":"string","description":"The new version of the data model.\n"}}},"ErrorDetail":{"type":"object","description":"Error detail information","required":["message"],"properties":{"message":{"description":"Specific error message.","type":"string"},"field":{"description":"The field that caused the error (if applicable).","type":"string"}},"example":{"field":"name","message":"name is required."}},"ApiError":{"type":"object","description":"API error response","required":["trace_id","type","title"],"properties":{"trace_id":{"description":"The unique identifier associated with the API request to assist with troubleshooting.","type":"string"},"type":{"description":"A URI reference [URI] that identifies the error type.","type":"string"},"status":{"description":"The HTTP status code associated with the error.","type":"int"},"title":{"description":"A short, human-readable summary of the error type.","type":"string"},"detail":{"description":"A human-readable explanation specific to this occurrence of the error.","type":"string"},"message":{"description":"Duplicate of detail field for backward compatibility.","type":"string"},"errors":{"description":"An optional list of individual errors when multiple errors occur.","type":"array","items":{"$ref":"#/components/schemas/ErrorDetail"}}},"example":{"trace_id":"123e4567-e89b-12d3-a456-426614174000","type":"https://catalog.syniti.com/errors/validation-error","status":400,"title":"Validation Error","detail":"One or more validation errors occurred. Errors: name: [name is required], age: [must be a positive integer]","errors":[{"field":"name","message":"name is required."},{"field":"age","message":"must be a positive integer."}]}},"DeleteDataModelResponse":{"type":"object","properties":{"results":{"type":"object","properties":{"context_id":{"type":"string"},"entities_deleted":{"type":"integer","description":"Indicates the number of entities deleted.\n"},"elements_deleted":{"type":"integer","description":"Indicates the number of elements deleted.\n"},"relationships_deleted":{"type":"integer","description":"Indicates the number of relationships deleted.\n"},"versions_deleted":{"type":"integer","description":"Indicates the number of versions deleted.\n"}}}}},"NavigateResponse":{"type":"object","properties":{"results":{"type":"object","properties":{"entities":{"type":"array","items":{"$ref":"#/components/schemas/Entity"},"description":"The list of entities found in the data model.\n"},"entity_elements":{"type":"array","items":{"$ref":"#/components/schemas/EntityElement"},"description":"The list of entity elements found in the data model.\n"},"relationships":{"type":"array","items":{"$ref":"#/components/schemas/Relationship"},"description":"The list of relationships found in the data model.\n"}}},"next_token":{"type":"string","description":"The token to be used to fetch the next page of results. This is a base64 encoded value.\nIf there are no more results, this field will not be present in the response.\n"}}},"ImportDataModelRequest":{"type":"object","properties":{"entities":{"type":"array","items":{"$ref":"#/components/schemas/Entity"},"description":"The list of entities in the data model.\nEach entity represents a distinct object or concept within the data model.\nThe entity can be a table, view, or any other object that is relevant to the data model.\nEach entity has a unique name and can have multiple entity elements (fields) associated with it.\n"},"relationships":{"type":"array","items":{"$ref":"#/components/schemas/Relationship"},"description":"The list of relationships in the data model.\nEach relationship represents a connection between entities or entity elements.\n"}}},"ImportDataModelResponse":{"type":"object","description":"The DataModelResponse schema represents the response structure for a data model request.\nIt contains metadata about the data model, including its new version.\n","required":["new_version"],"properties":{"new_version":{"type":"string","description":"The new version of the data model.\n"}}},"CatalogError":{"$ref":"#/components/schemas/ApiError"}},"securitySchemes":{"bearerAuth":{"description":"JWT token for authentication.\nThe token should be passed in the `Authorization` header as a Bearer token.\n","type":"http","scheme":"bearer","bearerFormat":"JWT"}},"responses":{"UnAuthorizedError":{"description":"Authentication information is expired, missing, or invalid.\nEnsure that you have authenticated correctly.\n"},"ForbiddenError":{"description":"Authorization failed or access denied error.\nEnsure that you have the necessary permissions.\n"}}},"x-tagGroups":[{"name":"API Reference","tags":["DataModel","Entity","EntityElement","Import DataModel"]},{"name":"Models","tags":["Schemas"]}]}},"searchIndex":{"store":["tag/DataModel","tag/DataModel/operation/createDataModel","tag/DataModel/operation/deleteDataModel","tag/DataModel/operation/navigateDataModel","tag/Entity","tag/Entity/operation/findEntities","tag/EntityElement","tag/EntityElement/operation/findElements","tag/Import-DataModel","tag/Import-DataModel/operation/importDataModel","tag/Schemas","schema/EntityElement","schema/Entity","schema/ModelElementRef","schema/Relationship","schema/DataModelRequest","schema/DataModelResponse","schema/ErrorDetail","schema/ApiError","schema/DeleteDataModelResponse","schema/NavigateResponse","schema/ImportDataModelRequest","schema/ImportDataModelResponse","schema/CatalogError"],"index":{"version":"2.3.9","fields":["title","description"],"fieldVectors":[["title/0",[0,2.711]],["description/0",[1,1.091,2,1.091,3,2.384]],["title/1",[1,0.507,2,0.507,4,1.439]],["description/1",[1,0.824,2,0.824,4,1.602,5,0.486,6,0.546,7,2.292,8,1.427,9,1.744,10,1.744,11,0.83,12,0.728,13,1.015,14,1.876,15,1.015,16,2.721,17,1.015,18,1.015,19,3.346,20,1.427,21,2.738,22,2.292,23,1.015,24,1.015,25,1.427,26,1.744,27,1.015,28,1.015,29,0.83,30,1.015,31,0.83,32,0.83,33,0.83,34,1.015,35,1.015,36,1.015,37,1.015,38,1.015,39,0.83,40,1.015,41,1.015,42,1.015,43,1.015,44,1.015,45,1.876,46,1.015,47,0.83,48,1.015,49,0.83,50,1.015,51,0.709,52,1.602,53,0.83,54,2.721,55,1.015,56,1.015,57,3.346,58,1.151,59,1.218,60,2.227,61,0.938,62,1.015,63,1.015,64,1.015,65,1.015,66,1.015,67,1.015,68,1.015,69,1.015,70,1.015,71,0.83]],["title/2",[1,0.507,2,0.507,72,2.06]],["description/2",[1,0.838,2,0.838,5,1.629,6,1.831,20,2.785,29,2.785,52,2.378,71,2.785,73,3.403,74,3.403,75,3.403,76,3.403,77,3.403,78,3.403,79,2.785,80,3.403]],["title/3",[1,0.507,2,0.507,81,1.686]],["description/3",[1,0.516,2,0.58,3,0.566,5,0.861,12,0.633,39,0.861,49,0.47,53,0.861,58,0.81,59,0.735,60,0.47,61,1.685,81,2.562,82,1.052,83,1.715,84,0.575,85,2.355,86,1.454,87,1.454,88,1.052,89,1.052,90,1.454,91,0.575,92,3.278,93,1.052,94,1.052,95,1.052,96,1.052,97,1.052,98,0.861,99,0.47,100,0.575,101,0.575,102,0.47,103,0.575,104,0.575,105,1.052,106,2.619,107,0.575,108,0.575,109,1.798,110,0.575,111,4.012,112,1.798,113,1.715,114,0.575,115,0.575,116,1.798,117,1.798,118,0.575,119,2.786,120,2.968,121,1.454,122,0.575,123,1.19,124,2.095,125,2.095,126,1.798,127,1.454,128,1.052,129,0.575,130,0.575,131,1.052,132,1.798,133,0.575,134,0.575,135,1.052,136,0.575,137,0.575,138,0.575,139,0.575,140,0.575,141,0.575,142,1.052,143,0.575,144,0.575,145,0.575,146,0.575,147,1.052,148,0.575,149,0.575,150,0.47,151,0.575,152,0.575,153,0.575,154,0.575,155,0.575,156,0.575,157,0.575,158,0.575,159,0.575,160,1.052,161,0.575,162,0.575,163,0.575,164,1.798,165,1.052,166,0.575,167,0.575,168,0.575,169,0.575,170,1.052,171,0.575,172,0.575]],["title/4",[58,1.139]],["description/4",[3,2.441,58,1.56]],["title/5",[58,0.708,173,1.686,174,1.686]],["description/5",[1,0.824,2,0.824,5,2.079,6,1.799,51,2.336,58,1.15,106,2.336,175,2.736,176,2.736,177,2.736,178,2.736,179,2.736,180,2.736,181,2.736,182,2.736,183,3.343]],["title/6",[184,2.711]],["description/6",[3,2.441,61,2.441]],["title/7",[58,0.596,61,0.932,173,1.417,174,1.417]],["description/7",[1,0.81,2,0.81,5,2.055,6,1.768,51,2.296,58,1.13,61,1.768,106,2.296,175,2.689,176,2.689,177,2.689,178,2.689,179,2.689,180,2.689,181,2.689,182,2.689,185,3.286]],["title/8",[0,2.079,186,1.548]],["description/8",[1,1.067,2,1.067,3,2.33,186,2.639]],["title/9",[1,0.507,2,0.507,186,1.255]],["description/9",[1,0.976,2,0.921,4,0.877,5,1.282,6,0.675,8,1.708,11,1.027,12,0.396,14,1.027,21,1.027,25,1.027,31,1.027,32,1.027,33,1.027,45,1.708,47,1.708,52,0.877,79,1.708,98,1.027,99,1.027,102,1.027,113,1.027,123,1.027,150,1.027,186,2.841,187,1.255,188,4.15,189,1.255,190,1.255,191,1.255,192,2.087,193,1.255,194,1.255,195,1.255,196,1.255,197,3.465,198,1.255,199,1.255,200,2.087,201,2.678,202,1.255,203,1.255,204,3.465,205,1.255,206,2.087,207,1.255,208,1.255,209,1.255,210,1.255,211,1.255,212,1.255,213,2.087,214,1.255,215,1.255,216,1.255,217,1.255,218,1.255,219,1.255,220,1.255,221,1.255,222,1.255]],["title/10",[83,2.711]],["description/10",[]],["title/11",[184,2.711]],["description/11",[12,0.639,223,0.948,224,0.948,225,4.329]],["title/12",[58,1.139]],["description/12",[12,0.639,223,0.948,224,0.948,226,4.329]],["title/13",[227,3.313]],["description/13",[12,0.639,223,0.948,224,0.948,228,4.329]],["title/14",[59,2.315]],["description/14",[12,0.639,223,0.948,224,0.948,229,4.329]],["title/15",[230,3.313]],["description/15",[12,0.639,223,0.948,224,0.948,231,4.329]],["title/16",[232,3.313]],["description/16",[12,0.639,223,0.948,224,0.948,233,4.329]],["title/17",[234,3.313]],["description/17",[12,0.639,223,0.948,224,0.948,235,4.329]],["title/18",[236,3.313]],["description/18",[12,0.639,223,0.948,224,0.948,237,4.329]],["title/19",[238,3.313]],["description/19",[12,0.639,223,0.948,224,0.948,239,4.329]],["title/20",[240,3.313]],["description/20",[12,0.639,223,0.948,224,0.948,241,4.329]],["title/21",[242,3.313]],["description/21",[12,0.639,223,0.948,224,0.948,243,4.329]],["title/22",[244,3.313]],["description/22",[12,0.639,223,0.948,224,0.948,245,4.329]],["title/23",[246,3.313]],["description/23",[12,0.639,223,0.948,224,0.948,247,4.329]]],"invertedIndex":[["",{"_index":12,"title":{},"description":{"1":{},"3":{},"9":{},"11":{},"12":{},"13":{},"14":{},"15":{},"16":{},"17":{},"18":{},"19":{},"20":{},"21":{},"22":{},"23":{}}}],["1",{"_index":195,"title":{},"description":{"9":{}}}],["2",{"_index":211,"title":{},"description":{"9":{}}}],["3",{"_index":212,"title":{},"description":{"9":{}}}],["access",{"_index":95,"title":{},"description":{"3":{}}}],["accommod",{"_index":189,"title":{},"description":{"9":{}}}],["action",{"_index":155,"title":{},"description":{"3":{}}}],["actionimport",{"_index":144,"title":{},"description":{"3":{}}}],["addpet",{"_index":133,"title":{},"description":{"3":{}}}],["allow",{"_index":6,"title":{},"description":{"1":{},"2":{},"5":{},"7":{},"9":{}}}],["along",{"_index":107,"title":{},"description":{"3":{}}}],["alphanumer",{"_index":64,"title":{},"description":{"1":{}}}],["api",{"_index":5,"title":{},"description":{"1":{},"2":{},"3":{},"5":{},"7":{},"9":{}}}],["apierror",{"_index":236,"title":{"18":{}},"description":{}}],["asynchron",{"_index":78,"title":{},"description":{"2":{}}}],["authorit",{"_index":9,"title":{},"description":{"1":{}}}],["automat",{"_index":124,"title":{},"description":{"3":{}}}],["avoid",{"_index":42,"title":{},"description":{"1":{}}}],["batch",{"_index":188,"title":{},"description":{"9":{}}}],["begin",{"_index":104,"title":{},"description":{"3":{}}}],["bind",{"_index":157,"title":{},"description":{"3":{}}}],["busi",{"_index":162,"title":{},"description":{"3":{}}}],["case",{"_index":66,"title":{},"description":{"1":{}}}],["catalog",{"_index":163,"title":{},"description":{"3":{}}}],["catalogerror",{"_index":246,"title":{"23":{}},"description":{}}],["chang",{"_index":29,"title":{},"description":{"1":{},"2":{}}}],["charact",{"_index":65,"title":{},"description":{"1":{}}}],["child",{"_index":119,"title":{},"description":{"3":{}}}],["cleanup",{"_index":75,"title":{},"description":{"2":{}}}],["column",{"_index":123,"title":{},"description":{"3":{},"9":{}}}],["complet",{"_index":8,"title":{},"description":{"1":{},"9":{}}}],["complex_typ",{"_index":152,"title":{},"description":{"3":{}}}],["conflict",{"_index":43,"title":{},"description":{"1":{}}}],["consist",{"_index":99,"title":{},"description":{"3":{},"9":{}}}],["constraint",{"_index":130,"title":{},"description":{"3":{}}}],["consum",{"_index":33,"title":{},"description":{"1":{},"9":{}}}],["contain",{"_index":63,"title":{},"description":{"1":{}}}],["content",{"_index":90,"title":{},"description":{"3":{}}}],["context",{"_index":52,"title":{},"description":{"1":{},"2":{},"9":{}}}],["contexts/{context_id}/datamodel",{"_index":71,"title":{},"description":{"1":{},"2":{}}}],["contexts/{context_id}/datamodel/el",{"_index":185,"title":{},"description":{"7":{}}}],["contexts/{context_id}/datamodel/ent",{"_index":183,"title":{},"description":{"5":{}}}],["contexts/{context_id}/datamodel/navig",{"_index":172,"title":{},"description":{"3":{}}}],["correctli",{"_index":203,"title":{},"description":{"9":{}}}],["creat",{"_index":4,"title":{"1":{}},"description":{"1":{},"9":{}}}],["creation",{"_index":13,"title":{},"description":{"1":{}}}],["critic",{"_index":218,"title":{},"description":{"9":{}}}],["current",{"_index":41,"title":{},"description":{"1":{}}}],["data",{"_index":1,"title":{"1":{},"2":{},"3":{},"9":{}},"description":{"0":{},"1":{},"2":{},"3":{},"5":{},"7":{},"8":{},"9":{}}}],["databas",{"_index":82,"title":{},"description":{"3":{}}}],["datamodel",{"_index":0,"title":{"0":{},"8":{}},"description":{}}],["datamodel-import",{"_index":222,"title":{},"description":{"9":{}}}],["datamodelrequest",{"_index":230,"title":{"15":{}},"description":{}}],["datamodelrespons",{"_index":232,"title":{"16":{}},"description":{}}],["dataset",{"_index":164,"title":{},"description":{"3":{}}}],["dataset_def_ui_001",{"_index":170,"title":{},"description":{"3":{}}}],["datastor",{"_index":55,"title":{},"description":{"1":{}}}],["defin",{"_index":53,"title":{},"description":{"1":{},"3":{}}}],["definit",{"_index":85,"title":{},"description":{"3":{}}}],["delet",{"_index":72,"title":{"2":{}},"description":{}}],["deletedatamodelrespons",{"_index":238,"title":{"19":{}},"description":{}}],["duplic",{"_index":217,"title":{},"description":{"9":{}}}],["e.g",{"_index":132,"title":{},"description":{"3":{}}}],["each",{"_index":39,"title":{},"description":{"1":{},"3":{}}}],["effect",{"_index":182,"title":{},"description":{"5":{},"7":{}}}],["element",{"_index":61,"title":{"7":{}},"description":{"1":{},"3":{},"6":{},"7":{}}}],["enabl",{"_index":187,"title":{},"description":{"9":{}}}],["endpoint",{"_index":175,"title":{},"description":{"5":{},"7":{}}}],["ensur",{"_index":201,"title":{},"description":{"9":{}}}],["entir",{"_index":25,"title":{},"description":{"1":{},"9":{}}}],["entiti",{"_index":58,"title":{"4":{},"5":{},"7":{},"12":{}},"description":{"1":{},"3":{},"4":{},"5":{},"7":{}}}],["entity_typ",{"_index":153,"title":{},"description":{"3":{}}}],["entityel",{"_index":184,"title":{"6":{},"11":{}},"description":{}}],["entityset",{"_index":142,"title":{},"description":{"3":{}}}],["entityset1",{"_index":158,"title":{},"description":{"3":{}}}],["entitytyp",{"_index":147,"title":{},"description":{"3":{}}}],["entitytype1",{"_index":161,"title":{},"description":{"3":{}}}],["entri",{"_index":208,"title":{},"description":{"9":{}}}],["enum_typ",{"_index":151,"title":{},"description":{"3":{}}}],["errordetail",{"_index":234,"title":{"17":{}},"description":{}}],["eventu",{"_index":77,"title":{},"description":{"2":{}}}],["exist",{"_index":24,"title":{},"description":{"1":{}}}],["explor",{"_index":108,"title":{},"description":{"3":{}}}],["fetch",{"_index":106,"title":{},"description":{"3":{},"5":{},"7":{}}}],["filter",{"_index":178,"title":{},"description":{"5":{},"7":{}}}],["form",{"_index":97,"title":{},"description":{"3":{}}}],["full",{"_index":20,"title":{},"description":{"1":{},"2":{}}}],["function",{"_index":154,"title":{},"description":{"3":{}}}],["functionimport",{"_index":143,"title":{},"description":{"3":{}}}],["futur",{"_index":80,"title":{},"description":{"2":{}}}],["gener",{"_index":205,"title":{},"description":{"9":{}}}],["given",{"_index":76,"title":{},"description":{"2":{}}}],["graph",{"_index":210,"title":{},"description":{"9":{}}}],["handl",{"_index":199,"title":{},"description":{"9":{}}}],["hyphen",{"_index":69,"title":{},"description":{"1":{}}}],["id",{"_index":120,"title":{},"description":{"3":{}}}],["idempot",{"_index":47,"title":{},"description":{"1":{},"9":{}}}],["identifi",{"_index":198,"title":{},"description":{"9":{}}}],["import",{"_index":186,"title":{"8":{},"9":{}},"description":{"8":{},"9":{}}}],["importdatamodelrequest",{"_index":242,"title":{"21":{}},"description":{}}],["importdatamodelrespons",{"_index":244,"title":{"22":{}},"description":{}}],["includ",{"_index":40,"title":{},"description":{"1":{}}}],["insenst",{"_index":67,"title":{},"description":{"1":{}}}],["integr",{"_index":220,"title":{},"description":{"9":{}}}],["interfac",{"_index":100,"title":{},"description":{"3":{}}}],["intern",{"_index":103,"title":{},"description":{"3":{}}}],["invok",{"_index":215,"title":{},"description":{"9":{}}}],["key",{"_index":31,"title":{},"description":{"1":{},"9":{}}}],["knowledg",{"_index":209,"title":{},"description":{"9":{}}}],["larg",{"_index":181,"title":{},"description":{"5":{},"7":{}}}],["level",{"_index":116,"title":{},"description":{"3":{}}}],["list",{"_index":174,"title":{"5":{},"7":{}},"description":{}}],["local",{"_index":202,"title":{},"description":{"9":{}}}],["logic",{"_index":196,"title":{},"description":{"9":{}}}],["made",{"_index":30,"title":{},"description":{"1":{}}}],["maintain",{"_index":219,"title":{},"description":{"9":{}}}],["manag",{"_index":180,"title":{},"description":{"5":{},"7":{}}}],["mark",{"_index":213,"title":{},"description":{"9":{}}}],["matter",{"_index":101,"title":{},"description":{"3":{}}}],["metascann",{"_index":191,"title":{},"description":{"9":{}}}],["method",{"_index":16,"title":{},"description":{"1":{}}}],["miss",{"_index":35,"title":{},"description":{"1":{}}}],["model",{"_index":2,"title":{"1":{},"2":{},"3":{},"9":{}},"description":{"0":{},"1":{},"2":{},"3":{},"5":{},"7":{},"8":{},"9":{}}}],["modelelementref",{"_index":227,"title":{"13":{}},"description":{}}],["more",{"_index":149,"title":{},"description":{"3":{}}}],["name",{"_index":57,"title":{},"description":{"1":{}}}],["namespac",{"_index":70,"title":{},"description":{"1":{}}}],["navig",{"_index":81,"title":{"3":{}},"description":{"3":{}}}],["navigate?context_id=dst_1877e07898eb4581ade0b0db4bd98963111",{"_index":110,"title":{},"description":{"3":{}}}],["navigate?context_id=dst_1877e07898eb4581ade0b0db4bd98963111&parent_id=table123",{"_index":118,"title":{},"description":{"3":{}}}],["navigate?context_id=dst_1877e07898eb4581ade0b0db4bd98963111&parent_id=table123&type=column",{"_index":122,"title":{},"description":{"3":{}}}],["navigate?context_id=dst_1877e07898eb4581ade0b0db4bd98963111&parent_id=table123&type=primary-key&type=foreign-key&type=index",{"_index":129,"title":{},"description":{"3":{}}}],["navigate?context_id=dst_1877e07898eb4581ade0b0db4bd98963111&type=t",{"_index":115,"title":{},"description":{"3":{}}}],["navigate?parent_id=dataset_def_ui_001",{"_index":169,"title":{},"description":{"3":{}}}],["navigate?parent_id=dataset_def_ui_001&type=properti",{"_index":171,"title":{},"description":{"3":{}}}],["navigate?parent_id=entityset1",{"_index":156,"title":{},"description":{"3":{}}}],["navigate?parent_id=entitytype1",{"_index":159,"title":{},"description":{"3":{}}}],["navigate?parent_id=operation1",{"_index":136,"title":{},"description":{"3":{}}}],["navigate?parent_id=petschema1",{"_index":140,"title":{},"description":{"3":{}}}],["navigate?type=entity_typ",{"_index":146,"title":{},"description":{"3":{}}}],["navigate?type=schema",{"_index":134,"title":{},"description":{"3":{}}}],["navigate?type=ui",{"_index":168,"title":{},"description":{"3":{}}}],["navigaterespons",{"_index":240,"title":{"20":{}},"description":{}}],["nest",{"_index":94,"title":{},"description":{"3":{}}}],["new",{"_index":7,"title":{},"description":{"1":{}}}],["odata",{"_index":86,"title":{},"description":{"3":{}}}],["on",{"_index":150,"title":{},"description":{"3":{},"9":{}}}],["onc",{"_index":214,"title":{},"description":{"9":{}}}],["open",{"_index":131,"title":{},"description":{"3":{}}}],["openapi",{"_index":84,"title":{},"description":{"3":{}}}],["oper",{"_index":3,"title":{},"description":{"0":{},"3":{},"4":{},"6":{},"8":{}}}],["operation1",{"_index":139,"title":{},"description":{"3":{}}}],["option",{"_index":179,"title":{},"description":{"5":{},"7":{}}}],["organ",{"_index":89,"title":{},"description":{"3":{}}}],["origin",{"_index":96,"title":{},"description":{"3":{}}}],["pagin",{"_index":177,"title":{},"description":{"5":{},"7":{}}}],["paramet",{"_index":105,"title":{},"description":{"3":{}}}],["part",{"_index":206,"title":{},"description":{"9":{}}}],["partial",{"_index":26,"title":{},"description":{"1":{}}}],["patch",{"_index":27,"title":{},"description":{"1":{}}}],["peopl",{"_index":145,"title":{},"description":{"3":{}}}],["person",{"_index":148,"title":{},"description":{"3":{}}}],["pet",{"_index":135,"title":{},"description":{"3":{}}}],["petschema1",{"_index":141,"title":{},"description":{"3":{}}}],["point",{"_index":32,"title":{},"description":{"1":{},"9":{}}}],["post",{"_index":15,"title":{},"description":{"1":{}}}],["process",{"_index":79,"title":{},"description":{"2":{},"9":{}}}],["progress",{"_index":44,"title":{},"description":{"1":{}}}],["properti",{"_index":160,"title":{},"description":{"3":{}}}],["provid",{"_index":98,"title":{},"description":{"3":{},"9":{}}}],["put",{"_index":22,"title":{},"description":{"1":{}}}],["queri",{"_index":111,"title":{},"description":{"3":{}}}],["re-import",{"_index":216,"title":{},"description":{"9":{}}}],["record",{"_index":167,"title":{},"description":{"3":{}}}],["ref",{"_index":127,"title":{},"description":{"3":{}}}],["refer",{"_index":126,"title":{},"description":{"3":{}}}],["reject",{"_index":46,"title":{},"description":{"1":{}}}],["relationship",{"_index":59,"title":{"14":{}},"description":{"1":{},"3":{}}}],["remov",{"_index":37,"title":{},"description":{"1":{}}}],["repeat",{"_index":48,"title":{},"description":{"1":{}}}],["replac",{"_index":23,"title":{},"description":{"1":{}}}],["report",{"_index":166,"title":{},"description":{"3":{}}}],["represent",{"_index":10,"title":{},"description":{"1":{}}}],["request",{"_index":49,"title":{},"description":{"1":{},"3":{}}}],["requir",{"_index":34,"title":{},"description":{"1":{}}}],["reset",{"_index":38,"title":{},"description":{"1":{}}}],["resolv",{"_index":125,"title":{},"description":{"3":{}}}],["respons",{"_index":137,"title":{},"description":{"3":{}}}],["result",{"_index":51,"title":{},"description":{"1":{},"5":{},"7":{}}}],["retriev",{"_index":173,"title":{"5":{},"7":{}},"description":{}}],["return",{"_index":112,"title":{},"description":{"3":{}}}],["root",{"_index":92,"title":{},"description":{"3":{}}}],["rule",{"_index":62,"title":{},"description":{"1":{}}}],["same",{"_index":45,"title":{},"description":{"1":{},"9":{}}}],["sampl",{"_index":109,"title":{},"description":{"3":{}}}],["scan",{"_index":192,"title":{},"description":{"9":{}}}],["scan_id",{"_index":204,"title":{},"description":{"9":{}}}],["scenario",{"_index":190,"title":{},"description":{"9":{}}}],["schema",{"_index":83,"title":{"10":{}},"description":{"3":{}}}],["schemadefinit",{"_index":223,"title":{},"description":{"11":{},"12":{},"13":{},"14":{},"15":{},"16":{},"17":{},"18":{},"19":{},"20":{},"21":{},"22":{},"23":{}}}],["schemaref=\"#/components/schemas/apierror",{"_index":237,"title":{},"description":{"18":{}}}],["schemaref=\"#/components/schemas/catalogerror",{"_index":247,"title":{},"description":{"23":{}}}],["schemaref=\"#/components/schemas/datamodelrequest",{"_index":231,"title":{},"description":{"15":{}}}],["schemaref=\"#/components/schemas/datamodelrespons",{"_index":233,"title":{},"description":{"16":{}}}],["schemaref=\"#/components/schemas/deletedatamodelrespons",{"_index":239,"title":{},"description":{"19":{}}}],["schemaref=\"#/components/schemas/ent",{"_index":226,"title":{},"description":{"12":{}}}],["schemaref=\"#/components/schemas/entityel",{"_index":225,"title":{},"description":{"11":{}}}],["schemaref=\"#/components/schemas/errordetail",{"_index":235,"title":{},"description":{"17":{}}}],["schemaref=\"#/components/schemas/importdatamodelrequest",{"_index":243,"title":{},"description":{"21":{}}}],["schemaref=\"#/components/schemas/importdatamodelrespons",{"_index":245,"title":{},"description":{"22":{}}}],["schemaref=\"#/components/schemas/modelelementref",{"_index":228,"title":{},"description":{"13":{}}}],["schemaref=\"#/components/schemas/navigaterespons",{"_index":241,"title":{},"description":{"20":{}}}],["schemaref=\"#/components/schemas/relationship",{"_index":229,"title":{},"description":{"14":{}}}],["section",{"_index":36,"title":{},"description":{"1":{}}}],["secur",{"_index":138,"title":{},"description":{"3":{}}}],["semant",{"_index":88,"title":{},"description":{"3":{}}}],["server",{"_index":17,"title":{},"description":{"1":{}}}],["showwriteonly={tru",{"_index":224,"title":{},"description":{"11":{},"12":{},"13":{},"14":{},"15":{},"16":{},"17":{},"18":{},"19":{},"20":{},"21":{},"22":{},"23":{}}}],["singl",{"_index":200,"title":{},"description":{"9":{}}}],["sourc",{"_index":128,"title":{},"description":{"3":{}}}],["specif",{"_index":102,"title":{},"description":{"3":{},"9":{}}}],["specifi",{"_index":28,"title":{},"description":{"1":{}}}],["start",{"_index":91,"title":{},"description":{"3":{}}}],["store",{"_index":18,"title":{},"description":{"1":{}}}],["structur",{"_index":87,"title":{},"description":{"3":{}}}],["support",{"_index":176,"title":{},"description":{"5":{},"7":{}}}],["synchron",{"_index":74,"title":{},"description":{"2":{}}}],["systemscan",{"_index":207,"title":{},"description":{"9":{}}}],["tabl",{"_index":113,"title":{},"description":{"3":{},"9":{}}}],["table123",{"_index":121,"title":{},"description":{"3":{}}}],["technic",{"_index":193,"title":{},"description":{"9":{}}}],["temporari",{"_index":73,"title":{},"description":{"2":{}}}],["thousand",{"_index":194,"title":{},"description":{"9":{}}}],["time",{"_index":221,"title":{},"description":{"9":{}}}],["transact",{"_index":197,"title":{},"description":{"9":{}}}],["travers",{"_index":93,"title":{},"description":{"3":{}}}],["type",{"_index":117,"title":{},"description":{"3":{}}}],["ui",{"_index":165,"title":{},"description":{"3":{}}}],["underscor",{"_index":68,"title":{},"description":{"1":{}}}],["uniqu",{"_index":60,"title":{},"description":{"1":{},"3":{}}}],["updat",{"_index":21,"title":{},"description":{"1":{},"9":{}}}],["us",{"_index":14,"title":{},"description":{"1":{},"9":{}}}],["version",{"_index":19,"title":{},"description":{"1":{}}}],["view",{"_index":114,"title":{},"description":{"3":{}}}],["wihthin",{"_index":56,"title":{},"description":{"1":{}}}],["within",{"_index":54,"title":{},"description":{"1":{}}}],["work",{"_index":11,"title":{},"description":{"1":{},"9":{}}}],["yield",{"_index":50,"title":{},"description":{"1":{}}}]],"pipeline":[]}},"options":{"schemaDefinitionsTagName":"Schemas"}};

      var container = document.getElementById('redoc');
      Redoc.hydrate(__redoc_state, container);

      </script>
</body>

</html>
