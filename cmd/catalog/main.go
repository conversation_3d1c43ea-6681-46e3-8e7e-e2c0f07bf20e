package main

import (
	"context"
	_ "embed"
	"os"

	"github.com/kelseyhightower/envconfig"
	"github.com/sirupsen/logrus"

	"github.com/BackOfficeAssoc/catalog/metrics"
	"github.com/BackOfficeAssoc/catalog/repo"
	"github.com/BackOfficeAssoc/catalog/server"
	"github.com/BackOfficeAssoc/pkg/exp/graceful"
	"github.com/BackOfficeAssoc/pkg/guac"
	"github.com/BackOfficeAssoc/pkg/probe"
	"github.com/BackOfficeAssoc/pkg/synpani"
	"github.com/BackOfficeAssoc/qzar/pkg/authorizer"
	qzar "github.com/BackOfficeAssoc/qzar/pkg/client"
	"github.com/BackOfficeAssoc/qzar/pkg/supt"
)

const serviceName = "catalog"

type Config struct {
	EnableDBSetup      bool         `envconfig:"ENABLE_DB" default:"false"`
	DBConnectionString string       `envconfig:"DB_CONNECTION_STRING" required:"true"`
	ListenAddr         string       `envconfig:"LISTEN_ADDR" default:"80"`
	LogLevel           logrus.Level `envconfig:"LOG_LEVEL" default:"info"`
	QzarBase           string       `envconfig:"QZAR_BASE" required:"true"`

	Qzar  qzar.Config
	Super supt.Config
}

func main() {
	ctx := context.Background()
	log := logrus.New()
	log.SetOutput(os.Stdout)
	syn := synpani.New(log, serviceName)

	metrics.Register(syn)
	authorizer.RegisterMetrics(syn)

	probe.SetServiceName(serviceName)
	probe.SetSampler(syn)
	probe.SetLogger(log)

	prb := probe.Load(ctx)

	var cfg Config
	if err := envconfig.Process("", &cfg); err != nil {
		prb.WithError(err).Emit(ProbeConfigFailed)
		os.Exit(1)
	}

	log.SetLevel(cfg.LogLevel)

	super, err := supt.New(&cfg.Super)
	if err != nil {
		prb.WithError(err).WithField("dependency", "supt").
			Emit(ProbeDependencyInitFailure)
		os.Exit(1)
	}

	qzarc := qzar.New(cfg.QzarBase, guac.Guac{Tokens: super})
	if err != nil {
		prb.WithError(err).WithField("dependency", "qzar").
			Emit(ProbeDependencyInitFailure)
		os.Exit(1)
	}

	authz := authorizer.New(apiSec, qzarc)
	authzMW := authz.Middleware()

	var repoRef *repo.Repository
	if cfg.EnableDBSetup {
		// TODO: rashmy to remove this check when we are ready to use DB in DEV env
		db, err := repo.Connect(cfg.DBConnectionString)
		if err != nil {
			log.WithError(err).Fatal("could not connect to database")
		}
		repo := repo.New(db)
		if err := repo.Migrate(context.Background()); err != nil {
			log.WithError(err).Fatal("could not migrate the database")
		}
		repoRef = repo
	} else {
		repoRef = repo.New(nil)
	}

	srv := server.Server{
		Authz: authzMW,
		Repo:  repoRef,
		Qzar:  qzarc,
	}

	prb.Emit(func(c *probe.Collector) {
		c.Logger.Infof("starting on :%s...", cfg.ListenAddr)
	})
	if err = graceful.Run(ctx, srv.Engine(), ":"+cfg.ListenAddr); err != nil {
		log.WithError(err)
	}
}
