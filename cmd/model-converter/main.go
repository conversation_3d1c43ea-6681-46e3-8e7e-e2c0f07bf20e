package main

import (
	"flag"
	"fmt"
	"path/filepath"
	"strings"

	"github.com/BackOfficeAssoc/catalog/internal"
)

func isJSONFile(filename string) bool {
	cleanPath := filepath.Clean(filename)
	return strings.HasSuffix(cleanPath, ".json")
}

func main() {
	inputFilePath := flag.String("input", "", "Path to the input scan results file")
	outputFilePath := flag.String("output", "", "Path for the output file")
	flag.Parse()

	if !isJSONFile(*inputFilePath) {
		fmt.Println("Input file is not a valid JSON file.")
		return
	}
	if !isJSONFile(*outputFilePath) {
		fmt.Println("Output file is not a valid JSON file.")
		return
	}

	err := internal.ConvertComponentToCatalogDataModel(*inputFilePath, *outputFilePath)
	if err != nil {
		fmt.Println("Error converting component to catalog data model:", err)
		return
	}

	fmt.Println("Processing completed successfully.")
}
