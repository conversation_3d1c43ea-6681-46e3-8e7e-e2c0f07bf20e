package main

import (
	"flag"
	"fmt"
	"os"

	metameta "github.com/BackOfficeAssoc/catalog/meta-meta"
)

func main() {
	// Setup command-line flags with default values
	baseFile := flag.String("base", "meta-meta/json_schema/base_datamodel_schema.json", "Path to the base schema file")
	dsFile := flag.String("mm", "meta-meta/definitions/relational_meta.json", "Path to the meta-meta definition file")
	outputFile := flag.String("output", "meta-meta/json_schema/relational_schema.json", "Path for the output file")
	flag.Parse()

	// Read base schema
	baseData, err := os.ReadFile(*baseFile)
	if err != nil {
		fmt.Printf("Error reading %s: %v\n", *baseFile, err)
		os.Exit(1)
	}

	// Read meta-meta definition
	dsData, err := os.ReadFile(*dsFile)
	if err != nil {
		fmt.Printf("Error reading %s: %v\n", *dsFile, err)
		os.Exit(1)
	}

	// Generate the schema and save the value in memory as requested
	result, err := metameta.GenerateDataModelSchema(baseData, dsData)
	if err != nil {
		fmt.Printf("Error generating schema: %v\n", err)
		os.Exit(1)
	}

	// Write the output to file
	f, err := os.Create(*outputFile)
	if err != nil {
		fmt.Printf("Error creating %s: %v\n", *outputFile, err)
		os.Exit(1)
	}
	defer f.Close()

	if _, err := f.Write(result); err != nil {
		fmt.Printf("Error writing to %s: %v\n", *outputFile, err)
		os.Exit(1)
	}

	fmt.Printf("Successfully generated %s!\n", *outputFile)
}
