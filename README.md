# Catalog Service

Provides the ability to define Technical and Business Catalog.

# Service Design

Review the service design documented in the [RFC](https://github.com/BackOfficeAssoc/rfc/pull/108).

# Open API

The Open API specification resides in the folder `open-api`. We have split the files to ease maintainance.

- `make openapi`
  - This command will bundle all the split API definitions into a single file `openapi.yaml` and generate a HTML `api_reference.html`

We use [redocly-cli](https://github.com/Redocly/redocly-cli) to bundle the spec and generated a HTML file so that you can visually inspect the generated API. For more information read the [redocly documentation](https://redocly.com/docs/cli)

# Regenerate meta-meta JSON schema

To support new entity/element/relationship types -

- Update the `meta-meta/definitions/relational_meta.json`
- Regenerate the JSON schema using the below command
  - `make regen-metameta`

# Convert components to new catalog model format

To convert the scan results file having Components to the new Catalog Model format -

- `go run ./cmd/model-converter -input internal/scan_sample_skp_security_model.json -output internal/output.json`

# Useful DB Commands

- Check if the triggers are installed in the database
  `SELECT tgname FROM pg_trigger WHERE NOT tgisinternal;`
