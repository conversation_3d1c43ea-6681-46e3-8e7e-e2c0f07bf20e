// Package server exposes REST endpoints.
package server

import (
	"net/http"

	"github.com/gin-gonic/gin"

	"github.com/BackOfficeAssoc/catalog/repo"
	"github.com/BackOfficeAssoc/catalog/server/datamodel"
	"github.com/BackOfficeAssoc/catalog/server/samples"
	"github.com/BackOfficeAssoc/pkg/probe"
	qzarc "github.com/BackOfficeAssoc/qzar/pkg/client"
)

type Server struct {
	Repo  repo.Repo
	Qzar  qzarc.Client
	Authz gin.HandlerFunc
}

// Attach registers the API handlers on the gin Engine.
func (s *Server) Engine() *gin.Engine {
	eng := gin.New()
	eng.Use(gin.Recovery())

	// Monitoring
	eng.GET("/health", func(c *gin.Context) { c.Status(http.StatusOK) })
	eng.GET("/metrics", probe.Sampler().Handler())

	// Internal API
	{
		api := eng.Group("/")
		api.Use(probe.Middlewares()...)
		api.Use(s.Authz)

		{
			samplesHandler := api.Group("/samples")
			samplesHandler.POST("", samples.Create(s.Repo)...)
			samplesHandler.PUT(":id", samples.Update(s.Repo)...)
			samplesHandler.GET("", samples.Find(s.Repo)...)
			samplesHandler.GET(":id", samples.Fetch(s.Repo)...)
			samplesHandler.DELETE(":id", samples.Delete(s.Repo)...)
		}

		{
			datamodelHandler := api.Group("/contexts/:context_id/datamodel")
			datamodelHandler.POST("", datamodel.Create(s.Repo)...)
			datamodelHandler.GET("/navigation", datamodel.NavigateDataModel(s.Repo)...)
		}
	}
	return eng
}
