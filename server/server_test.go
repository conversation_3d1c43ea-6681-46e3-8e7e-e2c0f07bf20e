package server_test

import (
	"context"
	"database/sql"
	"net/http/httptest"
	"testing"

	"github.com/gin-gonic/gin"
	"github.com/stretchr/testify/suite"

	"github.com/BackOfficeAssoc/pkg/guac"
	"github.com/BackOfficeAssoc/pkg/probe/dogpony"
	authorizertesting "github.com/BackOfficeAssoc/qzar/pkg/authorizer/authorizer_testing"
	qzarmock "github.com/BackOfficeAssoc/qzar/pkg/client/client-mock"
	qzarm "github.com/BackOfficeAssoc/qzar/pkg/models"
	supttesting "github.com/BackOfficeAssoc/qzar/pkg/supt/supt-testing"

	"github.com/BackOfficeAssoc/catalog/client"
	"github.com/BackOfficeAssoc/catalog/fixtures"
	"github.com/BackOfficeAssoc/catalog/repo/dto"
	"github.com/BackOfficeAssoc/catalog/server"
)

func TestClientLibrary(t *testing.T) {
	suite.Run(t, new(TestSuite))
}

type TestSuite struct {
	suite.Suite

	server *httptest.Server
	tntID  string
	repo   *fixtures.RepoMock
	qzar   *qzarmock.ClientMock
	client client.Client
}

func (suite *TestSuite) SetupSuite() {
	gin.SetMode(gin.ReleaseMode)
	dogpony.NewFunc(suite.T)

}

func (suite *TestSuite) SetupTest() {
	suite.repo = &fixtures.RepoMock{
		CreateEntitiesFunc: func(ctx context.Context, tx *sql.Tx, entities dto.Entities) error {
			return nil
		},
	}
	suite.qzar = &qzarmock.ClientMock{}

	suite.tntID = "tnt_1"
	authz := authorizertesting.New("pcp_clientTest").
		WithTenant(&suite.tntID).
		WithAuthorizedActions([]qzarm.ActionID{qzarm.AdministrationCreateCategoriesAction}).
		Middleware()

	srv := server.Server{
		Authz: authz,
		Repo:  suite.repo,
		Qzar:  suite.qzar,
	}
	eng := srv.Engine()
	suite.server = httptest.NewServer(eng)

	suite.client = client.Client{
		BaseURL: suite.server.URL,
		Guac: guac.Guac{
			Tokens: &supttesting.Mock{
				NextToken: "test_token",
			},
		},
	}
}

func (suite *TestSuite) Run(testName string, fn func()) bool {
	suite.SetupTest()
	return suite.Suite.Run(testName, fn)
}
