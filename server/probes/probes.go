// Package probes defines probes that are used by the API handlers.
package probes

import "github.com/BackOfficeAssoc/pkg/probe"

func BadRequest(c *probe.Collector) {
	c.<PERSON>gger.Debug("bad request")
}

func ProbeUnauthorized(c *probe.Collector) {
	c.<PERSON>gger.Debug("unauthorized request")
}

func <PERSON>o<PERSON>rror(c *probe.Collector) {
	c.<PERSON>.<PERSON>("repository error")
}

func MissingAuthData(c *probe.Collector) {
	c.<PERSON>.<PERSON>r("auth data was missing from request context. Are you missing a secpol middleware?")
}

func FailedToFetchPrincipal(c *probe.Collector) {
	c.<PERSON>.<PERSON>("Failed to fetch principal. Are you missing a valid SERVICE_PRINCIPAL?")
}

func ValidationError(c *probe.Collector) {
	c.<PERSON><PERSON>.<PERSON>("Schema validation error")
}
