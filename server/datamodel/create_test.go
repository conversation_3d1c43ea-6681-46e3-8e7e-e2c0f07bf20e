package datamodel_test

import (
	"context"
	"database/sql"
	"encoding/json"
	"net/http"

	"github.com/gin-gonic/gin"

	"github.com/BackOfficeAssoc/catalog/fixtures"
	"github.com/BackOfficeAssoc/catalog/models"
	"github.com/BackOfficeAssoc/catalog/repo/dto"
	"github.com/BackOfficeAssoc/catalog/server/datamodel"
	authorizertesting "github.com/BackOfficeAssoc/qzar/pkg/authorizer/authorizer_testing"
)

func (s *ChainTestSuite) TestCreateDataModel_OnlyEntities_Success() {
	// Prepare test data
	testUserID := "pcp_123"
	testTenantID := "tnt_1"
	expectedVersion := "ver_123"
	expectedStatus := http.StatusOK
	datamodel_context_id := "dst_111"

	datamodelRequest := models.DataModel{
		MetaSchemaVersion: "relational-20250501",
		TenantID:          testTenantID,
		Entities: []models.Entity{
			{
				ModelName:   "ns1.production.product",
				Name:        "Product",
				Type:        "TABLE",
				Description: "Product table",
				Active:      true,
				Tags:        []string{},
				Properties:  map[string]interface{}{"schema": "Production"},
				EntityElements: []models.EntityElement{
					{
						ModelName:    "ns1.production.product.productid",
						Name:         "ProductID",
						Type:         "COLUMN",
						DataType:     "int",
						DefaultValue: "",
						Precision:    10,
						Scale:        0,
						IsRequired:   true,
						Properties:   map[string]interface{}{},
					},
					{
						ModelName:   "ns1.production.product.pk.productid",
						Name:        "PK_ProductID",
						Type:        "PRIMARY-KEY",
						Description: "Primary key for ProductID",
						Properties:  map[string]interface{}{},
					},
					{
						ModelName:   "ns1.production.product.idx.productid",
						Name:        "IDX_ProductID",
						Type:        "INDEX",
						Description: "Index for ProductID",
						Properties: map[string]interface{}{
							"is_clustered": true,
							"is_unique":    true,
						},
					},
					{
						ModelName:   "ns1.production.product.fk.categoryid",
						Name:        "FK_CategoryID",
						Type:        "FOREIGN-KEY",
						Description: "Foreign key to ProductCategory",
						Properties: map[string]interface{}{
							"on_update": "CASCADE",
							"on_delete": "NO ACTION",
							"validate":  true,
						},
					},
					{
						ModelName:   "ns1.production.product.pk.productid",
						Name:        "PK_ProductID",
						Type:        "PRIMARY-KEY",
						Description: "Primary key for ProductID",
						Properties:  map[string]interface{}{},
					},
					{
						ModelName:   "ns1.production.product.idx.productid",
						Name:        "IDX_ProductID",
						Type:        "INDEX",
						Description: "Index for ProductID",
						Properties: map[string]interface{}{
							"is_clustered": true,
							"is_unique":    true,
						},
					},
					{
						ModelName:   "ns1.production.product.fk.categoryid",
						Name:        "FK_CategoryID",
						Type:        "FOREIGN-KEY",
						Description: "Foreign key to ProductCategory",
						Properties: map[string]interface{}{
							"on_update": "CASCADE",
							"on_delete": "NO ACTION",
							"validate":  true,
						},
					},
				},
			},
		},
	}

	// Create request
	reqData := map[string]interface{}{
		"meta_schema_version": datamodelRequest.MetaSchemaVersion,
		"entities":            datamodelRequest.Entities,
		"relationships":       []models.Relationship{},
		"tenant_id":           datamodelRequest.TenantID,
	}

	// Mock repository
	repo := &fixtures.RepoMock{
		GenerateBranchAndVersionIDFunc: func(ctx context.Context, tenantID string, contextID string, user_id string, tx *sql.Tx) (string, string) {
			return "default", expectedVersion
		},
		CreateEntitiesFunc: func(ctx context.Context, tx *sql.Tx, entities dto.Entities) error {
			return nil
		},
		CreateEntityElementsFunc: func(ctx context.Context, tx *sql.Tx, parentID string, entityElements dto.EntityElements) error {
			return nil
		},
		CreateRelationshipsFunc: func(ctx context.Context, tx *sql.Tx, relationships dto.Relationships) error {
			return nil
		},
	}

	// Invoke the handler
	authz := authorizertesting.New(testUserID)
	authz.WithTenant(&testTenantID)
	mw := []gin.HandlerFunc{authz.Middleware()}
	handlers := append(mw, datamodel.Create(repo)...)

	w, err := s.NewRequest("POST").
		WithPath("context_id", datamodel_context_id).
		WithJSON(&reqData).
		Send(handlers...)
	s.Require().NoError(err, "failed to send olives request")

	// Verify the response
	if !s.Equal(expectedStatus, w.Code) {
		s.FailNow(string(w.Body.Bytes()))
	}
	if expectedStatus != http.StatusOK {
		return
	}

	var respBody models.CreateDataModelResponse
	err = json.Unmarshal(w.Body.Bytes(), &respBody)
	s.Require().NoError(err, "failed to unmarshal olives request")

	s.Equal(datamodelRequest.MetaSchemaVersion, respBody.MetaSchemaVersion)
	s.Equal(datamodel_context_id, respBody.ContextID)
	s.Equal(expectedVersion, respBody.NewVersion)

	// Verify the repository calls
	calls1 := repo.CreateEntitiesCalls()
	s.Require().Equal(1, len(calls1), "Must make exactly 1 repo.CreateEntitiesCalls")
	calls2 := repo.CreateEntityElementsCalls()
	s.Require().Equal(1, len(calls2), "Must make exactly 1 repo.CreateEntityElementsCalls")
	calls3 := repo.CreateRelationshipsCalls()
	s.Require().Equal(0, len(calls3), "Must make exactly 0 repo.CreateRelationshipsCalls")
}

func (s *ChainTestSuite) TestCreateDataModel_WithRelationships_Success() {
	// Prepare test data
	testUserID := "pcp_123"
	testTenantID := "tnt_1"
	expectedVersion := "ver_123"
	expectedStatus := http.StatusOK
	datamodel_context_id := "dst_111"

	datamodelRequest := models.DataModel{
		MetaSchemaVersion: "relational-20250501",
		TenantID:          testTenantID,
		Entities: []models.Entity{
			{
				ModelName:   "ns1.production.product",
				Name:        "Product",
				Type:        "TABLE",
				Description: "Product table",
				Active:      true,
				Tags:        []string{},
				Properties:  map[string]interface{}{"schema": "Production"},
				EntityElements: []models.EntityElement{
					{
						ModelName:    "ns1.production.product.productid",
						Name:         "ProductID",
						Type:         "COLUMN",
						DataType:     "int",
						DefaultValue: "",
						Precision:    10,
						Scale:        0,
						IsRequired:   true,
						Properties:   map[string]interface{}{},
					},
					{
						ModelName:    "ns1.production.product.productname",
						Name:         "ProductName",
						Type:         "COLUMN",
						DataType:     "int",
						DefaultValue: "",
						Precision:    10,
						Scale:        0,
						IsRequired:   true,
						Properties:   map[string]interface{}{},
					},
					{
						ModelName:    "ns1.production.product.categoryid",
						Name:         "CategoryID",
						Type:         "COLUMN",
						DataType:     "int",
						DefaultValue: "",
						Precision:    10,
						Scale:        0,
						IsRequired:   true,
						Properties:   map[string]interface{}{},
					},
					{
						ModelName:   "ns1.production.product.pk.productid",
						Name:        "PK_ProductID",
						Type:        "PRIMARY-KEY",
						Description: "Primary key for ProductID",
						Properties:  map[string]interface{}{},
					},
					{
						ModelName:   "ns1.production.product.idx.productname",
						Name:        "IDX_ProductName",
						Type:        "INDEX",
						Description: "Index for ProductName",
						Properties: map[string]interface{}{
							"is_clustered": true,
							"is_unique":    true,
						},
					},
					{
						ModelName:   "ns1.production.product.fk.categoryid",
						Name:        "FK_CategoryID",
						Type:        "FOREIGN-KEY",
						Description: "Foreign key to ProductCategory",
						Properties: map[string]interface{}{
							"on_update": "CASCADE",
							"on_delete": "NO ACTION",
							"validate":  true,
						},
					},
				},
			},
			{
				ModelName:   "ns1.production.productcategory",
				Name:        "ProductCategory",
				Type:        "TABLE",
				Description: "Product category table",
				Active:      true,
				Tags:        []string{},
				Properties:  map[string]interface{}{"schema": "Production"},
				EntityElements: []models.EntityElement{
					{
						ModelName:    "ns1.production.productcategory.categoryid",
						Name:         "CategoryID",
						Type:         "COLUMN",
						DataType:     "int",
						DefaultValue: "",
						Precision:    10,
						Scale:        0,
						IsRequired:   true,
						Properties:   map[string]interface{}{},
					},
					{
						ModelName:    "ns1.production.productcategory.categoryname",
						Name:         "CategoryName",
						Type:         "COLUMN",
						DataType:     "int",
						DefaultValue: "",
						Precision:    10,
						Scale:        0,
						IsRequired:   true,
						Properties:   map[string]interface{}{},
					},
					{
						ModelName:   "ns1.production.productcategory.pk.categoryid",
						Name:        "PK_CategoryID",
						Type:        "PRIMARY-KEY",
						Description: "Primary key for CategoryID",
						Properties:  map[string]interface{}{},
					},
				},
			},
		},
		Relationships: []models.Relationship{
			{
				ModelName:   "ns1.pk_product_productid",
				Name:        "PK_Product_ProductID",
				Type:        "PK_REF",
				Description: "Primary key reference",
				Source:      models.Reference{Ref: "#element/ns1.production.product.pk.productid"},
				Target:      models.Reference{Ref: "#element/ns1.production.product.productid"},
			},
			{
				ModelName:   "ns1.idx_product_productname",
				Name:        "IDX_Product_ProductName",
				Type:        "IDX_REF",
				Description: "Index reference",
				Source:      models.Reference{Ref: "#element/ns1.production.product.idx.productname"},
				Target:      models.Reference{Ref: "#element/ns1.production.product.productname"},
			},
			{
				ModelName:   "ns1.fk_product_category_source",
				Name:        "FK_Product_Category_Source",
				Type:        "FK_REF_SOURCE",
				Description: "Foreign key source reference",
				Source:      models.Reference{Ref: "#element/ns1.production.product.fk.categoryid"},
				Target:      models.Reference{Ref: "#element/ns1.production.product.categoryid"},
			},
			{
				ModelName:   "ns1.fk_product_category_target",
				Name:        "FK_Product_Category_Target",
				Type:        "FK_REF_TARGET",
				Description: "Foreign key target reference",
				Source:      models.Reference{Ref: "#element/ns1.production.product.fk.categoryid"},
				Target:      models.Reference{Ref: "#element/ns1.production.productcategory.categoryid"},
			},
			{
				ModelName:   "ns1.pk_productcategory_categoryid",
				Name:        "PK_ProductCategory_CategoryID",
				Type:        "PK_REF",
				Description: "Primary key reference",
				Source:      models.Reference{Ref: "#element/ns1.production.productcategory.pk.categoryid"},
				Target:      models.Reference{Ref: "#element/ns1.production.productcategory.categoryid"},
			},
		},
	}

	// Create request
	reqData := map[string]interface{}{
		"meta_schema_version": datamodelRequest.MetaSchemaVersion,
		"entities":            datamodelRequest.Entities,
		"relationships":       datamodelRequest.Relationships,
		"tenant_id":           datamodelRequest.TenantID,
	}

	// Mock repository
	repo := &fixtures.RepoMock{
		GenerateBranchAndVersionIDFunc: func(ctx context.Context, tenantID string, contextID string, user_id string, tx *sql.Tx) (string, string) {
			return "default", expectedVersion
		},
		CreateEntitiesFunc: func(ctx context.Context, tx *sql.Tx, entities dto.Entities) error {
			return nil
		},
		CreateEntityElementsFunc: func(ctx context.Context, tx *sql.Tx, parentID string, entityElements dto.EntityElements) error {
			return nil
		},
		CreateRelationshipsFunc: func(ctx context.Context, tx *sql.Tx, relationships dto.Relationships) error {
			return nil
		},
	}

	// Invoke the handler
	authz := authorizertesting.New(testUserID)
	authz.WithTenant(&testTenantID)
	mw := []gin.HandlerFunc{authz.Middleware()}
	handlers := append(mw, datamodel.Create(repo)...)

	w, err := s.NewRequest("POST").
		WithPath("context_id", datamodel_context_id).
		WithJSON(&reqData).
		Send(handlers...)
	s.Require().NoError(err, "failed to send olives request")

	// Verify the response
	if !s.Equal(expectedStatus, w.Code) {
		s.FailNow(string(w.Body.Bytes()))
	}
	if expectedStatus != http.StatusOK {
		return
	}

	var respBody models.CreateDataModelResponse
	err = json.Unmarshal(w.Body.Bytes(), &respBody)
	s.Require().NoError(err, "failed to unmarshal olives request")

	s.Equal(datamodelRequest.MetaSchemaVersion, respBody.MetaSchemaVersion)
	s.Equal(datamodel_context_id, respBody.ContextID)
	s.Equal(expectedVersion, respBody.NewVersion)

	// Verify the repository calls
	calls1 := repo.CreateEntitiesCalls()
	s.Require().Equal(1, len(calls1), "Must make exactly 1 repo.CreateEntitiesCalls")
	calls2 := repo.CreateEntityElementsCalls()
	s.Require().Equal(2, len(calls2), "Must make exactly 2 repo.CreateEntityElementsCalls")
	calls3 := repo.CreateRelationshipsCalls()
	s.Require().Equal(1, len(calls3), "Must make exactly 1 repo.CreateRelationshipsCalls")
}

func (s *ChainTestSuite) TestCreateDataModel_SchemaValidation_Error() {
	// Prepare test data
	testUserID := "pcp_123"
	testTenantID := "tnt_1"
	expectedStatus := http.StatusBadRequest
	expectedVersion := "ver_123"
	datamodel_context_id := "dst_111"

	datamodelRequest := models.DataModel{
		MetaSchemaVersion: "relational-20250501",
		TenantID:          testTenantID,
		Entities: []models.Entity{
			{
				ModelName:      "ns1.production.product",
				Name:           "Product",
				Type:           "TABLE_X",
				Description:    "Product table",
				Active:         true,
				Tags:           []string{},
				Properties:     map[string]interface{}{"schema": "Production"},
				EntityElements: []models.EntityElement{},
			},
		},
	}

	// Create request
	reqData := map[string]interface{}{
		"meta_schema_version": datamodelRequest.MetaSchemaVersion,
		"namespace":           datamodelRequest.Namespace,
		"entities":            datamodelRequest.Entities,
		"relationships":       []models.Relationship{},
		"tenant_id":           datamodelRequest.TenantID,
	}

	// Mock repository
	repo := &fixtures.RepoMock{
		GenerateBranchAndVersionIDFunc: func(ctx context.Context, tenantID string, contextID string, user_id string, tx *sql.Tx) (string, string) {
			return "default", expectedVersion
		},
		CreateEntitiesFunc: func(ctx context.Context, tx *sql.Tx, entities dto.Entities) error {
			return nil
		},
		CreateEntityElementsFunc: func(ctx context.Context, tx *sql.Tx, parentID string, entityElements dto.EntityElements) error {
			return nil
		},
		CreateRelationshipsFunc: func(ctx context.Context, tx *sql.Tx, relationships dto.Relationships) error {
			return nil
		},
	}

	// Invoke the handler
	authz := authorizertesting.New(testUserID)
	authz.WithTenant(&testTenantID)
	mw := []gin.HandlerFunc{authz.Middleware()}
	handlers := append(mw, datamodel.Create(repo)...)

	w, err := s.NewRequest("POST").
		WithPath("context_id", datamodel_context_id).
		WithJSON(&reqData).
		Send(handlers...)
	s.Require().NoError(err, "failed to send olives request")

	// Verify the response
	s.Equal(expectedStatus, w.Code)

	// Verify the repository calls
	calls1 := repo.CreateEntitiesCalls()
	s.Require().Equal(0, len(calls1), "Must make exactly 0 repo.CreateEntitiesCalls")
	calls2 := repo.CreateEntityElementsCalls()
	s.Require().Equal(0, len(calls2), "Must make exactly 0 repo.CreateEntityElementsCalls")
	calls3 := repo.CreateRelationshipsCalls()
	s.Require().Equal(0, len(calls3), "Must make exactly 0 repo.CreateRelationshipsCalls")
}
