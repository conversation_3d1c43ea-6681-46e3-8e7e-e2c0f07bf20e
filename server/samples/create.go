package samples

import (
	"errors"
	"net/http"

	"github.com/gin-gonic/gin"

	"github.com/BackOfficeAssoc/pkg/httperrors"
	"github.com/BackOfficeAssoc/pkg/probe"
	"github.com/BackOfficeAssoc/qzar/pkg/authorizer"

	"github.com/BackOfficeAssoc/catalog/models"
	"github.com/BackOfficeAssoc/catalog/repo"
	"github.com/BackOfficeAssoc/catalog/repo/dto"
	"github.com/BackOfficeAssoc/catalog/server/probes"
)

type CreateRepo interface {
	CreateSample(request dto.CreateSampleInput, createdBy, tenantID string) (id string, err error)
	FetchSample(id string, tenantID *string) (dto.Sample, error)
}

func Create(r CreateRepo) []gin.HandlerFunc {
	h := createHandler{
		repo: r,
	}
	return h.Chain()
}

type createHandler struct {
	// Dependencies to be injected...
	repo CreateRepo
}

type createRequest struct {
	models.CreateSampleInput
}

func (h *createHandler) Validate(c *gin.Context) {
	var req createRequest
	if !req.bindAndValidate(c) {
		return
	}
	c.Set("request", &req)
}

func (h *createHandler) Authorize(c *gin.Context) {
	req := c.MustGet("request").(*createRequest)

	authZ, err := authorizer.Output(c)
	if err != nil {
		probe.Load(c).Emit(probes.ProbeUnauthorized)
		httperrors.StatusText(c, http.StatusUnauthorized)
		return
	}

	if authZ.HasGlobalAccess() && req.TenantID == nil {
		probe.Load(c).WithField("user_id", authZ.UserID).Emit(probes.ProbeUnauthorized)
		httperrors.StatusText(c, http.StatusUnauthorized)
		return
	}

	if !authZ.HasGlobalAccess() {
		req.TenantID = authZ.TenantID
	}

	c.Set("user_id", authZ.UserID)
}

func (h *createHandler) Handle(c *gin.Context) {
	var (
		req    = c.MustGet("request").(*createRequest)
		userID = c.MustGet("user_id").(string)
		prb    = probe.Load(c)
	)
	var dto dto.CreateSampleInput
	dto.FromModel(req.CreateSampleInput)
	id, err := h.repo.CreateSample(dto, userID, *req.TenantID)
	if err != nil {
		if errors.Is(err, repo.ErrConflict) {
			httperrors.Conflict(c, "entity already exists")
			return
		}

		prb.WithError(err).
			WithField("func", "CreateSample").
			Emit(probes.RepoError)
		httperrors.InternalServerError(c, "unexpected error while creating sample")
		return
	}

	s, err := h.repo.FetchSample(id, nil)
	if err != nil {
		if errors.Is(err, repo.ErrNotFound) {
			httperrors.NotFound(c, "%s not found", id)
			return
		}

		prb.WithError(err).
			WithField("func", "FetchSample").
			Emit(probes.RepoError)
		httperrors.InternalServerError(c, "unexpected error while fetching created sample")
		return
	}

	c.JSON(http.StatusOK, s.ToModel())
}

func (h *createHandler) Chain() []gin.HandlerFunc {
	return []gin.HandlerFunc{
		h.Validate,
		h.Authorize,
		h.Handle,
	}
}

func (r *createRequest) bindAndValidate(c *gin.Context) bool {
	if err := c.BindJSON(r); err != nil {
		httperrors.BadRequest(c, "Failed to bind body as JSON: %v ", err)
		return false
	}
	if r.Name == "" {
		httperrors.BadRequest(c, "name should be present")
		return false
	}
	if r.Type == models.SampleTypeUnknown {
		httperrors.BadRequest(c, "Unsupported sample type. Supported types are: %v", models.SampleTypes())
		return false
	}
	return true
}
