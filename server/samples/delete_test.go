package samples_test

import (
	"fmt"
	"net/http"

	"github.com/gin-gonic/gin"

	"github.com/BackOfficeAssoc/catalog/fixtures"
	"github.com/BackOfficeAssoc/catalog/repo"
	"github.com/BackOfficeAssoc/catalog/server/samples"
	authorizertesting "github.com/BackOfficeAssoc/qzar/pkg/authorizer/authorizer_testing"
)

func (s *ChainTestSuite) TestDeleteHandler() {
	var (
		tntID = "tnt_1"
	)

	tests := []struct {
		name       string
		userID     string
		tntID      *string
		id         string
		repoErr    error
		respStatus int
	}{
		{
			name:       "ok",
			userID:     "pcp_1",
			tntID:      &tntID,
			id:         "foo",
			respStatus: http.StatusNoContent,
		},
		{
			name:       "ok-internal-authz",
			userID:     "pcp_1",
			id:         "foo",
			respStatus: http.StatusNoContent,
		},
		{
			name:       "not-found",
			userID:     "pcp_1",
			id:         "foo",
			repoErr:    repo.ErrNotFound,
			respStatus: http.StatusNotFound,
		},
		{
			name:       "repo-error",
			userID:     "pcp_1",
			id:         "foo",
			repoErr:    fmt.Errorf("unexpected error"),
			respStatus: http.StatusInternalServerError,
		},
		{
			name:       "no-auth",
			id:         "foo",
			respStatus: http.StatusUnauthorized,
		},
	}
	for _, test := range tests {
		s.Run(test.name, func() {
			repo := &fixtures.RepoMock{
				DeleteSampleFunc: func(string, *string) error {
					return test.repoErr
				},
			}

			authz := authorizertesting.New(test.userID)
			if test.tntID != nil {
				authz.WithTenant(test.tntID)
			}
			mw := []gin.HandlerFunc{authz.Middleware()}
			handlers := append(mw, samples.Delete(repo)...)

			w, err := s.NewRequest("DELETE").
				WithPath("id", test.id).
				Send(handlers...)
			s.Require().NoError(err, "failed to send olives request")
			if !s.Equal(test.respStatus, w.Code) {
				s.FailNow(string(w.Body.Bytes()))
			}
			if test.respStatus != http.StatusNoContent {
				return
			}

			if calls := repo.DeleteSampleCalls(); s.Equal(1, len(calls), "DeleteSample calls") {
				s.Equal(test.id, calls[0].ID, "ID passed to DeleteSample")
				if test.tntID == nil {
					s.Nil(calls[0].TenantID, "TenantID passed to DeleteSample")
				} else {
					s.Require().NotNil(calls[0].TenantID, "TenantID passed to DeleteSample")
					s.Equal(test.tntID, calls[0].TenantID, "TenantID passed to DeleteSample")
				}
			}
		})
	}
}
