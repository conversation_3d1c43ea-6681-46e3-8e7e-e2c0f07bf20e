package samples_test

import (
	"encoding/json"
	"fmt"
	"net/http"

	"github.com/gin-gonic/gin"

	"github.com/BackOfficeAssoc/catalog/fixtures"
	"github.com/BackOfficeAssoc/catalog/models"
	"github.com/BackOfficeAssoc/catalog/repo"
	"github.com/BackOfficeAssoc/catalog/repo/dto"
	"github.com/BackOfficeAssoc/catalog/server/samples"
	authorizertesting "github.com/BackOfficeAssoc/qzar/pkg/authorizer/authorizer_testing"
)

func (s *ChainTestSuite) TestFetchHandler() {
	var (
		tntID      = "tnt_1"
		id         = "foo"
		mockSample = dto.Sample{
			ID:        id,
			CreatedBy: "pcp_1",
			Name:      "Bar",
			TenantID:  "tnt_1",
			Type:      "Foo",
		}
	)

	tests := []struct {
		name       string
		userID     string
		tntID      *string
		id         string
		repoErr    error
		respStatus int
	}{
		{
			name:       "ok",
			id:         "foo",
			userID:     "pcp_1",
			tntID:      &tntID,
			respStatus: http.StatusOK,
		},
		{
			name:       "ok-internal-authz",
			id:         "foo",
			userID:     "pcp_1",
			respStatus: http.StatusOK,
		},
		{
			name:       "not-found",
			id:         "foo",
			userID:     "pcp_1",
			repoErr:    repo.ErrNotFound,
			respStatus: http.StatusNotFound,
		},
		{
			name:       "repo-error",
			id:         "foo",
			userID:     "pcp_1",
			repoErr:    fmt.Errorf("unexpected error"),
			respStatus: http.StatusInternalServerError,
		},
		{
			name:       "no-auth",
			id:         "foo",
			respStatus: http.StatusUnauthorized,
		},
	}
	for _, test := range tests {
		s.Run(test.name, func() {
			repo := &fixtures.RepoMock{
				FetchSampleFunc: func(string, *string) (dto.Sample, error) {
					return mockSample, test.repoErr
				},
			}

			authz := authorizertesting.New(test.userID)
			if test.tntID != nil {
				authz.WithTenant(test.tntID)
			}
			mw := []gin.HandlerFunc{authz.Middleware()}
			handlers := append(mw, samples.Fetch(repo)...)

			w, err := s.NewRequest("GET").
				WithPath("id", test.id).
				Send(handlers...)
			s.Require().NoError(err, "failed to send olives request")
			if !s.Equal(test.respStatus, w.Code) {
				s.FailNow(string(w.Body.Bytes()))
			}
			if test.respStatus != http.StatusOK {
				return
			}

			var respBody models.Sample
			if err := json.Unmarshal(w.Body.Bytes(), &respBody); !s.NoError(err) {
				return
			}

			s.Equal(mockSample.ID, respBody.ID)
			s.Equal(mockSample.CreatedBy, respBody.CreatedBy)
			s.Equal(mockSample.Name, respBody.Name)
			s.Equal(mockSample.TenantID, respBody.TenantID)

			var st models.SampleType
			st.Parse(mockSample.Type)
			s.Equal(st, respBody.Type)

			if calls := repo.FetchSampleCalls(); s.Equal(1, len(calls), "FetchSample calls") {
				s.Equal(test.id, calls[0].ID, "ID passed to FetchSample")
				if test.tntID == nil {
					s.Nil(calls[0].TenantID, "TenantID passed to FetchSample")
				} else {
					s.Require().NotNil(calls[0].TenantID, "TenantID passed to FetchSample")
					s.Equal(test.tntID, calls[0].TenantID, "TenantID passed to FetchSample")
				}
			}
		})
	}
}
