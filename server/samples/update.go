package samples

import (
	"errors"
	"net/http"

	"github.com/gin-gonic/gin"

	"github.com/BackOfficeAssoc/catalog/models"
	"github.com/BackOfficeAssoc/catalog/repo"
	"github.com/BackOfficeAssoc/catalog/repo/dto"
	"github.com/BackOfficeAssoc/catalog/server/probes"
	"github.com/BackOfficeAssoc/pkg/httperrors"
	"github.com/BackOfficeAssoc/pkg/probe"
	"github.com/BackOfficeAssoc/qzar/pkg/authorizer"
)

type UpdateRepo interface {
	UpdateSample(id string, request dto.UpdateSampleInput, tenantID *string) error
	FetchSample(id string, tenantID *string) (dto.Sample, error)
}

func Update(r UpdateRepo) []gin.HandlerFunc {
	h := updateHandler{
		repo: r,
	}
	return h.Chain()
}

type updateHandler struct {
	// Dependencies to be injected...
	repo UpdateRepo
}

type updateRequest struct {
	models.UpdateSampleInput
}

func (h *updateHandler) Validate(c *gin.Context) {
	var req updateRequest
	if !req.bindAndValidate(c) {
		return
	}
	c.Set("request", &req)
}

func (h *updateHandler) Authorize(c *gin.Context) {
	authZ, err := authorizer.Output(c)
	if err != nil {
		probe.Load(c).Emit(probes.ProbeUnauthorized)
		httperrors.StatusText(c, http.StatusUnauthorized)
		return
	}

	c.Set("user_id", authZ.UserID)
	c.Set("tenant_id", authZ.TenantID)
}

func (h *updateHandler) Handle(c *gin.Context) {
	var (
		id    = c.Param("id")
		req   = c.MustGet("request").(*updateRequest)
		tntID = c.MustGet("tenant_id").(*string)
	)
	var dto dto.UpdateSampleInput
	dto.FromModel(req.UpdateSampleInput)
	err := h.repo.UpdateSample(id, dto, tntID)
	if err != nil {
		if errors.Is(err, repo.ErrNotFound) {
			httperrors.NotFound(c, "%s not found", id)
			return
		}

		probe.Load(c).
			WithError(err).
			WithField("func", "UpdateSample").
			Emit(probes.RepoError)
		httperrors.InternalServerError(c, "unexpected error while updating sample")
	}

	s, err := h.repo.FetchSample(id, nil)
	if err != nil {
		if errors.Is(err, repo.ErrNotFound) {
			httperrors.NotFound(c, "%s not found", id)
			return
		}

		probe.Load(c).
			WithError(err).
			WithField("func", "FetchSample").
			Emit(probes.RepoError)
		httperrors.InternalServerError(c, "unexpected error while fetching updated sample")
		return
	}

	c.JSON(http.StatusOK, s.ToModel())
}

func (h *updateHandler) Chain() []gin.HandlerFunc {
	return []gin.HandlerFunc{
		h.Validate,
		h.Authorize,
		h.Handle,
	}
}

func (r *updateRequest) bindAndValidate(c *gin.Context) bool {
	if err := c.BindJSON(r); err != nil {
		httperrors.BadRequest(c, "Failed to bind body as JSON: %v ", err)
		return false
	}
	if r.Name == "" {
		httperrors.BadRequest(c, "name should be present")
		return false
	}
	return true
}
