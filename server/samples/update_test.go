package samples_test

import (
	"encoding/json"
	"fmt"
	"net/http"

	"github.com/gin-gonic/gin"

	"github.com/BackOfficeAssoc/catalog/fixtures"
	"github.com/BackOfficeAssoc/catalog/models"
	"github.com/BackOfficeAssoc/catalog/repo"
	"github.com/BackOfficeAssoc/catalog/repo/dto"
	"github.com/BackOfficeAssoc/catalog/server/samples"
	authorizertesting "github.com/BackOfficeAssoc/qzar/pkg/authorizer/authorizer_testing"
)

func (s *ChainTestSuite) TestUpdateHandler() {
	var (
		tntID      = "tnt_1"
		id         = "foo"
		mockSample = dto.Sample{
			ID:        id,
			CreatedBy: "pcp_1",
			Name:      "Bar",
			TenantID:  "tnt_1",
			Type:      "Foo",
		}
	)

	type reqData struct {
		Name string `json:"name"`
	}

	tests := []struct {
		name       string
		userID     string
		tntID      *string
		id         string
		req        reqData
		updateErr  error
		fetchErr   error
		respStatus int
	}{
		{
			name:   "ok",
			userID: "pcp_1",
			tntID:  &tntID,
			id:     "foo",
			req: reqData{
				Name: "Bar",
			},
			respStatus: http.StatusOK,
		},
		{
			name:   "ok-internal-authz",
			userID: "pcp_1",
			id:     "foo",
			req: reqData{
				Name: "Bar",
			},
			respStatus: http.StatusOK,
		},
		{
			name:       "empty-name",
			userID:     "pcp_1",
			id:         "foo",
			respStatus: http.StatusBadRequest,
		},
		{
			name:   "update-not-found",
			userID: "pcp_1",
			id:     "foo",
			req: reqData{

				Name: "Bar",
			},
			updateErr:  repo.ErrNotFound,
			respStatus: http.StatusNotFound,
		},
		{
			name:   "update-repo-error",
			userID: "pcp_1",
			id:     "foo",
			req: reqData{

				Name: "Bar",
			},
			updateErr:  fmt.Errorf("unexpected error"),
			respStatus: http.StatusInternalServerError,
		},
		{
			name:   "fetch-after-update-not-found",
			userID: "pcp_1",
			id:     "foo",
			req: reqData{

				Name: "Bar",
			},
			fetchErr:   repo.ErrNotFound,
			respStatus: http.StatusNotFound,
		},
		{
			name:   "fetch-after-update-not-found",
			userID: "pcp_1",
			id:     "foo",
			req: reqData{

				Name: "Bar",
			},
			fetchErr:   fmt.Errorf("unexpected error"),
			respStatus: http.StatusInternalServerError,
		},
		{
			name: "no-auth",
			id:   "foo",
			req: reqData{
				Name: "Bar",
			},
			respStatus: http.StatusUnauthorized,
		},
	}
	for _, test := range tests {
		s.Run(test.name, func() {
			repo := &fixtures.RepoMock{
				UpdateSampleFunc: func(string, dto.UpdateSampleInput, *string) error {
					return test.updateErr
				},
				FetchSampleFunc: func(string, *string) (dto.Sample, error) {
					return mockSample, test.fetchErr
				},
			}

			authz := authorizertesting.New(test.userID)
			if test.tntID != nil {
				authz.WithTenant(test.tntID)
			}
			mw := []gin.HandlerFunc{authz.Middleware()}
			handlers := append(mw, samples.Update(repo)...)

			w, err := s.NewRequest("PUT").
				WithPath("id", test.id).
				WithJSON(&test.req).
				Send(handlers...)
			s.Require().NoError(err, "failed to send olives request")
			if !s.Equal(test.respStatus, w.Code) {
				s.FailNow(string(w.Body.Bytes()))
			}
			if test.respStatus != http.StatusOK {
				return
			}

			var respBody models.Sample
			if err := json.Unmarshal(w.Body.Bytes(), &respBody); !s.NoError(err) {
				return
			}

			s.Equal(mockSample.ID, respBody.ID)
			s.Equal(mockSample.CreatedBy, respBody.CreatedBy)
			s.Equal(mockSample.Name, respBody.Name)
			s.Equal(mockSample.TenantID, respBody.TenantID)

			var st models.SampleType
			st.Parse(mockSample.Type)
			s.Equal(st, respBody.Type)

			if calls := repo.UpdateSampleCalls(); s.Equal(1, len(calls), "UpdateSample calls") {
				s.Equal(test.id, calls[0].ID, "ID passed to UpdateSample")
				s.Equal(test.req.Name, calls[0].Request.Name, "Request.Name")
				if test.tntID == nil {
					s.Nil(calls[0].TenantID, "TenantID passed to UpdateSample")
				} else {
					s.Require().NotNil(calls[0].TenantID, "TenantID passed to UpdateSample")
					s.Equal(test.tntID, calls[0].TenantID, "TenantID passed to UpdateSample")
				}
			}
			if calls := repo.FetchSampleCalls(); s.Equal(1, len(calls), "FetchSample calls") {
				s.Equal(test.id, calls[0].ID, "ID passed to FetchSample")
				s.Nil(calls[0].TenantID, "TenantID passed to FetchSample")
			}
		})
	}
}
