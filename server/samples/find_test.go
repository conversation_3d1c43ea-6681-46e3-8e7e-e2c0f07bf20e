package samples_test

import (
	"encoding/json"
	"fmt"
	"net/http"

	"github.com/gin-gonic/gin"

	"github.com/BackOfficeAssoc/catalog/fixtures"
	"github.com/BackOfficeAssoc/catalog/models"
	"github.com/BackOfficeAssoc/catalog/repo/dto"
	"github.com/BackOfficeAssoc/catalog/server/samples"
	authorizertesting "github.com/BackOfficeAssoc/qzar/pkg/authorizer/authorizer_testing"
)

func (s *ChainTestSuite) TestFindHandler() {
	var (
		tntID      = "tnt_1"
		id         = "foo"
		mockSample = dto.Sample{
			ID:        id,
			CreatedBy: "pcp_1",
			Name:      "Bar",
			TenantID:  "tnt_1",
			Type:      "Foo",
		}
	)

	type reqData struct {
		Name      *string `json:"name"`
		Type      *string `json:"type"`
		CreatedBy *string `json:"created_by"`
		TenantID  *string `json:"tenant_id"`
	}

	tests := []struct {
		name       string
		userID     string
		tntID      *string
		req        reqData
		repoErr    error
		respStatus int
	}{
		{
			name:   "ok",
			userID: "pcp_1",
			tntID:  &tntID,
			req: reqData{
				Name:      fixtures.StrPtr("foo"),
				Type:      fixtures.StrPtr("Foo"),
				CreatedBy: fixtures.StrPtr("pcp_1"),
				TenantID:  fixtures.StrPtr("tnt_ignoreme"),
			},
			respStatus: http.StatusOK,
		},
		{
			name:   "ok-internal-authz",
			userID: "pcp_1",
			req: reqData{
				Name:      fixtures.StrPtr("foo"),
				Type:      fixtures.StrPtr("Foo"),
				CreatedBy: fixtures.StrPtr("pcp_1"),
				TenantID:  fixtures.StrPtr("tnt_2"),
			},
			respStatus: http.StatusOK,
		},
		{
			name:   "invalid-type",
			userID: "pcp_1",
			tntID:  &tntID,
			req: reqData{
				Name:      fixtures.StrPtr("foo"),
				Type:      fixtures.StrPtr("FAKE"),
				CreatedBy: fixtures.StrPtr("pcp_1"),
				TenantID:  fixtures.StrPtr("tnt_ignoreme"),
			},
			respStatus: http.StatusBadRequest,
		},
		{
			name:       "repo-error",
			userID:     "pcp_1",
			tntID:      &tntID,
			repoErr:    fmt.Errorf("unexpected error"),
			respStatus: http.StatusInternalServerError,
		},
		{
			name: "no-auth",
			req: reqData{
				Name:      fixtures.StrPtr("foo"),
				Type:      fixtures.StrPtr("Foo"),
				CreatedBy: fixtures.StrPtr("pcp_1"),
				TenantID:  fixtures.StrPtr("tnt_ignoreme"),
			},
			respStatus: http.StatusUnauthorized,
		},
	}
	for _, test := range tests {
		s.Run(test.name, func() {
			repo := &fixtures.RepoMock{
				FindSamplesFunc: func(dto.SampleQuery) ([]dto.Sample, error) {
					return []dto.Sample{mockSample}, test.repoErr
				},
			}

			authz := authorizertesting.New(test.userID)
			if test.tntID != nil {
				authz.WithTenant(test.tntID)
			}
			mw := []gin.HandlerFunc{authz.Middleware()}
			handlers := append(mw, samples.Find(repo)...)

			req := s.NewRequest("GET")
			if test.req.CreatedBy != nil {
				req = req.WithQuery("created_by", *test.req.CreatedBy)
			}
			if test.req.Name != nil {
				req = req.WithQuery("name", *test.req.Name)
			}
			if test.req.TenantID != nil {
				req = req.WithQuery("tenant_id", *test.req.TenantID)
			}
			if test.req.Type != nil {
				req = req.WithQuery("type", *test.req.Type)
			}
			w, err := req.Send(handlers...)
			s.Require().NoError(err, "failed to send olives request")
			if !s.Equal(test.respStatus, w.Code) {
				s.FailNow(string(w.Body.Bytes()))
			}
			if test.respStatus != http.StatusOK {
				return
			}

			var respList []*models.Sample
			if err := json.Unmarshal(w.Body.Bytes(), &respList); !s.NoError(err) {
				return
			}

			respBody := respList[0]
			s.Equal(mockSample.ID, respBody.ID)
			s.Equal(mockSample.CreatedBy, respBody.CreatedBy)
			s.Equal(mockSample.Name, respBody.Name)
			s.Equal(mockSample.TenantID, respBody.TenantID)

			var st models.SampleType
			st.Parse(mockSample.Type)
			s.Equal(st, respBody.Type)

			if calls := repo.FindSamplesCalls(); s.Equal(1, len(calls), "FetchSample calls") {
				s.Equal(test.req.CreatedBy, calls[0].Query.CreatedBy, "Query.CreatedBy passed to FindSamples")
				s.Equal(test.req.Name, calls[0].Query.Name, "Query.Name passed to FindSamples")
				s.Equal(test.req.Type, calls[0].Query.Type, "Query.Type passed to FindSamples")
				if test.tntID == nil {
					s.Equal(test.req.TenantID, calls[0].Query.TenantID, "Query.TenantID passed to FindSamples")
				} else {
					s.Equal(test.tntID, calls[0].Query.TenantID, "Query.TenantID passed to FindSamples")
				}
			}
		})
	}
}
