package samples_test

import (
	"encoding/json"
	"fmt"
	"net/http"

	"github.com/gin-gonic/gin"

	"github.com/BackOfficeAssoc/catalog/fixtures"
	"github.com/BackOfficeAssoc/catalog/models"
	"github.com/BackOfficeAssoc/catalog/repo"
	"github.com/BackOfficeAssoc/catalog/repo/dto"
	"github.com/BackOfficeAssoc/catalog/server/samples"
	authorizertesting "github.com/BackOfficeAssoc/qzar/pkg/authorizer/authorizer_testing"
)

func (s *ChainTestSuite) TestCreateHandler() {
	var (
		tntID      = "tnt_1"
		id         = "foo"
		mockSample = dto.Sample{
			ID:        id,
			CreatedBy: "pcp_1",
			Name:      "Bar",
			TenantID:  "tnt_1",
			Type:      "Foo",
		}
	)

	type reqData struct {
		Name     string  `json:"name"`
		Type     string  `json:"type"`
		TenantID *string `json:"tenant_id"`
	}

	tests := []struct {
		name       string
		userID     string
		tntID      *string
		req        reqData
		createErr  error
		fetchErr   error
		respStatus int
	}{
		{
			name:   "ok",
			userID: "pcp_1",
			tntID:  &tntID,
			req: reqData{
				Name: "Foo",
				Type: "Foo",
			},
			respStatus: http.StatusOK,
		},
		{
			name:   "ok-ignore-tenant",
			userID: "pcp_1",
			tntID:  &tntID,
			req: reqData{
				Name:     "Foo",
				Type:     "Foo",
				TenantID: fixtures.StrPtr("tnt_0"),
			},
			respStatus: http.StatusOK,
		},
		{
			name:   "ok-internal-authz",
			userID: "pcp_1",
			req: reqData{
				Name:     "Foo",
				Type:     "Foo",
				TenantID: fixtures.StrPtr("tnt_2"),
			},
			respStatus: http.StatusOK,
		},
		{
			name:   "internal-authz-no-tenant",
			userID: "pcp_1",
			req: reqData{
				Name: "Foo",
				Type: "Foo",
			},
			respStatus: http.StatusUnauthorized,
		},
		{
			name:   "empty-name",
			userID: "pcp_1",
			tntID:  &tntID,
			req: reqData{
				Type: "Foo",
			},
			respStatus: http.StatusBadRequest,
		},
		{
			name:   "empty-type",
			userID: "pcp_1",
			tntID:  &tntID,
			req: reqData{
				Name: "Foo",
			},
			respStatus: http.StatusBadRequest,
		},
		{
			name:   "invalid-type",
			userID: "pcp_1",
			tntID:  &tntID,
			req: reqData{
				Name: "Foo",
				Type: "INVALID",
			},
			respStatus: http.StatusBadRequest,
		},
		{
			name:   "create-conflict",
			userID: "pcp_1",
			tntID:  &tntID,
			req: reqData{
				Name: "Foo",
				Type: "Foo",
			},
			createErr:  repo.ErrConflict,
			respStatus: http.StatusConflict,
		},
		{
			name:   "update-repo-error",
			userID: "pcp_1",
			tntID:  &tntID,
			req: reqData{
				Name: "Foo",
				Type: "Foo",
			},
			createErr:  fmt.Errorf("unexpected error"),
			respStatus: http.StatusInternalServerError,
		},
		{
			name:   "fetch-after-create-not-found",
			userID: "pcp_1",
			tntID:  &tntID,
			req: reqData{
				Name: "Foo",
				Type: "Foo",
			},
			fetchErr:   repo.ErrNotFound,
			respStatus: http.StatusNotFound,
		},
		{
			name:   "fetch-after-create-not-found",
			userID: "pcp_1",
			tntID:  &tntID,
			req: reqData{
				Name: "Foo",
				Type: "Foo",
			},
			fetchErr:   fmt.Errorf("unexpected error"),
			respStatus: http.StatusInternalServerError,
		},
		{
			name: "no-auth",
			req: reqData{
				Name: "Foo",
				Type: "Foo",
			},
			respStatus: http.StatusUnauthorized,
		},
	}
	for _, test := range tests {
		s.Run(test.name, func() {
			repo := &fixtures.RepoMock{
				CreateSampleFunc: func(dto.CreateSampleInput, string, string) (string, error) {
					return id, test.createErr
				},
				FetchSampleFunc: func(string, *string) (dto.Sample, error) {
					return mockSample, test.fetchErr
				},
			}

			authz := authorizertesting.New(test.userID)
			if test.tntID != nil {
				authz.WithTenant(test.tntID)
			}
			mw := []gin.HandlerFunc{authz.Middleware()}
			handlers := append(mw, samples.Create(repo)...)

			w, err := s.NewRequest("POST").
				WithJSON(&test.req).
				Send(handlers...)
			s.Require().NoError(err, "failed to send olives request")
			if !s.Equal(test.respStatus, w.Code) {
				s.FailNow(string(w.Body.Bytes()))
			}
			if test.respStatus != http.StatusOK {
				return
			}

			var respBody models.Sample
			err = json.Unmarshal(w.Body.Bytes(), &respBody)
			s.Require().NoError(err, "failed to unmarshal olives request")

			s.Equal(mockSample.ID, respBody.ID)
			s.Equal(mockSample.CreatedBy, respBody.CreatedBy)
			s.Equal(mockSample.Name, respBody.Name)
			s.Equal(mockSample.TenantID, respBody.TenantID)

			var st models.SampleType
			st.Parse(mockSample.Type)
			s.Equal(st, respBody.Type)

			if calls := repo.CreateSampleCalls(); s.Equal(1, len(calls), "CreateSample calls") {
				s.Equal(test.req.Name, calls[0].Request.Name, "Request.Name")
				var st models.SampleType
				_ = st.Parse(test.req.Type)
				s.Equal(st.String(), calls[0].Request.Type, "Request.Type")
				s.Equal(test.userID, calls[0].CreatedBy, "CreatedBy passed to CreateSample")

				expTenant := test.tntID
				if test.tntID == nil && test.req.TenantID != nil {
					expTenant = test.req.TenantID
				}
				s.Equal(*expTenant, calls[0].TenantID, "TenantID passed to CreateSample")
			}
			if calls := repo.FetchSampleCalls(); s.Equal(1, len(calls), "FetchSample calls") {
				s.Equal(id, calls[0].ID, "ID passed to FetchSample")
				s.Nil(calls[0].TenantID, "TenantID passed to FetchSample")
			}
		})
	}
}
