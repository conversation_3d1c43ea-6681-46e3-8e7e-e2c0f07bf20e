package samples

import (
	"errors"
	"net/http"

	"github.com/gin-gonic/gin"

	"github.com/BackOfficeAssoc/catalog/repo"
	"github.com/BackOfficeAssoc/catalog/server/probes"
	"github.com/BackOfficeAssoc/pkg/httperrors"
	"github.com/BackOfficeAssoc/pkg/probe"
	"github.com/BackOfficeAssoc/qzar/pkg/authorizer"
)

type DeleteRepo interface {
	DeleteSample(id string, tenant *string) error
}

func Delete(r DeleteRepo) []gin.HandlerFunc {
	h := deleteHandler{
		repo: r,
	}
	return h.Chain()
}

type deleteHandler struct {
	// Dependencies to be injected...
	repo DeleteRepo
}

func (h *deleteHandler) Authorize(c *gin.Context) {
	authZ, err := authorizer.Output(c)
	if err != nil {
		probe.Load(c).Emit(probes.ProbeUnauthorized)
		httperrors.StatusText(c, http.StatusUnauthorized)
		return
	}

	c.Set("tenant_id", authZ.TenantID)
}

func (h *deleteHandler) Handle(c *gin.Context) {
	var (
		id    = c.Param("id")
		tntID = c.MustGet("tenant_id").(*string)
	)
	err := h.repo.DeleteSample(id, tntID)
	if err != nil {
		if errors.Is(err, repo.ErrNotFound) {
			httperrors.NotFound(c, "%s not found", id)
			return
		}

		probe.Load(c).
			WithError(err).
			WithField("func", "DeleteSample").
			Emit(probes.RepoError)
		httperrors.InternalServerError(c, "unexpected error deleting sample")
		return
	}

	c.Status(http.StatusNoContent)
}

func (h *deleteHandler) Chain() []gin.HandlerFunc {
	return []gin.HandlerFunc{
		h.Authorize,
		h.Handle,
	}
}
