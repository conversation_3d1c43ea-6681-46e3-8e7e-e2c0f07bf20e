package samples

import (
	"net/http"

	"github.com/gin-gonic/gin"

	"github.com/BackOfficeAssoc/catalog/models"
	"github.com/BackOfficeAssoc/catalog/repo/dto"
	"github.com/BackOfficeAssoc/catalog/server/probes"
	"github.com/BackOfficeAssoc/pkg/httperrors"
	"github.com/BackOfficeAssoc/pkg/probe"
	"github.com/BackOfficeAssoc/qzar/pkg/authorizer"
)

type FindRepo interface {
	FindSamples(query dto.SampleQuery) ([]dto.Sample, error)
}

func Find(r FindRepo) []gin.HandlerFunc {
	h := findHandler{
		repo: r,
	}
	return h.Chain()
}

type findHandler struct {
	// Dependencies to be injected...
	repo FindRepo
}

type findRequest struct {
	models.SampleQuery
}

func (h *findHandler) Validate(c *gin.Context) {
	var req findRequest
	if !req.bindAndValidate(c) {
		return
	}
	c.Set("request", &req)
}

func (h *findHandler) Authorize(c *gin.Context) {
	req := c.MustGet("request").(*findRequest)

	authZ, err := authorizer.Output(c)
	if err != nil {
		probe.Load(c).Emit(probes.ProbeUnauthorized)
		httperrors.StatusText(c, http.StatusUnauthorized)
		return
	} else if authZ.UserID == "" {
		probe.Load(c).Emit(probes.ProbeUnauthorized)
		httperrors.StatusText(c, http.StatusUnauthorized)
		return
	} else if authZ.HasGlobalAccess() && req.TenantID == nil {
		probe.Load(c).WithField("user_id", authZ.UserID).Emit(probes.ProbeUnauthorized)
		httperrors.StatusText(c, http.StatusUnauthorized)
		return
	}

	if !authZ.HasGlobalAccess() {
		req.TenantID = authZ.TenantID
	}
}

func (h *findHandler) Handle(c *gin.Context) {
	req := c.MustGet("request").(*findRequest)

	var query dto.SampleQuery
	query.FromModel(req.SampleQuery)
	samples, err := h.repo.FindSamples(query)
	if err != nil {
		httperrors.InternalServerError(c, "")
		return
	}

	resp := make([]models.Sample, len(samples))
	for i := range samples {
		resp[i] = samples[i].ToModel()
	}

	c.JSON(http.StatusOK, resp)
}

func (h *findHandler) Chain() []gin.HandlerFunc {
	return []gin.HandlerFunc{
		h.Validate,
		h.Authorize,
		h.Handle,
	}
}

func (r *findRequest) bindAndValidate(c *gin.Context) bool {
	if err := c.BindQuery(r); err != nil {
		httperrors.BadRequest(c, "Failed to bind query string. Expected: /samples?[name=:name]&[type=:type]")
		return false
	}
	if r.Type != nil {
		var sampleType models.SampleType
		if err := sampleType.Parse(*r.Type); err != nil || sampleType == models.SampleTypeUnknown {
			httperrors.BadRequest(c, "unsupported sample type '%s'", *r.Type)
			return false
		}
		typeStr := sampleType.String()
		r.Type = &typeStr // ensures consistent casing
	}
	return true
}
