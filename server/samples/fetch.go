package samples

import (
	"errors"
	"net/http"

	"github.com/gin-gonic/gin"

	"github.com/BackOfficeAssoc/catalog/repo"
	"github.com/BackOfficeAssoc/catalog/repo/dto"
	"github.com/BackOfficeAssoc/catalog/server/probes"
	"github.com/BackOfficeAssoc/pkg/httperrors"
	"github.com/BackOfficeAssoc/pkg/probe"
	"github.com/BackOfficeAssoc/qzar/pkg/authorizer"
)

type FetchRepo interface {
	FetchSample(id string, tenant *string) (dto.Sample, error)
}

func Fetch(r FetchRepo) []gin.HandlerFunc {
	h := fetchHandler{
		repo: r,
	}
	return h.Chain()
}

type fetchHandler struct {
	// Dependencies to be injected...
	repo FetchRepo
}

func (h *fetchHandler) Authorize(c *gin.Context) {
	authZ, err := authorizer.Output(c)
	if err != nil {
		probe.Load(c).Emit(probes.ProbeUnauthorized)
		httperrors.StatusText(c, http.StatusUnauthorized)
		return
	}

	c.Set("tenant_id", authZ.TenantID)
}

func (h *fetchHandler) Handle(c *gin.Context) {
	var (
		id    = c.Param("id")
		tntID = c.MustGet("tenant_id").(*string)
	)
	dto, err := h.repo.FetchSample(id, tntID)
	if err != nil {
		if errors.Is(err, repo.ErrNotFound) {
			httperrors.NotFound(c, "%s not found", id)
			return
		}

		probe.Load(c).
			WithError(err).
			WithField("func", "FetchSample").
			Emit(probes.RepoError)
		httperrors.InternalServerError(c, "unexpected error fetching sample")
		return
	}

	c.JSON(http.StatusOK, dto.ToModel())
}

func (h *fetchHandler) Chain() []gin.HandlerFunc {
	return []gin.HandlerFunc{
		h.Authorize,
		h.Handle,
	}
}
