package samples_test

import (
	"testing"

	"github.com/stretchr/testify/suite"

	"github.com/BackOfficeAssoc/pkg/olives"
	"github.com/BackOfficeAssoc/pkg/probe/dogpony"
)

type ChainTestSuite struct {
	olives.TestSuite
}

func (suite *ChainTestSuite) SetupSuite() {
	suite.TestSuite.SetupSuite()
	dogpony.NewFunc(suite.T)
}

func (suite *ChainTestSuite) Run(testName string, fn func()) {
	suite.TestSuite.Run(testName, fn)
}

func TestChainSuite(t *testing.T) {
	suite.Run(t, new(ChainTestSuite))
}
