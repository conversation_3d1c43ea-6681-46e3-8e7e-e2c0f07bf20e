{"meta_schema_version": "relational-20250501", "namespace": "ns1", "current_version": "20250201-001", "entities": [{"model_name": "ns1.rq1.ad01c_prof", "name": "AD01C_PROF", "type": "TRANSPARENT TABLE", "consumption_type": "dataset", "description": "DIP profile: Usage", "active": true, "properties": {"contflag": "C", "schema": "rq1"}, "tags": [], "entity_elements": [{"model_name": "ns1.rq1.ad01c_prof.col.mandt", "name": "MANDT", "type": "COLUMN", "consumption_type": "field", "description": "Client", "active": true, "ordinal_position": 1, "data_type": "CLNT", "max_length": 3, "is_required": true, "properties": {}, "custom_properties": {"checktable": "T000", "domname": "MANDT", "entitytab": "T000", "rollname": "MANDT"}}, {"model_name": "ns1.rq1.ad01c_prof.col.profnr", "name": "PROFNR", "type": "COLUMN", "consumption_type": "field", "description": "Dynamic Item Processor Profile", "active": true, "ordinal_position": 2, "data_type": "CHAR", "max_length": 8, "is_required": true, "properties": {}, "custom_properties": {"checktable": "DPPROFH", "domname": "AD01PROFNR", "entitytab": "DPPROFH", "rollname": "AD01PROFNR"}}, {"model_name": "ns1.rq1.ad01c_prof.col.dpus", "name": "DPUS", "type": "COLUMN", "consumption_type": "field", "description": "Usage of the DI profile", "active": true, "ordinal_position": 3, "data_type": "NUMC", "max_length": 2, "is_required": true, "properties": {}, "custom_properties": {"domname": "AD01USAGE", "rollname": "AD01USAGE"}}, {"model_name": "ns1.rq1.ad01c_prof.col.macond", "name": "MACOND", "type": "COLUMN", "consumption_type": "field", "description": "Number of manual conditions", "active": true, "ordinal_position": 4, "data_type": "NUMC", "max_length": 1, "properties": {}, "custom_properties": {"domname": "AD01MACOND", "rollname": "AD01MACOND"}}, {"model_name": "ns1.rq1.ad01c_prof.col.sdauart", "name": "SDAUART", "type": "COLUMN", "consumption_type": "field", "description": "Sales Document Type", "active": true, "ordinal_position": 5, "data_type": "CHAR", "max_length": 4, "properties": {}, "custom_properties": {"checktable": "TVAK", "domname": "AUART", "entitytab": "TVAK", "rollname": "AD01CAUART"}}, {"model_name": "ns1.rq1.ad01c_prof.col.sdauarg", "name": "SDAUARG", "type": "COLUMN", "consumption_type": "field", "description": "Sales Document Type for Credit Memo Request", "active": true, "ordinal_position": 6, "data_type": "CHAR", "max_length": 4, "properties": {}, "custom_properties": {"checktable": "TVAK", "domname": "AUART", "entitytab": "TVAK", "rollname": "AUARG"}}, {"model_name": "ns1.rq1.ad01c_prof.col.wcheck", "name": "WCHECK", "type": "COLUMN", "consumption_type": "field", "description": "Warranty check", "active": true, "ordinal_position": 7, "data_type": "CHAR", "max_length": 1, "properties": {}, "custom_properties": {"domname": "XFELD", "rollname": "WACHK"}}, {"model_name": "ns1.rq1.ad01c_prof.col.fldimat", "name": "FLDIMAT", "type": "COLUMN", "consumption_type": "field", "description": "Process Dynamic Items with Material Only", "active": true, "ordinal_position": 8, "data_type": "CHAR", "max_length": 1, "properties": {}, "custom_properties": {"domname": "XFELD", "rollname": "FLDIMAT"}}, {"model_name": "ns1.rq1.ad01c_prof.col.ad01sdauart", "name": "AD01SDAUART", "type": "COLUMN", "consumption_type": "field", "description": "Sales Document Type", "active": true, "ordinal_position": 9, "data_type": "CHAR", "max_length": 4, "properties": {}, "custom_properties": {"checktable": "TVAK", "domname": "AUART", "entitytab": "TVAK", "rollname": "AUART"}}, {"model_name": "ns1.rq1.ad01c_prof.pk.ad01c.prof", "name": "PK_AD01C_PROF", "type": "PRIMARY-KEY", "consumption_type": "key", "active": true, "data_type": "", "properties": {}}], "tenant_id": "", "id": "", "meta_version": "", "context_id": "", "base_type": "", "ref": "", "mapped_entity": null, "created_at": "", "created_by": "", "version_id": "", "branch_id": ""}, {"model_name": "ns1.rq1.adr2", "name": "ADR2", "type": "TRANSPARENT TABLE", "consumption_type": "dataset", "description": "Telephone Numbers (Business Address Services)", "active": true, "properties": {"contflag": "A", "schema": "rq1"}, "tags": [], "entity_elements": [{"model_name": "ns1.rq1.adr2.col.client", "name": "CLIENT", "type": "COLUMN", "consumption_type": "field", "description": "Client", "active": true, "ordinal_position": 1, "data_type": "CLNT", "max_length": 3, "is_required": true, "properties": {}, "custom_properties": {"checktable": "T000", "domname": "MANDT", "entitytab": "T000", "rollname": "MANDT"}}, {"model_name": "ns1.rq1.adr2.col.addrnumber", "name": "ADDRNUMBER", "type": "COLUMN", "consumption_type": "field", "description": "Address number", "active": true, "ordinal_position": 2, "data_type": "CHAR", "max_length": 10, "is_required": true, "properties": {}, "custom_properties": {"checktable": "ADRC", "domname": "AD_ADDRNUM", "entitytab": "ADRC", "rollname": "AD_ADDRNUM"}}, {"model_name": "ns1.rq1.adr2.col.persnumber", "name": "PERSNUMBER", "type": "COLUMN", "consumption_type": "field", "description": "Person number", "active": true, "ordinal_position": 3, "data_type": "CHAR", "max_length": 10, "is_required": true, "properties": {}, "custom_properties": {"checktable": "ADRP", "domname": "AD_PERSNUM", "entitytab": "ADRP", "rollname": "AD_PERSNUM"}}, {"model_name": "ns1.rq1.adr2.col.date.from", "name": "DATE_FROM", "type": "COLUMN", "consumption_type": "field", "description": "Valid-from date - in current Release only 00010101 possible", "active": true, "ordinal_position": 4, "data_type": "DATS", "max_length": 8, "is_required": true, "properties": {}, "custom_properties": {"domname": "DATUM", "rollname": "AD_DATE_FR"}}, {"model_name": "ns1.rq1.adr2.col.consnumber", "name": "CONSNUMBER", "type": "COLUMN", "consumption_type": "field", "description": "Sequence number", "active": true, "ordinal_position": 5, "data_type": "NUMC", "max_length": 3, "is_required": true, "properties": {}, "custom_properties": {"domname": "NUMC3", "rollname": "AD_CONSNUM"}}, {"model_name": "ns1.rq1.adr2.col.country", "name": "COUNTRY", "type": "COLUMN", "consumption_type": "field", "description": "Country for telephone/fax number", "active": true, "ordinal_position": 6, "data_type": "CHAR", "max_length": 3, "properties": {}, "custom_properties": {"checktable": "T005", "domname": "LAND1", "entitytab": "T005", "rollname": "AD_COMCTRY"}}, {"model_name": "ns1.rq1.adr2.col.flgdefault", "name": "FLGDEFAULT", "type": "COLUMN", "consumption_type": "field", "description": "Standard Sender Address in this Communication Type", "active": true, "ordinal_position": 7, "data_type": "CHAR", "max_length": 1, "properties": {}, "custom_properties": {"domname": "XFELD", "rollname": "AD_FLGDFNR"}}, {"model_name": "ns1.rq1.adr2.col.flg.nouse", "name": "FLG_NOUSE", "type": "COLUMN", "consumption_type": "field", "description": "Flag: This Communication Number is Not Used", "active": true, "ordinal_position": 8, "data_type": "CHAR", "max_length": 1, "properties": {}, "custom_properties": {"domname": "XFELD", "rollname": "AD_FLNOUSE"}}, {"model_name": "ns1.rq1.adr2.col.home.flag", "name": "HOME_FLAG", "type": "COLUMN", "consumption_type": "field", "description": "Recipient address in this communication type (mail sys.grp)", "active": true, "ordinal_position": 9, "data_type": "CHAR", "max_length": 1, "properties": {}, "custom_properties": {"domname": "XFELD", "rollname": "AD_FLGHOME"}}, {"model_name": "ns1.rq1.adr2.col.tel.number", "name": "TEL_NUMBER", "type": "COLUMN", "consumption_type": "field", "description": "Telephone no.: dialling code+number", "active": true, "ordinal_position": 10, "data_type": "CHAR", "max_length": 30, "properties": {}, "custom_properties": {"domname": "CHAR30", "rollname": "AD_TLNMBR"}}, {"model_name": "ns1.rq1.adr2.col.tel.extens", "name": "TEL_EXTENS", "type": "COLUMN", "consumption_type": "field", "description": "Telephone no.: Extension", "active": true, "ordinal_position": 11, "data_type": "CHAR", "max_length": 10, "properties": {}, "custom_properties": {"domname": "CHAR10", "rollname": "AD_TLXTNS"}}, {"model_name": "ns1.rq1.adr2.col.telnr.long", "name": "TELNR_LONG", "type": "COLUMN", "consumption_type": "field", "description": "Complete Number: Dialling Code+Number+Extension", "active": true, "ordinal_position": 12, "data_type": "CHAR", "max_length": 30, "properties": {}, "custom_properties": {"domname": "CHAR30", "rollname": "AD_TELNRLG"}}, {"model_name": "ns1.rq1.adr2.col.telnr.call", "name": "TELNR_CALL", "type": "COLUMN", "consumption_type": "field", "description": "Telephone number for determining caller", "active": true, "ordinal_position": 13, "data_type": "CHAR", "max_length": 30, "properties": {}, "custom_properties": {"domname": "CHAR30", "rollname": "AD_TELNRCL"}}, {"model_name": "ns1.rq1.adr2.col.dft.receiv", "name": "DFT_RECEIV", "type": "COLUMN", "consumption_type": "field", "description": "Indicator: Telephone is SMS-Enabled", "active": true, "ordinal_position": 14, "data_type": "CHAR", "max_length": 1, "properties": {}, "custom_properties": {"domname": "AD_FLGSMS", "rollname": "AD_FLGSMS"}}, {"model_name": "ns1.rq1.adr2.col.r3.user", "name": "R3_USER", "type": "COLUMN", "consumption_type": "field", "description": "Indicator: Telephone is a Mobile Telephone", "active": true, "ordinal_position": 15, "data_type": "CHAR", "max_length": 1, "properties": {}, "custom_properties": {"domname": "AD_FLGMOB", "rollname": "AD_FLGMOB"}}, {"model_name": "ns1.rq1.adr2.col.valid.from", "name": "VALID_FROM", "type": "COLUMN", "consumption_type": "field", "description": "Communication Data: Valid From (YYYYMMDDHHMMSS)", "active": true, "ordinal_position": 16, "data_type": "CHAR", "max_length": 14, "properties": {}, "custom_properties": {"domname": "TIMESTMP", "rollname": "AD_VALFROM"}}, {"model_name": "ns1.rq1.adr2.col.valid.to", "name": "VALID_TO", "type": "COLUMN", "consumption_type": "field", "description": "Communication Data: Valid To (YYYYMMDDHHMMSS)", "active": true, "ordinal_position": 17, "data_type": "CHAR", "max_length": 14, "properties": {}, "custom_properties": {"domname": "TIMESTAMP", "rollname": "AD_VALTO"}}, {"model_name": "ns1.rq1.adr2.pk.adr2", "name": "PK_ADR2", "type": "PRIMARY-KEY", "consumption_type": "key", "active": true, "data_type": "", "properties": {}}], "tenant_id": "", "id": "", "meta_version": "", "context_id": "", "base_type": "", "ref": "", "mapped_entity": null, "created_at": "", "created_by": "", "version_id": "", "branch_id": ""}, {"model_name": "ns1.rq1.at40", "name": "AT40", "type": "POOLED TABLE", "consumption_type": "dataset", "description": "Calculation Categories of Cash Flow Calculator", "active": true, "properties": {"contflag": "S", "schema": "rq1"}, "tags": [], "entity_elements": [{"model_name": "ns1.rq1.at40.col.sberfima", "name": "SBERFIMA", "type": "COLUMN", "consumption_type": "field", "description": "Calculation Category for Cash Flow Calculator", "active": true, "ordinal_position": 1, "data_type": "CHAR", "max_length": 4, "is_required": true, "properties": {}, "custom_properties": {"domname": "SBEWFIMA", "entitytab": "AT40", "rollname": "SBEWFIMA"}}, {"model_name": "ns1.rq1.at40.col.ssortfima", "name": "SSORTFIMA", "type": "COLUMN", "consumption_type": "field", "description": "Sort indicator for financial mathematic calculations", "active": true, "ordinal_position": 2, "data_type": "NUMC", "max_length": 2, "properties": {}, "custom_properties": {"domname": "NUMC2", "rollname": "TFM_SORT"}}, {"model_name": "ns1.rq1.at40.col.svon", "name": "SVON", "type": "COLUMN", "consumption_type": "field", "description": "Int. FiMa indicator to interprete DVALUT/SINCL", "active": true, "ordinal_position": 3, "data_type": "CHAR", "max_length": 1, "properties": {}, "custom_properties": {"domname": "XFELD", "rollname": "TFM_SVON"}}, {"model_name": "ns1.rq1.at40.pk.at40", "name": "PK_AT40", "type": "PRIMARY-KEY", "consumption_type": "key", "active": true, "data_type": "", "properties": {}}], "tenant_id": "", "id": "", "meta_version": "", "context_id": "", "base_type": "", "ref": "", "mapped_entity": null, "created_at": "", "created_by": "", "version_id": "", "branch_id": ""}, {"model_name": "ns1.rq1.sprt1", "name": "SPRT1", "type": "INTTAB", "consumption_type": "dataset", "description": "Partner table", "active": true, "properties": {"schema": "rq1"}, "tags": [], "entity_elements": [{"model_name": "ns1.rq1.sprt1.col.mandt", "name": "MANDT", "type": "COLUMN", "consumption_type": "field", "description": "Client", "active": true, "ordinal_position": 1, "data_type": "CLNT", "max_length": 3, "is_required": true, "properties": {}, "custom_properties": {"checktable": "T000", "domname": "MANDT", "entitytab": "T000", "rollname": "MANDT"}}, {"model_name": "ns1.rq1.sprt1.col.partnr", "name": "PARTNR", "type": "COLUMN", "consumption_type": "field", "description": "Partner number", "active": true, "ordinal_position": 2, "data_type": "CHAR", "max_length": 10, "is_required": true, "properties": {}, "custom_properties": {"domname": "SPARTNR", "entitytab": "SPRT1", "rollname": "SPARTNR"}}, {"model_name": "ns1.rq1.sprt1.col.kzptp", "name": "KZPTP", "type": "COLUMN", "consumption_type": "field", "description": "Person type indicator (legal or natural)", "active": true, "ordinal_position": 3, "data_type": "CHAR", "max_length": 1, "properties": {}, "custom_properties": {"domname": "XFELD", "rollname": "KZPTP"}}, {"model_name": "ns1.rq1.sprt1.col.begru", "name": "BEGRU", "type": "COLUMN", "consumption_type": "field", "description": "Authorization Group", "active": true, "ordinal_position": 4, "data_type": "CHAR", "max_length": 4, "properties": {}, "custom_properties": {"checktable": "TBRG", "domname": "BRGRU", "entitytab": "TBRG", "rollname": "BRGRU"}}, {"model_name": "ns1.rq1.sprt1.col.cntry.h", "name": "CNTRY_H", "type": "COLUMN", "consumption_type": "field", "description": "Country of origin", "active": true, "ordinal_position": 5, "data_type": "CHAR", "max_length": 3, "properties": {}, "custom_properties": {"checktable": "T005", "domname": "LAND1", "entitytab": "T005", "rollname": "CNTRY_H"}}, {"model_name": "ns1.rq1.sprt1.col.sprsl", "name": "SPRSL", "type": "COLUMN", "consumption_type": "field", "description": "Language Key", "active": true, "ordinal_position": 6, "data_type": "LANG", "max_length": 1, "properties": {}, "custom_properties": {"checktable": "T002", "domname": "SPRAS", "entitytab": "T002", "languflag": "X", "rollname": "SPRSL"}}, {"model_name": "ns1.rq1.sprt1.col.name1", "name": "NAME1", "type": "COLUMN", "consumption_type": "field", "description": "Name 1", "active": true, "ordinal_position": 7, "data_type": "CHAR", "max_length": 35, "properties": {}, "custom_properties": {"domname": "NAME", "rollname": "NAME1_GP"}}, {"model_name": "ns1.rq1.sprt1.col.name2", "name": "NAME2", "type": "COLUMN", "consumption_type": "field", "description": "Name 2", "active": true, "ordinal_position": 8, "data_type": "CHAR", "max_length": 35, "properties": {}, "custom_properties": {"domname": "NAME", "rollname": "NAME2_GP"}}, {"model_name": "ns1.rq1.sprt1.col.anred", "name": "ANRED", "type": "COLUMN", "consumption_type": "field", "description": "Form-of-Address Key", "active": true, "ordinal_position": 9, "data_type": "NUMC", "max_length": 2, "properties": {}, "custom_properties": {"checktable": "TZV01", "domname": "SANRED", "entitytab": "TZV01", "rollname": "SANRED"}}, {"model_name": "ns1.rq1.sprt1.col.xanred", "name": "XANRED", "type": "COLUMN", "consumption_type": "field", "description": "Form of address text", "active": true, "ordinal_position": 10, "data_type": "CHAR", "max_length": 50, "properties": {}, "custom_properties": {"domname": "TEXT50", "rollname": "XANRED"}}, {"model_name": "ns1.rq1.sprt1.col.anrbf", "name": "ANRBF", "type": "COLUMN", "consumption_type": "field", "description": "Salutation key", "active": true, "ordinal_position": 11, "data_type": "NUMC", "max_length": 2, "properties": {}, "custom_properties": {"checktable": "TZV02", "domname": "SANRBF", "entitytab": "TZV02", "rollname": "SANRBF"}}, {"model_name": "ns1.rq1.sprt1.col.xanrbf", "name": "XANRBF", "type": "COLUMN", "consumption_type": "field", "description": "Translation for saluation key", "active": true, "ordinal_position": 12, "data_type": "CHAR", "max_length": 50, "properties": {}, "custom_properties": {"domname": "TEXT50", "rollname": "XANRBF"}}, {"model_name": "ns1.rq1.sprt1.col.sortl", "name": "SORTL", "type": "COLUMN", "consumption_type": "field", "description": "Partner ID", "active": true, "ordinal_position": 13, "data_type": "CHAR", "max_length": 10, "properties": {}, "custom_properties": {"domname": "CHAR10", "rollname": "PARTID"}}, {"model_name": "ns1.rq1.sprt1.col.phsrt", "name": "PHSRT", "type": "COLUMN", "consumption_type": "field", "description": "Phonetic search term", "active": true, "ordinal_position": 14, "data_type": "CHAR", "max_length": 20, "properties": {}, "custom_properties": {"domname": "CHAR20", "rollname": "PHSRT"}}, {"model_name": "ns1.rq1.sprt1.col.partid", "name": "PARTID", "type": "COLUMN", "consumption_type": "field", "description": "IS-IS: Partner identification", "active": true, "ordinal_position": 15, "data_type": "CHAR", "max_length": 15, "properties": {}, "custom_properties": {"domname": "CHAR15", "rollname": "VVXPARTID"}}, {"model_name": "ns1.rq1.sprt1.col.name1.mc", "name": "NAME1_MC", "type": "COLUMN", "consumption_type": "field", "description": "IS-IS: Name 1 in upper case characters for matchcode search", "active": true, "ordinal_position": 16, "data_type": "CHAR", "max_length": 16, "properties": {}, "custom_properties": {"domname": "CHAR16", "rollname": "VVXNAME1MC"}}, {"model_name": "ns1.rq1.sprt1.col.flg.anrbf", "name": "FLG_ANRBF", "type": "COLUMN", "consumption_type": "field", "description": "ID for alternative salutations", "active": true, "ordinal_position": 17, "data_type": "CHAR", "max_length": 1, "properties": {}, "custom_properties": {"domname": "XFELD", "rollname": "VVSABWBA"}}, {"model_name": "ns1.rq1.sprt1.col.flg.mitarb", "name": "FLG_MITARB", "type": "COLUMN", "consumption_type": "field", "description": "IS-IS: Indicator, partner is employee", "active": true, "ordinal_position": 18, "data_type": "CHAR", "max_length": 1, "properties": {}, "custom_properties": {"domname": "XFELD", "rollname": "VVSMITARB"}}, {"model_name": "ns1.rq1.sprt1.pk.sprt1", "name": "PK_SPRT1", "type": "PRIMARY-KEY", "consumption_type": "key", "active": true, "data_type": "", "properties": {}}], "tenant_id": "", "id": "", "meta_version": "", "context_id": "", "base_type": "", "ref": "", "mapped_entity": null, "created_at": "", "created_by": "", "version_id": "", "branch_id": ""}, {"model_name": "ns1.rq1.t001s", "name": "T001S", "type": "POOLED TABLE", "consumption_type": "dataset", "description": "Accounting Clerks", "active": true, "properties": {"contflag": "C", "schema": "rq1"}, "tags": [], "entity_elements": [{"model_name": "ns1.rq1.t001s.col.mandt", "name": "MANDT", "type": "COLUMN", "consumption_type": "field", "description": "Client", "active": true, "ordinal_position": 1, "data_type": "CLNT", "max_length": 3, "is_required": true, "properties": {}, "custom_properties": {"checktable": "T000", "domname": "MANDT", "entitytab": "T000", "rollname": "MANDT"}}, {"model_name": "ns1.rq1.t001s.col.bukrs", "name": "BUKRS", "type": "COLUMN", "consumption_type": "field", "description": "Company Code", "active": true, "ordinal_position": 2, "data_type": "CHAR", "max_length": 4, "is_required": true, "properties": {}, "custom_properties": {"checktable": "T001", "domname": "BUKRS", "entitytab": "T001", "rollname": "BUKRS"}}, {"model_name": "ns1.rq1.t001s.col.busab", "name": "BUSAB", "type": "COLUMN", "consumption_type": "field", "description": "Accounting clerk", "active": true, "ordinal_position": 3, "data_type": "CHAR", "max_length": 2, "is_required": true, "properties": {}, "custom_properties": {"domname": "BUSAB", "entitytab": "T001S", "rollname": "BUSAB"}}, {"model_name": "ns1.rq1.t001s.col.sname", "name": "SNAME", "type": "COLUMN", "consumption_type": "field", "description": "Name of Accounting Clerk", "active": true, "ordinal_position": 4, "data_type": "CHAR", "max_length": 30, "properties": {}, "custom_properties": {"domname": "TEXT30", "rollname": "SNAME_001S"}}, {"model_name": "ns1.rq1.t001s.col.usnam", "name": "USNAM", "type": "COLUMN", "consumption_type": "field", "description": "Name of SAP Office User", "active": true, "ordinal_position": 5, "data_type": "CHAR", "max_length": 12, "properties": {}, "custom_properties": {"domname": "USERNAME", "rollname": "FI_USR_NAM"}}, {"model_name": "ns1.rq1.t001s.pk.t001s", "name": "PK_T001S", "type": "PRIMARY-KEY", "consumption_type": "key", "active": true, "data_type": "", "properties": {}}], "tenant_id": "", "id": "", "meta_version": "", "context_id": "", "base_type": "", "ref": "", "mapped_entity": null, "created_at": "", "created_by": "", "version_id": "", "branch_id": ""}, {"model_name": "ns1.rq1.t006", "name": "T006", "type": "TRANSPARENT TABLE", "consumption_type": "dataset", "description": "Units of Measurement", "active": true, "properties": {"contflag": "C", "schema": "rq1"}, "tags": [], "entity_elements": [{"model_name": "ns1.rq1.t006.col.mandt", "name": "MANDT", "type": "COLUMN", "consumption_type": "field", "description": "Client", "active": true, "ordinal_position": 1, "data_type": "CLNT", "max_length": 3, "is_required": true, "properties": {}, "custom_properties": {"checktable": "T000", "domname": "MANDT", "entitytab": "T000", "rollname": "MANDT"}}, {"model_name": "ns1.rq1.t006.col.msehi", "name": "MSEHI", "type": "COLUMN", "consumption_type": "field", "description": "Unit of Measurement", "active": true, "ordinal_position": 2, "data_type": "UNIT", "max_length": 3, "is_required": true, "properties": {}, "custom_properties": {"domname": "MEINS", "entitytab": "T006", "rollname": "MSEHI"}}, {"model_name": "ns1.rq1.t006.col.kzex3", "name": "KZEX3", "type": "COLUMN", "consumption_type": "field", "description": "3-char indicator for external unit of measurement", "active": true, "ordinal_position": 3, "data_type": "CHAR", "max_length": 1, "properties": {}, "custom_properties": {"domname": "X", "rollname": "KZEX3"}}, {"model_name": "ns1.rq1.t006.col.kzex6", "name": "KZEX6", "type": "COLUMN", "consumption_type": "field", "description": "6-char. ID for external unit of measurement", "active": true, "ordinal_position": 4, "data_type": "CHAR", "max_length": 1, "properties": {}, "custom_properties": {"domname": "X", "rollname": "KZEX6"}}, {"model_name": "ns1.rq1.t006.col.andec", "name": "ANDEC", "type": "COLUMN", "consumption_type": "field", "description": "No. of decimal places to which rounding should be performed", "active": true, "ordinal_position": 5, "data_type": "INT2", "max_length": 5, "properties": {}, "custom_properties": {"domname": "ANDEC", "rollname": "ANDEC"}}, {"model_name": "ns1.rq1.t006.col.kzkeh", "name": "KZKEH", "type": "COLUMN", "consumption_type": "field", "description": "Commercial measurement unit ID", "active": true, "ordinal_position": 6, "data_type": "CHAR", "max_length": 1, "properties": {}, "custom_properties": {"domname": "X", "rollname": "KZKEH"}}, {"model_name": "ns1.rq1.t006.col.kzwob", "name": "KZWOB", "type": "COLUMN", "consumption_type": "field", "description": "Value-based commitment indicator", "active": true, "ordinal_position": 7, "data_type": "CHAR", "max_length": 1, "properties": {}, "custom_properties": {"domname": "X", "rollname": "KZWOB"}}, {"model_name": "ns1.rq1.t006.col.kz1eh", "name": "KZ1EH", "type": "COLUMN", "consumption_type": "field", "description": "Indicator (1) unit (indicator not yet defined)", "active": true, "ordinal_position": 8, "data_type": "CHAR", "max_length": 1, "properties": {}, "custom_properties": {"domname": "X", "rollname": "KZ1EH"}}, {"model_name": "ns1.rq1.t006.col.kz2eh", "name": "KZ2EH", "type": "COLUMN", "consumption_type": "field", "description": "Indicator (2) unit (indicator not yet defined)", "active": true, "ordinal_position": 9, "data_type": "CHAR", "max_length": 1, "properties": {}, "custom_properties": {"domname": "X", "rollname": "KZ2EH"}}, {"model_name": "ns1.rq1.t006.col.dimid", "name": "DIMID", "type": "COLUMN", "consumption_type": "field", "description": "Dimension key", "active": true, "ordinal_position": 10, "data_type": "CHAR", "max_length": 6, "properties": {}, "custom_properties": {"checktable": "T006D", "domname": "DIMID", "entitytab": "T006D", "rollname": "DIMID"}}, {"model_name": "ns1.rq1.t006.col.z<PERSON>hl", "name": "ZAEHL", "type": "COLUMN", "consumption_type": "field", "description": "Numerator for conversion to SI unit", "active": true, "ordinal_position": 11, "data_type": "INT4", "max_length": 10, "properties": {}, "custom_properties": {"domname": "INT4", "rollname": "DZAEHL"}}, {"model_name": "ns1.rq1.t006.col.nennr", "name": "NENNR", "type": "COLUMN", "consumption_type": "field", "description": "Denominator for conversion into SI unit", "active": true, "ordinal_position": 12, "data_type": "INT4", "max_length": 10, "properties": {}, "custom_properties": {"domname": "INT4", "rollname": "NENNR"}}, {"model_name": "ns1.rq1.t006.col.exp10", "name": "EXP10", "type": "COLUMN", "consumption_type": "field", "description": "base ten exponent for conversion to SI unit", "active": true, "ordinal_position": 13, "data_type": "INT2", "max_length": 5, "properties": {}, "custom_properties": {"domname": "EXP10", "rollname": "EXP10"}}, {"model_name": "ns1.rq1.t006.col.addko", "name": "ADDKO", "type": "COLUMN", "consumption_type": "field", "description": "Additive constant for conversion to SI unit", "active": true, "ordinal_position": 14, "data_type": "DEC", "scale": 6, "max_length": 9, "properties": {}, "custom_properties": {"domname": "ADDKO", "rollname": "ADDKO"}}, {"model_name": "ns1.rq1.t006.col.expon", "name": "EXPON", "type": "COLUMN", "consumption_type": "field", "description": "Base ten exponent for floating-point display", "active": true, "ordinal_position": 15, "data_type": "INT2", "max_length": 5, "properties": {}, "custom_properties": {"domname": "EXP10", "rollname": "EXPON"}}, {"model_name": "ns1.rq1.t006.col.decan", "name": "DECAN", "type": "COLUMN", "consumption_type": "field", "description": "Number of decimal places for number display", "active": true, "ordinal_position": 16, "data_type": "INT2", "max_length": 5, "properties": {}, "custom_properties": {"domname": "DECAN", "rollname": "DECAN"}}, {"model_name": "ns1.rq1.t006.col.isocode", "name": "ISOCODE", "type": "COLUMN", "consumption_type": "field", "description": "ISO code for unit of measurement", "active": true, "ordinal_position": 17, "data_type": "CHAR", "max_length": 3, "properties": {}, "custom_properties": {"checktable": "T006I", "domname": "ISOCD_UNIT", "entitytab": "T006I", "rollname": "ISOCD_UNIT"}}, {"model_name": "ns1.rq1.t006.col.primary", "name": "PRIMARY", "type": "COLUMN", "consumption_type": "field", "description": "Selection field for conversion from ISO code to int. code", "active": true, "ordinal_position": 18, "data_type": "CHAR", "max_length": 1, "properties": {}, "custom_properties": {"domname": "X", "rollname": "ISOCD_MARK"}}, {"model_name": "ns1.rq1.t006.col.temp.value", "name": "TEMP_VALUE", "type": "COLUMN", "consumption_type": "field", "description": "Temperature", "active": true, "ordinal_position": 19, "data_type": "FLTP", "scale": 16, "max_length": 16, "properties": {}, "custom_properties": {"domname": "TEMP_VALUE", "rollname": "TEMP_VALUE"}}, {"model_name": "ns1.rq1.t006.col.temp.unit", "name": "TEMP_UNIT", "type": "COLUMN", "consumption_type": "field", "description": "Temperature unit", "active": true, "ordinal_position": 20, "data_type": "UNIT", "max_length": 3, "properties": {}, "custom_properties": {"checktable": "T006", "domname": "MEINS", "entitytab": "T006", "rollname": "TEMP_UNIT"}}, {"model_name": "ns1.rq1.t006.ct_t006_temp_unit_t006", "name": "CT_T006_TEMP_UNIT_T006", "type": "CHECK-TABLE", "consumption_type": "lookup", "active": true, "data_type": "", "properties": {}}, {"model_name": "ns1.rq1.t006.col.famunit", "name": "FAMUNIT", "type": "COLUMN", "consumption_type": "field", "description": "Unit of measurement family", "active": true, "ordinal_position": 21, "data_type": "CHAR", "max_length": 1, "properties": {}, "custom_properties": {"domname": "CHAR1", "rollname": "FAMUNIT"}}, {"model_name": "ns1.rq1.t006.col.press.val", "name": "PRESS_VAL", "type": "COLUMN", "consumption_type": "field", "description": "Pressure Value", "active": true, "ordinal_position": 22, "data_type": "FLTP", "scale": 16, "max_length": 16, "properties": {}, "custom_properties": {"domname": "TEMP_VALUE", "rollname": "PRESS_VAL"}}, {"model_name": "ns1.rq1.t006.col.press.unit", "name": "PRESS_UNIT", "type": "COLUMN", "consumption_type": "field", "description": "Unit of Pressure", "active": true, "ordinal_position": 23, "data_type": "UNIT", "max_length": 3, "properties": {}, "custom_properties": {"checktable": "T006", "domname": "MEINS", "entitytab": "T006", "rollname": "PRESS_UNIT"}}, {"model_name": "ns1.rq1.t006.pk.t006", "name": "PK_T006", "type": "PRIMARY-KEY", "consumption_type": "key", "active": true, "data_type": "", "properties": {}}], "tenant_id": "", "id": "", "meta_version": "", "context_id": "", "base_type": "", "ref": "", "mapped_entity": null, "created_at": "", "created_by": "", "version_id": "", "branch_id": ""}, {"model_name": "ns1.rq1.t006a", "name": "T006A", "type": "TRANSPARENT TABLE", "consumption_type": "dataset", "description": "Assign Internal to Language-Dependent Unit", "active": true, "properties": {"contflag": "C", "schema": "rq1"}, "tags": [], "entity_elements": [{"model_name": "ns1.rq1.t006a.col.mandt", "name": "MANDT", "type": "COLUMN", "consumption_type": "field", "description": "Client", "active": true, "ordinal_position": 1, "data_type": "CLNT", "max_length": 3, "is_required": true, "properties": {}, "custom_properties": {"checktable": "T000", "domname": "MANDT", "entitytab": "T000", "rollname": "MANDT"}}, {"model_name": "ns1.rq1.t006a.col.spras", "name": "SPRAS", "type": "COLUMN", "consumption_type": "field", "description": "Language Key", "active": true, "ordinal_position": 2, "data_type": "LANG", "max_length": 1, "is_required": true, "properties": {}, "custom_properties": {"checktable": "T002", "domname": "SPRAS", "entitytab": "T002", "languflag": "X", "rollname": "SPRAS"}}, {"model_name": "ns1.rq1.t006a.col.msehi", "name": "MSEHI", "type": "COLUMN", "consumption_type": "field", "description": "Unit of Measurement", "active": true, "ordinal_position": 3, "data_type": "UNIT", "max_length": 3, "is_required": true, "properties": {}, "custom_properties": {"checktable": "T006", "domname": "MEINS", "entitytab": "T006", "rollname": "MSEHI"}}, {"model_name": "ns1.rq1.t006a.ct_t006a_msehi_t006", "name": "CT_T006A_MSEHI_T006", "type": "CHECK-TABLE", "consumption_type": "lookup", "active": true, "data_type": "", "properties": {}}, {"model_name": "ns1.rq1.t006a.col.mseh3", "name": "MSEH3", "type": "COLUMN", "consumption_type": "field", "description": "External Unit of Measurement in Commercial Format (3-Char.)", "active": true, "ordinal_position": 4, "data_type": "CHAR", "max_length": 3, "properties": {}, "custom_properties": {"domname": "MSEH3", "rollname": "MSEH3"}}, {"model_name": "ns1.rq1.t006a.col.mseh6", "name": "MSEH6", "type": "COLUMN", "consumption_type": "field", "description": "External Unit of Measurement in Technical Format (6-Char.)", "active": true, "ordinal_position": 5, "data_type": "CHAR", "max_length": 6, "properties": {}, "custom_properties": {"domname": "MSEH6", "rollname": "MSEH6"}}, {"model_name": "ns1.rq1.t006a.col.mseht", "name": "MSEHT", "type": "COLUMN", "consumption_type": "field", "description": "Unit of Measurement Text (Maximum 10 Characters)", "active": true, "ordinal_position": 6, "data_type": "CHAR", "max_length": 10, "properties": {}, "custom_properties": {"domname": "TEXT10", "rollname": "MSEHT"}}, {"model_name": "ns1.rq1.t006a.col.msehl", "name": "MSEHL", "type": "COLUMN", "consumption_type": "field", "description": "Unit of Measurement Text (Maximum 30 Characters)", "active": true, "ordinal_position": 7, "data_type": "CHAR", "max_length": 30, "properties": {}, "custom_properties": {"domname": "TEXT30", "rollname": "MSEHL"}}, {"model_name": "ns1.rq1.t006a.pk.t006a", "name": "PK_T006A", "type": "PRIMARY-KEY", "consumption_type": "key", "active": true, "data_type": "", "properties": {}}], "tenant_id": "", "id": "", "meta_version": "", "context_id": "", "base_type": "", "ref": "", "mapped_entity": null, "created_at": "", "created_by": "", "version_id": "", "branch_id": ""}], "relationships": [{"model_name": "ns1.rq1.ad01c_prof.pk_ref.pk_ad01c.prof.mandt", "name": "PK_AD01C.PROF.MANDT", "type": "PK_REF", "ordinal_position": 1, "source": {"$ref": "ns1.rq1.ad01c_prof.pk.ad01c.prof"}, "target": {"$ref": "ns1.ad01c_prof.col.mandt0001"}}, {"model_name": "ns1.rq1.ad01c_prof.pk_ref.pk_ad01c.prof.profnr", "name": "PK_AD01C.PROF.PROFNR", "type": "PK_REF", "ordinal_position": 2, "source": {"$ref": "ns1.rq1.ad01c_prof.pk.ad01c.prof"}, "target": {"$ref": "ns1.ad01c_prof.col.profnr0002"}}, {"model_name": "ns1.rq1.ad01c_prof.pk_ref.pk_ad01c.prof.dpus", "name": "PK_AD01C.PROF.DPUS", "type": "PK_REF", "ordinal_position": 3, "source": {"$ref": "ns1.rq1.ad01c_prof.pk.ad01c.prof"}, "target": {"$ref": "ns1.ad01c_prof.col.dpus0003"}}, {"model_name": "ns1.rq1.adr2.pk_ref.pk_adr2.client", "name": "PK_ADR2.CLIENT", "type": "PK_REF", "ordinal_position": 1, "source": {"$ref": "ns1.rq1.adr2.pk.adr2"}, "target": {"$ref": "ns1.adr2.col.client0001"}}, {"model_name": "ns1.rq1.adr2.pk_ref.pk_adr2.addrnumber", "name": "PK_ADR2.ADDR<PERSON>MBER", "type": "PK_REF", "ordinal_position": 2, "source": {"$ref": "ns1.rq1.adr2.pk.adr2"}, "target": {"$ref": "ns1.adr2.col.addrnumber0002"}}, {"model_name": "ns1.rq1.adr2.pk_ref.pk_adr2.persnumber", "name": "PK_ADR2.PERSNUMBER", "type": "PK_REF", "ordinal_position": 3, "source": {"$ref": "ns1.rq1.adr2.pk.adr2"}, "target": {"$ref": "ns1.adr2.col.persnumber0003"}}, {"model_name": "ns1.rq1.adr2.pk_ref.pk_adr2.date.from", "name": "PK_ADR2.DATE.FROM", "type": "PK_REF", "ordinal_position": 4, "source": {"$ref": "ns1.rq1.adr2.pk.adr2"}, "target": {"$ref": "ns1.adr2.col.date_from0004"}}, {"model_name": "ns1.rq1.adr2.pk_ref.pk_adr2.consnumber", "name": "PK_ADR2.CONSNUMBER", "type": "PK_REF", "ordinal_position": 5, "source": {"$ref": "ns1.rq1.adr2.pk.adr2"}, "target": {"$ref": "ns1.adr2.col.consnumber0005"}}, {"model_name": "ns1.rq1.at40.pk_ref.pk_at40.sberfima", "name": "PK_AT40.SBERFIMA", "type": "PK_REF", "ordinal_position": 1, "source": {"$ref": "ns1.rq1.at40.pk.at40"}, "target": {"$ref": "ns1.at40.col.sberfima0001"}}, {"model_name": "ns1.rq1.sprt1.pk_ref.pk_sprt1.mandt", "name": "PK_SPRT1.MANDT", "type": "PK_REF", "ordinal_position": 1, "source": {"$ref": "ns1.rq1.sprt1.pk.sprt1"}, "target": {"$ref": "ns1.sprt1.col.mandt0001"}}, {"model_name": "ns1.rq1.sprt1.pk_ref.pk_sprt1.partnr", "name": "PK_SPRT1.PARTNR", "type": "PK_REF", "ordinal_position": 2, "source": {"$ref": "ns1.rq1.sprt1.pk.sprt1"}, "target": {"$ref": "ns1.sprt1.col.partnr0002"}}, {"model_name": "ns1.rq1.t001s.pk_ref.pk_t001s.mandt", "name": "PK_T001S.MANDT", "type": "PK_REF", "ordinal_position": 1, "source": {"$ref": "ns1.rq1.t001s.pk.t001s"}, "target": {"$ref": "ns1.t001s.col.mandt0001"}}, {"model_name": "ns1.rq1.t001s.pk_ref.pk_t001s.bukrs", "name": "PK_T001S.BUKRS", "type": "PK_REF", "ordinal_position": 2, "source": {"$ref": "ns1.rq1.t001s.pk.t001s"}, "target": {"$ref": "ns1.t001s.col.bukrs0002"}}, {"model_name": "ns1.rq1.t001s.pk_ref.pk_t001s.busab", "name": "PK_T001S.BUSAB", "type": "PK_REF", "ordinal_position": 3, "source": {"$ref": "ns1.rq1.t001s.pk.t001s"}, "target": {"$ref": "ns1.t001s.col.busab0003"}}, {"model_name": "ns1.rq1.t006.ct_ref_src.ct_t006.temp.unit_mandt-mandt", "name": "CT_T006.TEMP.UNIT_MANDT-MANDT", "type": "CT_REF_SOURCE", "ordinal_position": 1, "source": {"$ref": "ns1.rq1.t006.ct_t006_temp_unit_t006"}, "target": {"$ref": "ns1.rq1.t006.col.mandt"}}, {"model_name": "ns1.rq1.t006.ct_ref_tgt.ct_t006.temp.unit_mandt-mandt", "name": "CT_T006.TEMP.UNIT_MANDT-MANDT", "type": "CT_REF_TARGET", "ordinal_position": 1, "source": {"$ref": "ns1.rq1.t006.ct_t006_temp_unit_t006"}, "target": {"$ref": "ns1.rq1.t006.col.mandt"}}, {"model_name": "ns1.rq1.ct_t006_temp_unit.t006a.mseht0006", "name": "T006A.MSEHT0006", "type": "TEXT_COLUMN", "source": {"$ref": "ns1.rq1.t006.ct_t006_temp_unit_t006"}, "target": {"$ref": "ns1.rq1.t006a.col.mseht"}}, {"model_name": "ns1.rq1.ct_t006_temp_unit.t006a.spras0002", "name": "T006A.SPRAS0002", "type": "LANGUAGE_COLUMN", "source": {"$ref": "ns1.rq1.t006.ct_t006_temp_unit_t006"}, "target": {"$ref": "ns1.rq1.t006a.col.spras"}}, {"model_name": "ns1.rq1.ct_t006_temp_unit.cst_on_src.t006.msehi0002", "name": "T006.MSEHI0002", "type": "CONSTRAINED_ON_SOURCE", "source": {"$ref": "ns1.rq1.t006.ct_t006_temp_unit_t006"}, "target": {"$ref": "ns1.rq1.t006.col.temp_unit"}}, {"model_name": "ns1.rq1.ct_t006_temp_unit.cst_on_tgt.t006.msehi0002", "name": "T006.MSEHI0002", "type": "CONSTRAINED_ON_TARGET", "source": {"$ref": "ns1.rq1.t006.ct_t006_temp_unit_t006"}, "target": {"$ref": "ns1.rq1.t006.col.msehi"}}, {"model_name": "ns1.rq1.t006.pk_ref.pk_t006.mandt", "name": "PK_T006.MANDT", "type": "PK_REF", "ordinal_position": 1, "source": {"$ref": "ns1.rq1.t006.pk.t006"}, "target": {"$ref": "ns1.t006.col.mandt0001"}}, {"model_name": "ns1.rq1.t006.pk_ref.pk_t006.msehi", "name": "PK_T006.MSEHI", "type": "PK_REF", "ordinal_position": 2, "source": {"$ref": "ns1.rq1.t006.pk.t006"}, "target": {"$ref": "ns1.t006.col.msehi0002"}}, {"model_name": "ns1.rq1.t006a.ct_ref_src.ct_t006a.msehi_mandt-mandt", "name": "CT_T006A.MSEHI_MANDT-MANDT", "type": "CT_REF_SOURCE", "ordinal_position": 1, "source": {"$ref": "ns1.rq1.t006a.ct_t006a_msehi_t006"}, "target": {"$ref": "ns1.rq1.t006a.col.mandt"}}, {"model_name": "ns1.rq1.t006a.ct_ref_tgt.ct_t006a.msehi_mandt-mandt", "name": "CT_T006A.MSEHI_MANDT-MANDT", "type": "CT_REF_TARGET", "ordinal_position": 1, "source": {"$ref": "ns1.rq1.t006a.ct_t006a_msehi_t006"}, "target": {"$ref": "ns1.rq1.t006.col.mandt"}}, {"model_name": "ns1.rq1.ct_t006a_msehi.t006a.mseht0006", "name": "T006A.MSEHT0006", "type": "TEXT_COLUMN", "source": {"$ref": "ns1.rq1.t006a.ct_t006a_msehi_t006"}, "target": {"$ref": "ns1.rq1.t006a.col.mseht"}}, {"model_name": "ns1.rq1.ct_t006a_msehi.t006a.spras0002", "name": "T006A.SPRAS0002", "type": "LANGUAGE_COLUMN", "source": {"$ref": "ns1.rq1.t006a.ct_t006a_msehi_t006"}, "target": {"$ref": "ns1.rq1.t006a.col.spras"}}, {"model_name": "ns1.rq1.ct_t006a_msehi.cst_on_src.t006.msehi0002", "name": "T006.MSEHI0002", "type": "CONSTRAINED_ON_SOURCE", "source": {"$ref": "ns1.rq1.t006a.ct_t006a_msehi_t006"}, "target": {"$ref": "ns1.rq1.t006a.col.msehi"}}, {"model_name": "ns1.rq1.ct_t006a_msehi.cst_on_tgt.t006.msehi0002", "name": "T006.MSEHI0002", "type": "CONSTRAINED_ON_TARGET", "source": {"$ref": "ns1.rq1.t006a.ct_t006a_msehi_t006"}, "target": {"$ref": "ns1.rq1.t006.col.msehi"}}, {"model_name": "ns1.rq1.t006a.pk_ref.pk_t006a.mandt", "name": "PK_T006A.MANDT", "type": "PK_REF", "ordinal_position": 1, "source": {"$ref": "ns1.rq1.t006a.pk.t006a"}, "target": {"$ref": "ns1.t006a.col.mandt0001"}}, {"model_name": "ns1.rq1.t006a.pk_ref.pk_t006a.spras", "name": "PK_T006A.SPRAS", "type": "PK_REF", "ordinal_position": 2, "source": {"$ref": "ns1.rq1.t006a.pk.t006a"}, "target": {"$ref": "ns1.t006a.col.spras0002"}}, {"model_name": "ns1.rq1.t006a.pk_ref.pk_t006a.msehi", "name": "PK_T006A.MSEHI", "type": "PK_REF", "ordinal_position": 3, "source": {"$ref": "ns1.rq1.t006a.pk.t006a"}, "target": {"$ref": "ns1.t006a.col.msehi0003"}}], "tenant_id": ""}