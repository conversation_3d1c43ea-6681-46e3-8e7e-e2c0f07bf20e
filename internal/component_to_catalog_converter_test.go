package internal

import (
	"os"
	"path/filepath"
	"strings"
	"testing"
)

func isJSONFile(filename string) bool {
	cleanPath := filepath.Clean(filename)
	return strings.HasSuffix(cleanPath, ".json")
}

func TestConvertComponentToCatalogDataModel(t *testing.T) {
	// Define input and output file paths
	inputFilePath := "scan_sample_sap.json"
	outputFilePath := "output_sap.json"

	if !isJSONFile(inputFilePath) {
		t.Fatalf("Input file is not a JSON file: %s", inputFilePath)
	}
	if !isJSONFile(outputFilePath) {
		t.Fatalf("Output file is not a JSON file: %s", outputFilePath)
	}

	// Call the function
	err := ConvertComponentToCatalogDataModel(inputFilePath, outputFilePath)
	if err != nil {
		t.Fatalf("Function failed: %v", err)
	}

	// Verify the output file exists
	if _, err := os.Stat(outputFilePath); os.IsNotExist(err) {
		t.Fatalf("Output file was not created")
	}
}
