package internal

import (
	"encoding/json"
	"fmt"
	"os"
	"strconv"
	"strings"

	"github.com/BackOfficeAssoc/catalog/models"
)

func readJSONFile(filePath string) ([]models.Component, error) {
	// #nosec G304
	byteValue, err := os.ReadFile(filePath)
	if err != nil {
		return nil, err
	}

	var components []models.Component

	err = json.Unmarshal(byteValue, &components)
	if err != nil {
		fmt.Println("Error decoding JSON:", err)
		return nil, err
	}

	return components, nil
}

// Function to write JSON to a file
func writeJSONToFile(filename string, data interface{}) error {
	file, err := os.Create(filename)
	if err != nil {
		return err
	}
	defer file.Close()

	// Encode data into JSON and write to file
	encoder := json.NewEncoder(file)
	encoder.SetIndent("", "  ") // Pretty-print JSON with indentation
	err = encoder.Encode(data)
	if err != nil {
		return err
	}

	return nil
}

// Helper function to safely dereference a string pointer
func safeDereference(value *string) string {
	if value == nil {
		return ""
	}
	return *value
}

// Helper function to convert *int to *string
func pointerToString(value *int) *string {
	if value == nil {
		return nil
	}
	str := strconv.Itoa(*value)
	return &str
}

// Helper function to convert string to int with a default value
func safeStringToInt(value *string, defaultValue int) int {
	if value == nil {
		return defaultValue
	}
	parsedValue, err := strconv.Atoi(*value)
	if err != nil {
		return defaultValue
	}
	return parsedValue
}

// Helper function to create a map of components by their XID
func createComponentMapAndFetchSchemas(components []models.Component) (map[string]models.Component, []models.Component) {
	componentMap := make(map[string]models.Component)
	schemas := []models.Component{}

	for _, comp := range components {
		if comp.ComponentType != nil && *comp.ComponentType == "SCHEMA" {
			schemas = append(schemas, comp)
		}
		componentMap[comp.XID] = comp
	}

	return componentMap, schemas
}

// Helper function to create an EntityElement
func createEntityElement(entEle models.Component, entityModelName string) models.EntityElement {
	entityElementModelName := strings.ToLower(entityModelName + "." + strings.ReplaceAll(strings.ReplaceAll(*entEle.Name, "_", "."), "/", "."))

	entityElement := models.EntityElement{
		ModelName: entityElementModelName,
		Name:      strings.ReplaceAll(*entEle.Name, "/", "_"),
		Type:      *entEle.ComponentType,

		Active:     true,
		Precision:  safeStringToInt(entEle.Precision, 0),
		MaxLength:  safeStringToInt(entEle.Size, 0),
		Properties: make(map[string]interface{}),
	}

	// Set properties based on component type
	switch *entEle.ComponentType {
	case "COLUMN":
		entityElement.ConsumptionType = "field"
		entityElement.ModelName = strings.ToLower(entityModelName + ".col." + strings.ReplaceAll(*entEle.Name, "_", "."))
		entityElement.Description = *entEle.Description
		entityElement.DataType = *entEle.DataType
		entityElement.OrdinalPosition = *entEle.OrdinalPosition
		entityElement.IsRequired = !(*entEle.AllowNull)
		entityElement.Scale = *entEle.Scale
		entityElement.CustomProperties = entEle.CustomProperties
	case "PRIMARY_KEY", "PRIMARY-KEY": //In case hte primary key has a typo error - instead of _
		entityElement.ConsumptionType = "key"
		entityElement.Type = "PRIMARY-KEY" // Ensure consistent naming
		entityElement.Description = safeDereference(entEle.Description)
		if entEle.OrdinalPosition != nil {
			entityElement.OrdinalPosition = *entEle.OrdinalPosition
		}
	case "INDEX":
		entityElement.ConsumptionType = "index"
		entityElement.Description = safeDereference(entEle.Description)
		if entEle.OrdinalPosition != nil {
			entityElement.OrdinalPosition = *entEle.OrdinalPosition
		}

		if entEle.IsClustered != nil {
			entityElement.Properties["is_clustered"] = *entEle.IsClustered
		}
		if entEle.IsUnique != nil {
			entityElement.Properties["is_unique"] = *entEle.IsUnique
		}
	case "FOREIGN_KEY", "FOREIGN-KEY":
		entityElement.ConsumptionType = "constraint"
		entityElement.Type = "FOREIGN-KEY" // Ensure consistent naming
		entityElement.Description = safeDereference(entEle.Description)
		if entEle.OrdinalPosition != nil {
			entityElement.OrdinalPosition = *entEle.OrdinalPosition
		}

		if entEle.ActionOnDelete != nil {
			entityElement.Properties["on_delete"] = *entEle.ActionOnDelete
		}
		if entEle.ActionOnUpdate != nil {
			entityElement.Properties["on_update"] = *entEle.ActionOnUpdate
		}
		if entEle.Validate != nil {
			entityElement.Properties["validate"] = *entEle.Validate
		}
	case "CHECK-TABLE":
		entityElement.ConsumptionType = "lookup"
		name := strings.ReplaceAll(strings.ReplaceAll(entEle.XID+"_"+*entEle.Name, "/", "_"), ".", "_")
		entityElement.Name = name //CT_{current_table}_{current_column}_{checktable_name}
		entityElement.ModelName = strings.ToLower(entityModelName + "." + name)
		entityElement.Type = "CHECK-TABLE"
	}

	// Set properties for PRIMARY_KEY
	if entityElement.Properties == nil {
		entityElement.Properties = make(map[string]interface{})
	}
	if entityElement.Tags == nil {
		entityElement.Tags = []string{}
	}
	return entityElement
}

// Helper function to create a Relationship
func createRelationship(entEleRel models.Component, entityModelName string, isIncludedColumn bool, parentElementName string, nameSPSchema string, parentName string, trimPreSchema string, isSAPModel bool) []models.Relationship {
	relName := entEleRel.XID

	if !isSAPModel && parentName != "" {
		relNamePart := strings.Split(relName, parentName)
		if len(relNamePart) > 1 { // This is used for SQL type
			relName = strings.Trim(relNamePart[len(relNamePart)-1], ".")
		}
	}

	elementXID := strings.ToLower(entEleRel.XID)
	relType := "REFERENCE_ELEMENT"
	if strings.HasPrefix(elementXID, "pk/") {
		relType = "PK_REF"
	} else if strings.HasPrefix(elementXID, "fk/") {
		relType = "FK_REFERENCE_ELEMENT"
	} else if isIncludedColumn && strings.HasPrefix(elementXID, "idx/") {
		relType = "IDX_INCLUDED_COLUMN"
	} else if !isIncludedColumn && strings.HasPrefix(elementXID, "idx/") {
		relType = "IDX_REF"
	} else if strings.HasPrefix(elementXID, "uix/") {
		relType = "UIX_REF"
	} else if strings.HasPrefix(elementXID, "ct/") {
		relType = "CT_REF"
		relName = entEleRel.XID
	} else {
		relType = "N/A"
	}

	relName = strings.ReplaceAll(strings.ReplaceAll(relName, "_", "."), "/", "_")
	relationModelName := strings.ToLower(entityModelName + ".{reltype}." + relName)
	modelRel := models.Relationship{
		ModelName: relationModelName,
		Name:      relName,

		Type:            relType,
		OrdinalPosition: safeStringToInt(pointerToString(entEleRel.OrdinalPosition), 0),
		Source:          models.Reference{Ref: parentElementName},
	}

	if relType == "PK_REF" {
		// For PK Source in input model is target for new meta meta mapping design

		modelRel.ModelName = strings.ReplaceAll(relationModelName, "{reltype}", "pk_ref")
		ctSourceName := strings.ToLower(nameSPSchema + "." + strings.TrimPrefix(*entEleRel.Source, trimPreSchema))

		lastIndex := strings.LastIndex(ctSourceName, ".")
		if lastIndex != -1 {
			ctSourceName = ctSourceName[:lastIndex] + ".col." + ctSourceName[lastIndex+1:]
		}

		modelRel.Target = models.Reference{Ref: ctSourceName}

	} else if relType == "FK_REFERENCE_ELEMENT" {
		// For FK Source / Target in input model

		ctRelationship := []models.Relationship{}

		ctRel := modelRel
		ctRel.Type = "FK_REF_SOURCE"

		ctRel.ModelName = strings.ReplaceAll(relationModelName, "{reltype}", "fk_ref_src")
		ctSourceName := strings.ToLower(nameSPSchema + "." + strings.TrimPrefix(*entEleRel.Source, trimPreSchema))

		lastIndex := strings.LastIndex(ctSourceName, ".")
		if lastIndex != -1 {
			ctSourceName = ctSourceName[:lastIndex] + ".col." + ctSourceName[lastIndex+1:]
		}

		ctRel.Target = models.Reference{Ref: ctSourceName}
		ctRelationship = append(ctRelationship, ctRel)

		ctRel = modelRel
		ctRel.Type = "FK_REF_TARGET"
		ctRel.ModelName = strings.ReplaceAll(relationModelName, "{reltype}", "fk_ref_tgt")
		ctSourceName = strings.ToLower(nameSPSchema + "." + strings.TrimPrefix(*entEleRel.Target, trimPreSchema))

		lastIndex = strings.LastIndex(ctSourceName, ".")
		if lastIndex != -1 {
			ctSourceName = ctSourceName[:lastIndex] + ".col." + ctSourceName[lastIndex+1:]
		}

		ctRel.Target = models.Reference{Ref: ctSourceName}

		ctRelationship = append(ctRelationship, ctRel)
		return ctRelationship
	}

	if relType == "CT_REF" {

		ctRelationship := []models.Relationship{}
		modelRel.Name = relName

		ctRel := modelRel
		ctRel.Type = "CT_REF_SOURCE"
		ctRel.ModelName = strings.ReplaceAll(relationModelName, "{reltype}", "ct_ref_src")
		ctSourceName := strings.ToLower(nameSPSchema + "." + strings.ReplaceAll((*entEleRel.Source)[:len(*entEleRel.Source)-4], ".", ".col."))
		ctRel.Target = models.Reference{Ref: ctSourceName}

		ctRelationship = append(ctRelationship, ctRel)

		ctRel = models.Relationship{}
		ctRel = modelRel
		ctRel.Type = "CT_REF_TARGET"
		ctRel.ModelName = strings.ReplaceAll(relationModelName, "{reltype}", "ct_ref_tgt")
		ctTargetName := strings.ToLower(nameSPSchema + "." + strings.ReplaceAll((*entEleRel.Target)[:len(*entEleRel.Target)-4], ".", ".col."))
		ctRel.Target = models.Reference{Ref: ctTargetName}

		ctRelationship = append(ctRelationship, ctRel)

		return ctRelationship
	}

	return []models.Relationship{modelRel}
}

func checkTableAdditionalRelationship(entEleRel models.Component, entityModelName string, parentXID string) []models.Relationship {

	relName := strings.ReplaceAll(strings.ReplaceAll(entEleRel.XID, "/", "_"), ".", "_")
	relationModelName := entityModelName + "." + relName

	modelRel := models.Relationship{
		Name:            relName,
		ModelName:       relationModelName,
		OrdinalPosition: safeStringToInt(pointerToString(entEleRel.OrdinalPosition), 0),
		Source:          models.Reference{Ref: parentXID},
	}

	ctRel := modelRel
	ctRelationship := []models.Relationship{}

	if entEleRel.TextColumn != nil {
		ctRel = models.Relationship{}
		ctRel = modelRel
		ctRel.Name = safeDereference(entEleRel.TextColumn)
		ctRel.ModelName = strings.ToLower(relationModelName + "." + safeDereference(entEleRel.TextColumn))
		ctRel.Type = "TEXT_COLUMN"
		ctTargetName := strings.ToLower(entityModelName + "." + strings.ReplaceAll((*entEleRel.TextColumn)[:len(*entEleRel.TextColumn)-4], ".", ".col."))

		ctRel.Target = models.Reference{Ref: ctTargetName}

		ctRelationship = append(ctRelationship, ctRel)
	}

	if entEleRel.LanguageColumn != nil {
		ctRel = models.Relationship{}
		ctRel = modelRel
		ctRel.Name = safeDereference(entEleRel.LanguageColumn)
		ctRel.ModelName = strings.ToLower(relationModelName + "." + safeDereference(entEleRel.LanguageColumn))
		ctRel.Type = "LANGUAGE_COLUMN"
		ctTargetName := strings.ToLower(entityModelName + "." + strings.ReplaceAll((*entEleRel.LanguageColumn)[:len(*entEleRel.LanguageColumn)-4], ".", ".col."))

		ctRel.Target = models.Reference{Ref: ctTargetName}

		ctRelationship = append(ctRelationship, ctRel)
	}

	if entEleRel.ConstrainedOn != nil {
		ctRel = models.Relationship{}
		ctRel = modelRel
		ctRel.Type = "CONSTRAINED_ON_SOURCE"
		ctRel.Name = safeDereference(entEleRel.ConstrainedOn)
		ctRel.ModelName = strings.ToLower(relationModelName + ".cst_on_src." + safeDereference(entEleRel.ConstrainedOn))

		ctTargetName := strings.ToLower(entityModelName + "." + strings.ReplaceAll(strings.ReplaceAll(entEleRel.XID, ".", ".col."), "CT/", ""))

		ctRel.Target = models.Reference{Ref: ctTargetName}
		ctRelationship = append(ctRelationship, ctRel)

		ctRel = modelRel
		ctRel.Type = "CONSTRAINED_ON_TARGET"
		ctRel.Name = safeDereference(entEleRel.ConstrainedOn)
		ctRel.ModelName = strings.ToLower(relationModelName + ".cst_on_tgt." + safeDereference(entEleRel.ConstrainedOn))

		ctTargetName = strings.ToLower(entityModelName + "." + strings.ReplaceAll((*entEleRel.ConstrainedOn)[:len(*entEleRel.ConstrainedOn)-4], ".", ".col."))

		ctRel.Target = models.Reference{Ref: ctTargetName}

		ctRelationship = append(ctRelationship, ctRel)
	}
	return ctRelationship
}

func isValidComponentType(componentType *string) bool {
	if componentType == nil {
		return false
	}

	validTypes := []string{"TABLE", "VIEW", "Transparent table", "Pooled table", "INTTAB", "CHECK-TABLE"}

	for _, validType := range validTypes {
		if *componentType == validType {
			return true
		}
	}
	return false
}

func buildCatalogDataModel(components []models.Component) models.DataModel {
	compMap, schemas := createComponentMapAndFetchSchemas(components)

	datamodel := models.DataModel{
		MetaSchemaVersion: "relational-20250501",
		Namespace:         "ns1",
		CurrentVersion:    "20250201-001",
	}

	var entities []models.Entity
	var relationships []models.Relationship = []models.Relationship{}
	var isSAPModel bool = false
	for _, schema := range schemas {
		for _, comp := range schema.Components {
			if ent, exists := compMap[comp]; exists && isValidComponentType(ent.ComponentType) {

				entityModelName := strings.ToLower(datamodel.Namespace + "." + *schema.Name + "." + *ent.Name)
				entityNameSpaceSchema := datamodel.Namespace + "." + *schema.Name
				trimAdditionalSchema := strings.ReplaceAll(schema.XID, *schema.Name, "")
				entity := models.Entity{
					ModelName:       entityModelName,
					Name:            *ent.Name,
					Type:            strings.ToUpper(*ent.ComponentType),
					ConsumptionType: "dataset",
					Description:     *ent.Description,
					Active:          true,
					Properties:      map[string]interface{}{"schema": *schema.Name},
				}

				if *ent.ComponentType != "TABLE" && *ent.ComponentType != "VIEW" {
					isSAPModel = true
				}
				if *ent.ComponentType == "Transparent table" || *ent.ComponentType == "Pooled table" {
					entity.Properties["contflag"] = ent.CustomProperties["contflag"]
				}
				if entity.Tags == nil {
					entity.Tags = []string{}
				}

				for _, ele := range ent.Components {
					if entEle, exists := compMap[ele]; exists {

						getEntity := createEntityElement(entEle, entityModelName)
						entity.EntityElements = append(entity.EntityElements, getEntity)

						// Check-Table
						cRel, cEntEle := getRelationshipData(entEle, compMap, entityModelName, datamodel, ent.XID, entityNameSpaceSchema, isSAPModel)

						if cEntEle.ModelName != "" {
							entity.EntityElements = append(entity.EntityElements, cEntEle)
						}
						relationships = append(relationships, cRel...)
					}
				}

				for _, ele := range ent.ReferenceData { // PK
					if entEle, exists := compMap[ele]; exists {

						// Reference Data to EntityElements
						getEntity := createEntityElement(entEle, entityModelName)
						entity.EntityElements = append(entity.EntityElements, getEntity)

						for _, refele := range entEle.ReferenceElements {
							if entEleRel, exists := compMap[refele]; exists {
								// As in SAP model reference does not contains full path
								// ParentName parameter is ussed for FK ref source
								// Trim additional Schema is used  for SQL type, to remove additional schema name for example "AdventureWorks2008."

								if strings.HasPrefix(entEleRel.XID, "fk/") {
									relationships = append(relationships, createRelationship(entEleRel, entityModelName, false, getEntity.ModelName, datamodel.Namespace, getEntity.Name, trimAdditionalSchema, isSAPModel)...)
								} else {
									relationships = append(relationships, createRelationship(entEleRel, entityModelName, false, getEntity.ModelName, datamodel.Namespace, "", trimAdditionalSchema, isSAPModel)...)
								}
							}
						}

						for _, refele := range entEle.IncludedColumns {
							if entEleRel, exists := compMap[refele]; exists {
								relationships = append(relationships, createRelationship(entEleRel, datamodel.Namespace, true, getEntity.ModelName, "", "", "", isSAPModel)...)
							}
						}
					}
				}
				if entity.EntityElements == nil {
					entity.EntityElements = []models.EntityElement{}
				}
				entities = append(entities, entity)
			}
		}
	}

	datamodel.Entities = entities
	datamodel.Relationships = relationships

	return datamodel
}

func getRelationshipData(entEle models.Component, compMap map[string]models.Component, entityModelName string, datamodel models.DataModel, entXID string, nameSPSchema string, isSAPModel bool) ([]models.Relationship, models.EntityElement) {
	// Reference Data to EntityElements
	var relationships []models.Relationship
	var getEntity models.EntityElement

	for _, ele := range entEle.ReferenceData {

		if entEle, exists := compMap[ele]; exists {

			// Reference Data to EntityElements
			getEntity = createEntityElement(entEle, entityModelName)

			for _, refele := range entEle.ReferenceElements { // PK, CT_REF_TARGET
				if entEleRel, exists := compMap[refele]; exists {

					relationships = append(relationships, createRelationship(entEleRel, entityModelName, false, getEntity.ModelName, nameSPSchema, "", "", isSAPModel)...)
				}
			}

			if *entEle.ComponentType == "CHECK-TABLE" {
				ctRelationship := checkTableAdditionalRelationship(entEle, nameSPSchema, getEntity.ModelName)
				relationships = append(relationships, ctRelationship...)
			}
		}
	}
	return relationships, getEntity
}

func ConvertComponentToCatalogDataModel(inputFilePath string, outputFilePath string) error {
	components, err := readJSONFile(inputFilePath)
	if err != nil {
		return fmt.Errorf("Error reading JSON file: %w", err)
	}

	datamodel := buildCatalogDataModel(components)

	err = writeJSONToFile(outputFilePath, datamodel)
	if err != nil {
		return fmt.Errorf("Error writing JSON file: %w", err)
	}

	return nil
}
