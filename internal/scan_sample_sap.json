[{"xid": "AD01C_PROF.MANDT0001", "name": "MANDT", "description": "Client", "component_type": "COLUMN", "consumption_type": "column", "custom_properties": {"checktable": "T000", "rollname": "MANDT", "domname": "MANDT", "entitytab": "T000"}, "location_path": "AD01C_PROF.MANDT", "key": true, "ordinal_position": 1, "is_field": true, "data_type": "CLNT", "precision": "0", "scale": 0, "size": "3", "allow_null": false}, {"xid": "AD01C_PROF.PROFNR0002", "name": "PROFNR", "description": "Dynamic Item Processor Profile", "component_type": "COLUMN", "consumption_type": "column", "custom_properties": {"checktable": "DPPROFH", "rollname": "AD01PROFNR", "domname": "AD01PROFNR", "entitytab": "DPPROFH"}, "location_path": "AD01C_PROF.PROFNR", "key": true, "ordinal_position": 2, "is_field": true, "data_type": "CHAR", "precision": "0", "scale": 0, "size": "8", "allow_null": false}, {"xid": "AD01C_PROF.DPUS0003", "name": "DPUS", "description": "Usage of the DI profile", "component_type": "COLUMN", "consumption_type": "column", "custom_properties": {"rollname": "AD01USAGE", "domname": "AD01USAGE"}, "location_path": "AD01C_PROF.DPUS", "key": true, "ordinal_position": 3, "is_field": true, "data_type": "NUMC", "precision": "0", "scale": 0, "size": "2", "allow_null": false}, {"xid": "AD01C_PROF.MACOND0004", "name": "MACOND", "description": "Number of manual conditions", "component_type": "COLUMN", "consumption_type": "column", "custom_properties": {"rollname": "AD01MACOND", "domname": "AD01MACOND"}, "location_path": "AD01C_PROF.MACOND", "key": false, "ordinal_position": 4, "is_field": true, "data_type": "NUMC", "precision": "0", "scale": 0, "size": "1", "allow_null": true}, {"xid": "AD01C_PROF.SDAUART0005", "name": "SDAUART", "description": "Sales Document Type", "component_type": "COLUMN", "consumption_type": "column", "custom_properties": {"checktable": "TVAK", "rollname": "AD01CAUART", "domname": "AUART", "entitytab": "TVAK"}, "location_path": "AD01C_PROF.SDAUART", "key": false, "ordinal_position": 5, "is_field": true, "data_type": "CHAR", "precision": "0", "scale": 0, "size": "4", "allow_null": true}, {"xid": "AD01C_PROF.SDAUARG0006", "name": "SDAUARG", "description": "Sales Document Type for Credit Memo Request", "component_type": "COLUMN", "consumption_type": "column", "custom_properties": {"checktable": "TVAK", "rollname": "AUARG", "domname": "AUART", "entitytab": "TVAK"}, "location_path": "AD01C_PROF.SDAUARG", "key": false, "ordinal_position": 6, "is_field": true, "data_type": "CHAR", "precision": "0", "scale": 0, "size": "4", "allow_null": true}, {"xid": "AD01C_PROF.WCHECK0007", "name": "WCHECK", "description": "Warranty check", "component_type": "COLUMN", "consumption_type": "column", "custom_properties": {"rollname": "WACHK", "domname": "XFELD"}, "location_path": "AD01C_PROF.WCHECK", "key": false, "ordinal_position": 7, "is_field": true, "data_type": "CHAR", "precision": "0", "scale": 0, "size": "1", "allow_null": true}, {"xid": "AD01C_PROF.FLDIMAT0008", "name": "FLDIMAT", "description": "Process Dynamic Items with Material Only", "component_type": "COLUMN", "consumption_type": "column", "custom_properties": {"rollname": "FLDIMAT", "domname": "XFELD"}, "location_path": "AD01C_PROF.FLDIMAT", "key": false, "ordinal_position": 8, "is_field": true, "data_type": "CHAR", "precision": "0", "scale": 0, "size": "1", "allow_null": true}, {"xid": "AD01C_PROF.AD01SDAUART0009", "name": "AD01SDAUART", "description": "Sales Document Type", "component_type": "COLUMN", "consumption_type": "column", "custom_properties": {"checktable": "TVAK", "rollname": "AUART", "domname": "AUART", "entitytab": "TVAK"}, "location_path": "AD01C_PROF.AD01SDAUART", "key": false, "ordinal_position": 9, "is_field": true, "data_type": "CHAR", "precision": "0", "scale": 0, "size": "4", "allow_null": true}, {"xid": "PK/AD01C_PROF.MANDT", "ordinal_position": 1, "source": "AD01C_PROF.MANDT0001"}, {"xid": "PK/AD01C_PROF.PROFNR", "ordinal_position": 2, "source": "AD01C_PROF.PROFNR0002"}, {"xid": "PK/AD01C_PROF.DPUS", "ordinal_position": 3, "source": "AD01C_PROF.DPUS0003"}, {"xid": "PK/AD01C_PROF", "name": "PK/AD01C_PROF", "component_type": "PRIMARY-KEY", "consumption_type": "key", "reference_elements": ["PK/AD01C_PROF.MANDT", "PK/AD01C_PROF.PROFNR", "PK/AD01C_PROF.DPUS"]}, {"xid": "AD01C_PROF", "name": "AD01C_PROF", "description": "DIP profile: Usage", "component_type": "Transparent table", "consumption_type": "table", "components": ["AD01C_PROF.MANDT0001", "AD01C_PROF.PROFNR0002", "AD01C_PROF.DPUS0003", "AD01C_PROF.MACOND0004", "AD01C_PROF.SDAUART0005", "AD01C_PROF.SDAUARG0006", "AD01C_PROF.WCHECK0007", "AD01C_PROF.FLDIMAT0008", "AD01C_PROF.AD01SDAUART0009"], "custom_properties": {"contflag": "C"}, "location_path": "AD01C_PROF", "reference_data": ["PK/AD01C_PROF"]}, {"xid": "ADR2.CLIENT0001", "name": "CLIENT", "description": "Client", "component_type": "COLUMN", "consumption_type": "column", "custom_properties": {"checktable": "T000", "rollname": "MANDT", "domname": "MANDT", "entitytab": "T000"}, "location_path": "ADR2.CLIENT", "key": true, "ordinal_position": 1, "is_field": true, "data_type": "CLNT", "precision": "0", "scale": 0, "size": "3", "allow_null": false}, {"xid": "ADR2.ADDRNUMBER0002", "name": "ADDRNUMBER", "description": "Address number", "component_type": "COLUMN", "consumption_type": "column", "custom_properties": {"checktable": "ADRC", "rollname": "AD_ADDRNUM", "domname": "AD_ADDRNUM", "entitytab": "ADRC"}, "location_path": "ADR2.ADDR<PERSON>MBER", "key": true, "ordinal_position": 2, "is_field": true, "data_type": "CHAR", "precision": "0", "scale": 0, "size": "10", "allow_null": false}, {"xid": "ADR2.PERSNUMBER0003", "name": "PERSNUMBER", "description": "Person number", "component_type": "COLUMN", "consumption_type": "column", "custom_properties": {"checktable": "ADRP", "rollname": "AD_PERSNUM", "domname": "AD_PERSNUM", "entitytab": "ADRP"}, "location_path": "ADR2.PERSNUMBER", "key": true, "ordinal_position": 3, "is_field": true, "data_type": "CHAR", "precision": "0", "scale": 0, "size": "10", "allow_null": false}, {"xid": "ADR2.DATE_FROM0004", "name": "DATE_FROM", "description": "Valid-from date - in current Release only 00010101 possible", "component_type": "COLUMN", "consumption_type": "column", "custom_properties": {"rollname": "AD_DATE_FR", "domname": "DATUM"}, "location_path": "ADR2.DATE_FROM", "key": true, "ordinal_position": 4, "is_field": true, "data_type": "DATS", "precision": "0", "scale": 0, "size": "8", "allow_null": false}, {"xid": "ADR2.CONSNUMBER0005", "name": "CONSNUMBER", "description": "Sequence number", "component_type": "COLUMN", "consumption_type": "column", "custom_properties": {"rollname": "AD_CONSNUM", "domname": "NUMC3"}, "location_path": "ADR2.CONSNUMBER", "key": true, "ordinal_position": 5, "is_field": true, "data_type": "NUMC", "precision": "0", "scale": 0, "size": "3", "allow_null": false}, {"xid": "ADR2.COUNTRY0006", "name": "COUNTRY", "description": "Country for telephone/fax number", "component_type": "COLUMN", "consumption_type": "column", "custom_properties": {"checktable": "T005", "rollname": "AD_COMCTRY", "domname": "LAND1", "entitytab": "T005"}, "location_path": "ADR2.COUNTRY", "key": false, "ordinal_position": 6, "is_field": true, "data_type": "CHAR", "precision": "0", "scale": 0, "size": "3", "allow_null": true}, {"xid": "ADR2.FLGDEFAULT0007", "name": "FLGDEFAULT", "description": "Standard Sender Address in this Communication Type", "component_type": "COLUMN", "consumption_type": "column", "custom_properties": {"rollname": "AD_FLGDFNR", "domname": "XFELD"}, "location_path": "ADR2.FLGDEFAULT", "key": false, "ordinal_position": 7, "is_field": true, "data_type": "CHAR", "precision": "0", "scale": 0, "size": "1", "allow_null": true}, {"xid": "ADR2.FLG_NOUSE0008", "name": "FLG_NOUSE", "description": "Flag: This Communication Number is Not Used", "component_type": "COLUMN", "consumption_type": "column", "custom_properties": {"rollname": "AD_FLNOUSE", "domname": "XFELD"}, "location_path": "ADR2.FLG_NOUSE", "key": false, "ordinal_position": 8, "is_field": true, "data_type": "CHAR", "precision": "0", "scale": 0, "size": "1", "allow_null": true}, {"xid": "ADR2.HOME_FLAG0009", "name": "HOME_FLAG", "description": "Recipient address in this communication type (mail sys.grp)", "component_type": "COLUMN", "consumption_type": "column", "custom_properties": {"rollname": "AD_FLGHOME", "domname": "XFELD"}, "location_path": "ADR2.HOME_FLAG", "key": false, "ordinal_position": 9, "is_field": true, "data_type": "CHAR", "precision": "0", "scale": 0, "size": "1", "allow_null": true}, {"xid": "ADR2.TEL_NUMBER0010", "name": "TEL_NUMBER", "description": "Telephone no.: dialling code+number", "component_type": "COLUMN", "consumption_type": "column", "custom_properties": {"rollname": "AD_TLNMBR", "domname": "CHAR30"}, "location_path": "ADR2.TEL_NUMBER", "key": false, "ordinal_position": 10, "is_field": true, "data_type": "CHAR", "precision": "0", "scale": 0, "size": "30", "allow_null": true}, {"xid": "ADR2.TEL_EXTENS0011", "name": "TEL_EXTENS", "description": "Telephone no.: Extension", "component_type": "COLUMN", "consumption_type": "column", "custom_properties": {"rollname": "AD_TLXTNS", "domname": "CHAR10"}, "location_path": "ADR2.TEL_EXTENS", "key": false, "ordinal_position": 11, "is_field": true, "data_type": "CHAR", "precision": "0", "scale": 0, "size": "10", "allow_null": true}, {"xid": "ADR2.TELNR_LONG0012", "name": "TELNR_LONG", "description": "Complete Number: Dialling Code+Number+Extension", "component_type": "COLUMN", "consumption_type": "column", "custom_properties": {"rollname": "AD_TELNRLG", "domname": "CHAR30"}, "location_path": "ADR2.TELNR_LONG", "key": false, "ordinal_position": 12, "is_field": true, "data_type": "CHAR", "precision": "0", "scale": 0, "size": "30", "allow_null": true}, {"xid": "ADR2.TELNR_CALL0013", "name": "TELNR_CALL", "description": "Telephone number for determining caller", "component_type": "COLUMN", "consumption_type": "column", "custom_properties": {"rollname": "AD_TELNRCL", "domname": "CHAR30"}, "location_path": "ADR2.TELNR_CALL", "key": false, "ordinal_position": 13, "is_field": true, "data_type": "CHAR", "precision": "0", "scale": 0, "size": "30", "allow_null": true}, {"xid": "ADR2.DFT_RECEIV0014", "name": "DFT_RECEIV", "description": "Indicator: Telephone is SMS-Enabled", "component_type": "COLUMN", "consumption_type": "column", "custom_properties": {"rollname": "AD_FLGSMS", "domname": "AD_FLGSMS"}, "location_path": "ADR2.DFT_RECEIV", "key": false, "ordinal_position": 14, "is_field": true, "data_type": "CHAR", "precision": "0", "scale": 0, "size": "1", "allow_null": true}, {"xid": "ADR2.R3_USER0015", "name": "R3_USER", "description": "Indicator: Telephone is a Mobile Telephone", "component_type": "COLUMN", "consumption_type": "column", "custom_properties": {"rollname": "AD_FLGMOB", "domname": "AD_FLGMOB"}, "location_path": "ADR2.R3_USER", "key": false, "ordinal_position": 15, "is_field": true, "data_type": "CHAR", "precision": "0", "scale": 0, "size": "1", "allow_null": true}, {"xid": "ADR2.VALID_FROM0016", "name": "VALID_FROM", "description": "Communication Data: Valid From (YYYYMMDDHHMMSS)", "component_type": "COLUMN", "consumption_type": "column", "custom_properties": {"rollname": "AD_VALFROM", "domname": "TIMESTMP"}, "location_path": "ADR2.VALID_FROM", "key": false, "ordinal_position": 16, "is_field": true, "data_type": "CHAR", "precision": "0", "scale": 0, "size": "14", "allow_null": true}, {"xid": "ADR2.VALID_TO0017", "name": "VALID_TO", "description": "Communication Data: Valid To (YYYYMMDDHHMMSS)", "component_type": "COLUMN", "consumption_type": "column", "custom_properties": {"rollname": "AD_VALTO", "domname": "TIMESTAMP"}, "location_path": "ADR2.VALID_TO", "key": false, "ordinal_position": 17, "is_field": true, "data_type": "CHAR", "precision": "0", "scale": 0, "size": "14", "allow_null": true}, {"xid": "PK/ADR2.CLIENT", "ordinal_position": 1, "source": "ADR2.CLIENT0001"}, {"xid": "PK/ADR2.ADDRNUMBER", "ordinal_position": 2, "source": "ADR2.ADDRNUMBER0002"}, {"xid": "PK/ADR2.PERSNUMBER", "ordinal_position": 3, "source": "ADR2.PERSNUMBER0003"}, {"xid": "PK/ADR2.DATE_FROM", "ordinal_position": 4, "source": "ADR2.DATE_FROM0004"}, {"xid": "PK/ADR2.CONSNUMBER", "ordinal_position": 5, "source": "ADR2.CONSNUMBER0005"}, {"xid": "PK/ADR2", "name": "PK/ADR2", "component_type": "PRIMARY-KEY", "consumption_type": "key", "reference_elements": ["PK/ADR2.CLIENT", "PK/ADR2.ADDRNUMBER", "PK/ADR2.PERSNUMBER", "PK/ADR2.DATE_FROM", "PK/ADR2.CONSNUMBER"]}, {"xid": "ADR2", "name": "ADR2", "description": "Telephone Numbers (Business Address Services)", "component_type": "Transparent table", "consumption_type": "table", "components": ["ADR2.CLIENT0001", "ADR2.ADDRNUMBER0002", "ADR2.PERSNUMBER0003", "ADR2.DATE_FROM0004", "ADR2.CONSNUMBER0005", "ADR2.COUNTRY0006", "ADR2.FLGDEFAULT0007", "ADR2.FLG_NOUSE0008", "ADR2.HOME_FLAG0009", "ADR2.TEL_NUMBER0010", "ADR2.TEL_EXTENS0011", "ADR2.TELNR_LONG0012", "ADR2.TELNR_CALL0013", "ADR2.DFT_RECEIV0014", "ADR2.R3_USER0015", "ADR2.VALID_FROM0016", "ADR2.VALID_TO0017"], "custom_properties": {"contflag": "A"}, "location_path": "ADR2", "reference_data": ["PK/ADR2"]}, {"xid": "AT40.SBERFIMA0001", "name": "SBERFIMA", "description": "Calculation Category for Cash Flow Calculator", "component_type": "COLUMN", "consumption_type": "column", "custom_properties": {"rollname": "SBEWFIMA", "domname": "SBEWFIMA", "entitytab": "AT40"}, "location_path": "AT40.SBERFIMA", "key": true, "ordinal_position": 1, "is_field": true, "data_type": "CHAR", "precision": "0", "scale": 0, "size": "4", "allow_null": false}, {"xid": "AT40.SSORTFIMA0002", "name": "SSORTFIMA", "description": "Sort indicator for financial mathematic calculations", "component_type": "COLUMN", "consumption_type": "column", "custom_properties": {"rollname": "TFM_SORT", "domname": "NUMC2"}, "location_path": "AT40.SSORTFIMA", "key": false, "ordinal_position": 2, "is_field": true, "data_type": "NUMC", "precision": "0", "scale": 0, "size": "2", "allow_null": true}, {"xid": "AT40.SVON0003", "name": "SVON", "description": "Int. FiMa indicator to interprete DVALUT/SINCL", "component_type": "COLUMN", "consumption_type": "column", "custom_properties": {"rollname": "TFM_SVON", "domname": "XFELD"}, "location_path": "AT40.SVON", "key": false, "ordinal_position": 3, "is_field": true, "data_type": "CHAR", "precision": "0", "scale": 0, "size": "1", "allow_null": true}, {"xid": "PK/AT40.SBERFIMA", "ordinal_position": 1, "source": "AT40.SBERFIMA0001"}, {"xid": "PK/AT40", "name": "PK/AT40", "component_type": "PRIMARY-KEY", "consumption_type": "key", "reference_elements": ["PK/AT40.SBERFIMA"]}, {"xid": "AT40", "name": "AT40", "description": "Calculation Categories of Cash Flow Calculator", "component_type": "Pooled table", "consumption_type": "table", "components": ["AT40.SBERFIMA0001", "AT40.SSORTFIMA0002", "AT40.SVON0003"], "custom_properties": {"contflag": "S"}, "location_path": "AT40", "reference_data": ["PK/AT40"]}, {"xid": "SPRT1.MANDT0001", "name": "MANDT", "description": "Client", "component_type": "COLUMN", "consumption_type": "column", "custom_properties": {"checktable": "T000", "rollname": "MANDT", "domname": "MANDT", "entitytab": "T000"}, "location_path": "SPRT1.MANDT", "key": true, "ordinal_position": 1, "is_field": true, "data_type": "CLNT", "precision": "0", "scale": 0, "size": "3", "allow_null": false}, {"xid": "SPRT1.PARTNR0002", "name": "PARTNR", "description": "Partner number", "component_type": "COLUMN", "consumption_type": "column", "custom_properties": {"rollname": "SPARTNR", "domname": "SPARTNR", "entitytab": "SPRT1"}, "location_path": "SPRT1.PARTNR", "key": true, "ordinal_position": 2, "is_field": true, "data_type": "CHAR", "precision": "0", "scale": 0, "size": "10", "allow_null": false}, {"xid": "SPRT1.KZPTP0003", "name": "KZPTP", "description": "Person type indicator (legal or natural)", "component_type": "COLUMN", "consumption_type": "column", "custom_properties": {"rollname": "KZPTP", "domname": "XFELD"}, "location_path": "SPRT1.KZPTP", "key": false, "ordinal_position": 3, "is_field": true, "data_type": "CHAR", "precision": "0", "scale": 0, "size": "1", "allow_null": true}, {"xid": "SPRT1.BEGRU0004", "name": "BEGRU", "description": "Authorization Group", "component_type": "COLUMN", "consumption_type": "column", "custom_properties": {"checktable": "TBRG", "rollname": "BRGRU", "domname": "BRGRU", "entitytab": "TBRG"}, "location_path": "SPRT1.BEGRU", "key": false, "ordinal_position": 4, "is_field": true, "data_type": "CHAR", "precision": "0", "scale": 0, "size": "4", "allow_null": true}, {"xid": "SPRT1.CNTRY_H0005", "name": "CNTRY_H", "description": "Country of origin", "component_type": "COLUMN", "consumption_type": "column", "custom_properties": {"checktable": "T005", "rollname": "CNTRY_H", "domname": "LAND1", "entitytab": "T005"}, "location_path": "SPRT1.CNTRY_H", "key": false, "ordinal_position": 5, "is_field": true, "data_type": "CHAR", "precision": "0", "scale": 0, "size": "3", "allow_null": true}, {"xid": "SPRT1.SPRSL0006", "name": "SPRSL", "description": "Language Key", "component_type": "COLUMN", "consumption_type": "column", "custom_properties": {"checktable": "T002", "rollname": "SPRSL", "domname": "SPRAS", "entitytab": "T002", "languflag": "X"}, "location_path": "SPRT1.SPRSL", "key": false, "ordinal_position": 6, "is_field": true, "data_type": "LANG", "precision": "0", "scale": 0, "size": "1", "allow_null": true}, {"xid": "SPRT1.NAME10007", "name": "NAME1", "description": "Name 1", "component_type": "COLUMN", "consumption_type": "column", "custom_properties": {"rollname": "NAME1_GP", "domname": "NAME"}, "location_path": "SPRT1.NAME1", "key": false, "ordinal_position": 7, "is_field": true, "data_type": "CHAR", "precision": "0", "scale": 0, "size": "35", "allow_null": true}, {"xid": "SPRT1.NAME20008", "name": "NAME2", "description": "Name 2", "component_type": "COLUMN", "consumption_type": "column", "custom_properties": {"rollname": "NAME2_GP", "domname": "NAME"}, "location_path": "SPRT1.NAME2", "key": false, "ordinal_position": 8, "is_field": true, "data_type": "CHAR", "precision": "0", "scale": 0, "size": "35", "allow_null": true}, {"xid": "SPRT1.ANRED0009", "name": "ANRED", "description": "Form-of-Address Key", "component_type": "COLUMN", "consumption_type": "column", "custom_properties": {"checktable": "TZV01", "rollname": "SANRED", "domname": "SANRED", "entitytab": "TZV01"}, "location_path": "SPRT1.ANRED", "key": false, "ordinal_position": 9, "is_field": true, "data_type": "NUMC", "precision": "0", "scale": 0, "size": "2", "allow_null": true}, {"xid": "SPRT1.XANRED0010", "name": "XANRED", "description": "Form of address text", "component_type": "COLUMN", "consumption_type": "column", "custom_properties": {"rollname": "XANRED", "domname": "TEXT50"}, "location_path": "SPRT1.XANRED", "key": false, "ordinal_position": 10, "is_field": true, "data_type": "CHAR", "precision": "0", "scale": 0, "size": "50", "allow_null": true}, {"xid": "SPRT1.ANRBF0011", "name": "ANRBF", "description": "Salutation key", "component_type": "COLUMN", "consumption_type": "column", "custom_properties": {"checktable": "TZV02", "rollname": "SANRBF", "domname": "SANRBF", "entitytab": "TZV02"}, "location_path": "SPRT1.ANRBF", "key": false, "ordinal_position": 11, "is_field": true, "data_type": "NUMC", "precision": "0", "scale": 0, "size": "2", "allow_null": true}, {"xid": "SPRT1.XANRBF0012", "name": "XANRBF", "description": "Translation for saluation key", "component_type": "COLUMN", "consumption_type": "column", "custom_properties": {"rollname": "XANRBF", "domname": "TEXT50"}, "location_path": "SPRT1.XANRBF", "key": false, "ordinal_position": 12, "is_field": true, "data_type": "CHAR", "precision": "0", "scale": 0, "size": "50", "allow_null": true}, {"xid": "SPRT1.SORTL0013", "name": "SORTL", "description": "Partner ID", "component_type": "COLUMN", "consumption_type": "column", "custom_properties": {"rollname": "PARTID", "domname": "CHAR10"}, "location_path": "SPRT1.SORTL", "key": false, "ordinal_position": 13, "is_field": true, "data_type": "CHAR", "precision": "0", "scale": 0, "size": "10", "allow_null": true}, {"xid": "SPRT1.PHSRT0014", "name": "PHSRT", "description": "Phonetic search term", "component_type": "COLUMN", "consumption_type": "column", "custom_properties": {"rollname": "PHSRT", "domname": "CHAR20"}, "location_path": "SPRT1.PHSRT", "key": false, "ordinal_position": 14, "is_field": true, "data_type": "CHAR", "precision": "0", "scale": 0, "size": "20", "allow_null": true}, {"xid": "SPRT1.PARTID0015", "name": "PARTID", "description": "IS-IS: Partner identification", "component_type": "COLUMN", "consumption_type": "column", "custom_properties": {"rollname": "VVXPARTID", "domname": "CHAR15"}, "location_path": "SPRT1.PARTID", "key": false, "ordinal_position": 15, "is_field": true, "data_type": "CHAR", "precision": "0", "scale": 0, "size": "15", "allow_null": true}, {"xid": "SPRT1.NAME1_MC0016", "name": "NAME1_MC", "description": "IS-IS: Name 1 in upper case characters for matchcode search", "component_type": "COLUMN", "consumption_type": "column", "custom_properties": {"rollname": "VVXNAME1MC", "domname": "CHAR16"}, "location_path": "SPRT1.NAME1_MC", "key": false, "ordinal_position": 16, "is_field": true, "data_type": "CHAR", "precision": "0", "scale": 0, "size": "16", "allow_null": true}, {"xid": "SPRT1.FLG_ANRBF0017", "name": "FLG_ANRBF", "description": "ID for alternative salutations", "component_type": "COLUMN", "consumption_type": "column", "custom_properties": {"rollname": "VVSABWBA", "domname": "XFELD"}, "location_path": "SPRT1.FLG_ANRBF", "key": false, "ordinal_position": 17, "is_field": true, "data_type": "CHAR", "precision": "0", "scale": 0, "size": "1", "allow_null": true}, {"xid": "SPRT1.FLG_MITARB0018", "name": "FLG_MITARB", "description": "IS-IS: Indicator, partner is employee", "component_type": "COLUMN", "consumption_type": "column", "custom_properties": {"rollname": "VVSMITARB", "domname": "XFELD"}, "location_path": "SPRT1.FLG_MITARB", "key": false, "ordinal_position": 18, "is_field": true, "data_type": "CHAR", "precision": "0", "scale": 0, "size": "1", "allow_null": true}, {"xid": "PK/SPRT1.MANDT", "ordinal_position": 1, "source": "SPRT1.MANDT0001"}, {"xid": "PK/SPRT1.PARTNR", "ordinal_position": 2, "source": "SPRT1.PARTNR0002"}, {"xid": "PK/SPRT1", "name": "PK/SPRT1", "component_type": "PRIMARY-KEY", "consumption_type": "key", "reference_elements": ["PK/SPRT1.MANDT", "PK/SPRT1.PARTNR"]}, {"xid": "SPRT1", "name": "SPRT1", "description": "Partner table", "component_type": "INTTAB", "consumption_type": "table", "components": ["SPRT1.MANDT0001", "SPRT1.PARTNR0002", "SPRT1.KZPTP0003", "SPRT1.BEGRU0004", "SPRT1.CNTRY_H0005", "SPRT1.SPRSL0006", "SPRT1.NAME10007", "SPRT1.NAME20008", "SPRT1.ANRED0009", "SPRT1.XANRED0010", "SPRT1.ANRBF0011", "SPRT1.XANRBF0012", "SPRT1.SORTL0013", "SPRT1.PHSRT0014", "SPRT1.PARTID0015", "SPRT1.NAME1_MC0016", "SPRT1.FLG_ANRBF0017", "SPRT1.FLG_MITARB0018"], "location_path": "SPRT1", "reference_data": ["PK/SPRT1"]}, {"xid": "T001S.MANDT0001", "name": "MANDT", "description": "Client", "component_type": "COLUMN", "consumption_type": "column", "custom_properties": {"checktable": "T000", "rollname": "MANDT", "domname": "MANDT", "entitytab": "T000"}, "location_path": "T001S.MANDT", "key": true, "ordinal_position": 1, "is_field": true, "data_type": "CLNT", "precision": "0", "scale": 0, "size": "3", "allow_null": false}, {"xid": "T001S.BUKRS0002", "name": "BUKRS", "description": "Company Code", "component_type": "COLUMN", "consumption_type": "column", "custom_properties": {"checktable": "T001", "rollname": "BUKRS", "domname": "BUKRS", "entitytab": "T001"}, "location_path": "T001S.BUKRS", "key": true, "ordinal_position": 2, "is_field": true, "data_type": "CHAR", "precision": "0", "scale": 0, "size": "4", "allow_null": false}, {"xid": "T001S.BUSAB0003", "name": "BUSAB", "description": "Accounting clerk", "component_type": "COLUMN", "consumption_type": "column", "custom_properties": {"rollname": "BUSAB", "domname": "BUSAB", "entitytab": "T001S"}, "location_path": "T001S.BUSAB", "key": true, "ordinal_position": 3, "is_field": true, "data_type": "CHAR", "precision": "0", "scale": 0, "size": "2", "allow_null": false}, {"xid": "T001S.SNAME0004", "name": "SNAME", "description": "Name of Accounting Clerk", "component_type": "COLUMN", "consumption_type": "column", "custom_properties": {"rollname": "SNAME_001S", "domname": "TEXT30"}, "location_path": "T001S.SNAME", "key": false, "ordinal_position": 4, "is_field": true, "data_type": "CHAR", "precision": "0", "scale": 0, "size": "30", "allow_null": true}, {"xid": "T001S.USNAM0005", "name": "USNAM", "description": "Name of SAP Office User", "component_type": "COLUMN", "consumption_type": "column", "custom_properties": {"rollname": "FI_USR_NAM", "domname": "USERNAME"}, "location_path": "T001S.USNAM", "key": false, "ordinal_position": 5, "is_field": true, "data_type": "CHAR", "precision": "0", "scale": 0, "size": "12", "allow_null": true}, {"xid": "PK/T001S.MANDT", "ordinal_position": 1, "source": "T001S.MANDT0001"}, {"xid": "PK/T001S.BUKRS", "ordinal_position": 2, "source": "T001S.BUKRS0002"}, {"xid": "PK/T001S.BUSAB", "ordinal_position": 3, "source": "T001S.BUSAB0003"}, {"xid": "PK/T001S", "name": "PK/T001S", "component_type": "PRIMARY-KEY", "consumption_type": "key", "reference_elements": ["PK/T001S.MANDT", "PK/T001S.BUKRS", "PK/T001S.BUSAB"]}, {"xid": "T001S", "name": "T001S", "description": "Accounting Clerks", "component_type": "Pooled table", "consumption_type": "table", "components": ["T001S.MANDT0001", "T001S.BUKRS0002", "T001S.BUSAB0003", "T001S.SNAME0004", "T001S.USNAM0005"], "custom_properties": {"contflag": "C"}, "location_path": "T001S", "reference_data": ["PK/T001S"]}, {"xid": "T006.MANDT0001", "name": "MANDT", "description": "Client", "component_type": "COLUMN", "consumption_type": "column", "custom_properties": {"checktable": "T000", "rollname": "MANDT", "domname": "MANDT", "entitytab": "T000"}, "location_path": "T006.MANDT", "key": true, "ordinal_position": 1, "is_field": true, "data_type": "CLNT", "precision": "0", "scale": 0, "size": "3", "allow_null": false}, {"xid": "T006.MSEHI0002", "name": "MSEHI", "description": "Unit of Measurement", "component_type": "COLUMN", "consumption_type": "column", "custom_properties": {"rollname": "MSEHI", "domname": "MEINS", "entitytab": "T006"}, "location_path": "T006.MSEHI", "key": true, "ordinal_position": 2, "is_field": true, "data_type": "UNIT", "precision": "0", "scale": 0, "size": "3", "allow_null": false}, {"xid": "T006.KZEX30003", "name": "KZEX3", "description": "3-char indicator for external unit of measurement", "component_type": "COLUMN", "consumption_type": "column", "custom_properties": {"rollname": "KZEX3", "domname": "X"}, "location_path": "T006.KZEX3", "key": false, "ordinal_position": 3, "is_field": true, "data_type": "CHAR", "precision": "0", "scale": 0, "size": "1", "allow_null": true}, {"xid": "T006.KZEX60004", "name": "KZEX6", "description": "6-char. ID for external unit of measurement", "component_type": "COLUMN", "consumption_type": "column", "custom_properties": {"rollname": "KZEX6", "domname": "X"}, "location_path": "T006.KZEX6", "key": false, "ordinal_position": 4, "is_field": true, "data_type": "CHAR", "precision": "0", "scale": 0, "size": "1", "allow_null": true}, {"xid": "T006.ANDEC0005", "name": "ANDEC", "description": "No. of decimal places to which rounding should be performed", "component_type": "COLUMN", "consumption_type": "column", "custom_properties": {"rollname": "ANDEC", "domname": "ANDEC"}, "location_path": "T006.ANDEC", "key": false, "ordinal_position": 5, "is_field": true, "data_type": "INT2", "precision": "0", "scale": 0, "size": "5", "allow_null": true}, {"xid": "T006.KZKEH0006", "name": "KZKEH", "description": "Commercial measurement unit ID", "component_type": "COLUMN", "consumption_type": "column", "custom_properties": {"rollname": "KZKEH", "domname": "X"}, "location_path": "T006.KZKEH", "key": false, "ordinal_position": 6, "is_field": true, "data_type": "CHAR", "precision": "0", "scale": 0, "size": "1", "allow_null": true}, {"xid": "T006.KZWOB0007", "name": "KZWOB", "description": "Value-based commitment indicator", "component_type": "COLUMN", "consumption_type": "column", "custom_properties": {"rollname": "KZWOB", "domname": "X"}, "location_path": "T006.KZWOB", "key": false, "ordinal_position": 7, "is_field": true, "data_type": "CHAR", "precision": "0", "scale": 0, "size": "1", "allow_null": true}, {"xid": "T006.KZ1EH0008", "name": "KZ1EH", "description": "Indicator (1) unit (indicator not yet defined)", "component_type": "COLUMN", "consumption_type": "column", "custom_properties": {"rollname": "KZ1EH", "domname": "X"}, "location_path": "T006.KZ1EH", "key": false, "ordinal_position": 8, "is_field": true, "data_type": "CHAR", "precision": "0", "scale": 0, "size": "1", "allow_null": true}, {"xid": "T006.KZ2EH0009", "name": "KZ2EH", "description": "Indicator (2) unit (indicator not yet defined)", "component_type": "COLUMN", "consumption_type": "column", "custom_properties": {"rollname": "KZ2EH", "domname": "X"}, "location_path": "T006.KZ2EH", "key": false, "ordinal_position": 9, "is_field": true, "data_type": "CHAR", "precision": "0", "scale": 0, "size": "1", "allow_null": true}, {"xid": "T006.DIMID0010", "name": "DIMID", "description": "Dimension key", "component_type": "COLUMN", "consumption_type": "column", "custom_properties": {"checktable": "T006D", "rollname": "DIMID", "domname": "DIMID", "entitytab": "T006D"}, "location_path": "T006.DIMID", "key": false, "ordinal_position": 10, "is_field": true, "data_type": "CHAR", "precision": "0", "scale": 0, "size": "6", "allow_null": true}, {"xid": "T006.ZAEHL0011", "name": "ZAEHL", "description": "Numerator for conversion to SI unit", "component_type": "COLUMN", "consumption_type": "column", "custom_properties": {"rollname": "DZAEHL", "domname": "INT4"}, "location_path": "T006.ZAEHL", "key": false, "ordinal_position": 11, "is_field": true, "data_type": "INT4", "precision": "0", "scale": 0, "size": "10", "allow_null": true}, {"xid": "T006.NENNR0012", "name": "NENNR", "description": "Denominator for conversion into SI unit", "component_type": "COLUMN", "consumption_type": "column", "custom_properties": {"rollname": "NENNR", "domname": "INT4"}, "location_path": "T006.NENNR", "key": false, "ordinal_position": 12, "is_field": true, "data_type": "INT4", "precision": "0", "scale": 0, "size": "10", "allow_null": true}, {"xid": "T006.EXP100013", "name": "EXP10", "description": "base ten exponent for conversion to SI unit", "component_type": "COLUMN", "consumption_type": "column", "custom_properties": {"rollname": "EXP10", "domname": "EXP10"}, "location_path": "T006.EXP10", "key": false, "ordinal_position": 13, "is_field": true, "data_type": "INT2", "precision": "0", "scale": 0, "size": "5", "allow_null": true}, {"xid": "T006.ADDKO0014", "name": "ADDKO", "description": "Additive constant for conversion to SI unit", "component_type": "COLUMN", "consumption_type": "column", "custom_properties": {"rollname": "ADDKO", "domname": "ADDKO"}, "location_path": "T006.ADDKO", "key": false, "ordinal_position": 14, "is_field": true, "data_type": "DEC", "precision": "0", "scale": 6, "size": "9", "allow_null": true}, {"xid": "T006.EXPON0015", "name": "EXPON", "description": "Base ten exponent for floating-point display", "component_type": "COLUMN", "consumption_type": "column", "custom_properties": {"rollname": "EXPON", "domname": "EXP10"}, "location_path": "T006.EXPON", "key": false, "ordinal_position": 15, "is_field": true, "data_type": "INT2", "precision": "0", "scale": 0, "size": "5", "allow_null": true}, {"xid": "T006.DECAN0016", "name": "DECAN", "description": "Number of decimal places for number display", "component_type": "COLUMN", "consumption_type": "column", "custom_properties": {"rollname": "DECAN", "domname": "DECAN"}, "location_path": "T006.DECAN", "key": false, "ordinal_position": 16, "is_field": true, "data_type": "INT2", "precision": "0", "scale": 0, "size": "5", "allow_null": true}, {"xid": "T006.ISOCODE0017", "name": "ISOCODE", "description": "ISO code for unit of measurement", "component_type": "COLUMN", "consumption_type": "column", "custom_properties": {"checktable": "T006I", "rollname": "ISOCD_UNIT", "domname": "ISOCD_UNIT", "entitytab": "T006I"}, "location_path": "T006.ISOCODE", "key": false, "ordinal_position": 17, "is_field": true, "data_type": "CHAR", "precision": "0", "scale": 0, "size": "3", "allow_null": true}, {"xid": "T006.PRIMARY0018", "name": "PRIMARY", "description": "Selection field for conversion from ISO code to int. code", "component_type": "COLUMN", "consumption_type": "column", "custom_properties": {"rollname": "ISOCD_MARK", "domname": "X"}, "location_path": "T006.PRIMARY", "key": false, "ordinal_position": 18, "is_field": true, "data_type": "CHAR", "precision": "0", "scale": 0, "size": "1", "allow_null": true}, {"xid": "T006.TEMP_VALUE0019", "name": "TEMP_VALUE", "description": "Temperature", "component_type": "COLUMN", "consumption_type": "column", "custom_properties": {"rollname": "TEMP_VALUE", "domname": "TEMP_VALUE"}, "location_path": "T006.TEMP_VALUE", "key": false, "ordinal_position": 19, "is_field": true, "data_type": "FLTP", "precision": "0", "scale": 16, "size": "16", "allow_null": true}, {"xid": "CT/T006.TEMP_UNIT", "name": "T006", "component_type": "CHECK-TABLE", "consumption_type": "lookup", "reference_elements": ["CT/T006.TEMP_UNIT/MANDT-MANDT"], "text_column": "T006A.MSEHT0006", "language_column": "T006A.SPRAS0002", "constrained_on": "T006.MSEHI0002"}, {"xid": "CT/T006.TEMP_UNIT/MANDT-MANDT", "ordinal_position": 1, "source": "T006.MANDT0001", "target": "T006.MANDT0001"}, {"xid": "T006.TEMP_UNIT0020", "name": "TEMP_UNIT", "description": "Temperature unit", "component_type": "COLUMN", "consumption_type": "column", "custom_properties": {"checktable": "T006", "rollname": "TEMP_UNIT", "domname": "MEINS", "entitytab": "T006"}, "location_path": "T006.TEMP_UNIT", "key": false, "ordinal_position": 20, "is_field": true, "data_type": "UNIT", "precision": "0", "scale": 0, "size": "3", "reference_data": ["CT/T006.TEMP_UNIT"], "allow_null": true}, {"xid": "T006.FAMUNIT0021", "name": "FAMUNIT", "description": "Unit of measurement family", "component_type": "COLUMN", "consumption_type": "column", "custom_properties": {"rollname": "FAMUNIT", "domname": "CHAR1"}, "location_path": "T006.FAMUNIT", "key": false, "ordinal_position": 21, "is_field": true, "data_type": "CHAR", "precision": "0", "scale": 0, "size": "1", "allow_null": true}, {"xid": "T006.PRESS_VAL0022", "name": "PRESS_VAL", "description": "Pressure Value", "component_type": "COLUMN", "consumption_type": "column", "custom_properties": {"rollname": "PRESS_VAL", "domname": "TEMP_VALUE"}, "location_path": "T006.PRESS_VAL", "key": false, "ordinal_position": 22, "is_field": true, "data_type": "FLTP", "precision": "0", "scale": 16, "size": "16", "allow_null": true}, {"xid": "T006.PRESS_UNIT0023", "name": "PRESS_UNIT", "description": "Unit of Pressure", "component_type": "COLUMN", "consumption_type": "column", "custom_properties": {"checktable": "T006", "rollname": "PRESS_UNIT", "domname": "MEINS", "entitytab": "T006"}, "location_path": "T006.PRESS_UNIT", "key": false, "ordinal_position": 23, "is_field": true, "data_type": "UNIT", "precision": "0", "scale": 0, "size": "3", "allow_null": true}, {"xid": "PK/T006.MANDT", "ordinal_position": 1, "source": "T006.MANDT0001"}, {"xid": "PK/T006.MSEHI", "ordinal_position": 2, "source": "T006.MSEHI0002"}, {"xid": "PK/T006", "name": "PK/T006", "component_type": "PRIMARY-KEY", "consumption_type": "key", "reference_elements": ["PK/T006.MANDT", "PK/T006.MSEHI"]}, {"xid": "T006", "name": "T006", "description": "Units of Measurement", "component_type": "Transparent table", "consumption_type": "table", "components": ["T006.MANDT0001", "T006.MSEHI0002", "T006.KZEX30003", "T006.KZEX60004", "T006.ANDEC0005", "T006.KZKEH0006", "T006.KZWOB0007", "T006.KZ1EH0008", "T006.KZ2EH0009", "T006.DIMID0010", "T006.ZAEHL0011", "T006.NENNR0012", "T006.EXP100013", "T006.ADDKO0014", "T006.EXPON0015", "T006.DECAN0016", "T006.ISOCODE0017", "T006.PRIMARY0018", "T006.TEMP_VALUE0019", "T006.TEMP_UNIT0020", "T006.FAMUNIT0021", "T006.PRESS_VAL0022", "T006.PRESS_UNIT0023"], "custom_properties": {"contflag": "C"}, "location_path": "T006", "reference_data": ["PK/T006"]}, {"xid": "T006A.MANDT0001", "name": "MANDT", "description": "Client", "component_type": "COLUMN", "consumption_type": "column", "custom_properties": {"checktable": "T000", "rollname": "MANDT", "domname": "MANDT", "entitytab": "T000"}, "location_path": "T006A.MANDT", "key": true, "ordinal_position": 1, "is_field": true, "data_type": "CLNT", "precision": "0", "scale": 0, "size": "3", "allow_null": false}, {"xid": "T006A.SPRAS0002", "name": "SPRAS", "description": "Language Key", "component_type": "COLUMN", "consumption_type": "column", "custom_properties": {"checktable": "T002", "rollname": "SPRAS", "domname": "SPRAS", "entitytab": "T002", "languflag": "X"}, "location_path": "T006A.SPRAS", "key": true, "ordinal_position": 2, "is_field": true, "data_type": "LANG", "precision": "0", "scale": 0, "size": "1", "allow_null": false}, {"xid": "CT/T006A.MSEHI", "name": "T006", "component_type": "CHECK-TABLE", "consumption_type": "lookup", "reference_elements": ["CT/T006A.MSEHI/MANDT-MANDT"], "text_column": "T006A.MSEHT0006", "language_column": "T006A.SPRAS0002", "constrained_on": "T006.MSEHI0002"}, {"xid": "CT/T006A.MSEHI/MANDT-MANDT", "ordinal_position": 1, "source": "T006A.MANDT0001", "target": "T006.MANDT0001"}, {"xid": "T006A.MSEHI0003", "name": "MSEHI", "description": "Unit of Measurement", "component_type": "COLUMN", "consumption_type": "column", "custom_properties": {"checktable": "T006", "rollname": "MSEHI", "domname": "MEINS", "entitytab": "T006"}, "location_path": "T006A.MSEHI", "key": true, "ordinal_position": 3, "is_field": true, "data_type": "UNIT", "precision": "0", "scale": 0, "size": "3", "reference_data": ["CT/T006A.MSEHI"], "allow_null": false}, {"xid": "T006A.MSEH30004", "name": "MSEH3", "description": "External Unit of Measurement in Commercial Format (3-Char.)", "component_type": "COLUMN", "consumption_type": "column", "custom_properties": {"rollname": "MSEH3", "domname": "MSEH3"}, "location_path": "T006A.MSEH3", "key": false, "ordinal_position": 4, "is_field": true, "data_type": "CHAR", "precision": "0", "scale": 0, "size": "3", "allow_null": true}, {"xid": "T006A.MSEH60005", "name": "MSEH6", "description": "External Unit of Measurement in Technical Format (6-Char.)", "component_type": "COLUMN", "consumption_type": "column", "custom_properties": {"rollname": "MSEH6", "domname": "MSEH6"}, "location_path": "T006A.MSEH6", "key": false, "ordinal_position": 5, "is_field": true, "data_type": "CHAR", "precision": "0", "scale": 0, "size": "6", "allow_null": true}, {"xid": "T006A.MSEHT0006", "name": "MSEHT", "description": "Unit of Measurement Text (Maximum 10 Characters)", "component_type": "COLUMN", "consumption_type": "column", "custom_properties": {"rollname": "MSEHT", "domname": "TEXT10"}, "location_path": "T006A.MSEHT", "key": false, "ordinal_position": 6, "is_field": true, "data_type": "CHAR", "precision": "0", "scale": 0, "size": "10", "allow_null": true}, {"xid": "T006A.MSEHL0007", "name": "MSEHL", "description": "Unit of Measurement Text (Maximum 30 Characters)", "component_type": "COLUMN", "consumption_type": "column", "custom_properties": {"rollname": "MSEHL", "domname": "TEXT30"}, "location_path": "T006A.MSEHL", "key": false, "ordinal_position": 7, "is_field": true, "data_type": "CHAR", "precision": "0", "scale": 0, "size": "30", "allow_null": true}, {"xid": "PK/T006A.MANDT", "ordinal_position": 1, "source": "T006A.MANDT0001"}, {"xid": "PK/T006A.SPRAS", "ordinal_position": 2, "source": "T006A.SPRAS0002"}, {"xid": "PK/T006A.MSEHI", "ordinal_position": 3, "source": "T006A.MSEHI0003"}, {"xid": "PK/T006A", "name": "PK/T006A", "component_type": "PRIMARY-KEY", "consumption_type": "key", "reference_elements": ["PK/T006A.MANDT", "PK/T006A.SPRAS", "PK/T006A.MSEHI"]}, {"xid": "T006A", "name": "T006A", "description": "Assign Internal to Language-Dependent Unit", "component_type": "Transparent table", "consumption_type": "table", "components": ["T006A.MANDT0001", "T006A.SPRAS0002", "T006A.MSEHI0003", "T006A.MSEH30004", "T006A.MSEH60005", "T006A.MSEHT0006", "T006A.MSEHL0007"], "custom_properties": {"contflag": "C"}, "location_path": "T006A", "reference_data": ["PK/T006A"]}, {"xid": "RQ1.rq1", "name": "rq1", "component_type": "SCHEMA", "consumption_type": "schema", "components": ["AD01C_PROF", "ADR2", "AT40", "SPRT1", "T001S", "T006", "T006A"], "location_path": "\"RQ1\".\"rq1\""}]