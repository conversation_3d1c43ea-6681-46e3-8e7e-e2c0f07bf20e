name: Pull Request Validation revive scan

on:
  pull_request:
    types: [opened, synchronize, reopened]

concurrency:
  group: ${{ github.workflow }}-${{ github.ref }}-scan-revive
  cancel-in-progress: true

jobs:
  revive-scan:
    uses: BackOfficeAssoc/devops/.github/workflows/reusable_golang_revive.yml@main
    with:
      revive_exclude_folders_list: "-exclude ./client/client-mock/... -exclude ./progress/client/client-mock/... -exclude ./fixtures/..."
      repo_name: catalog
    secrets: inherit