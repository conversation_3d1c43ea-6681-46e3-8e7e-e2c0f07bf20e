name: Pull Request Validation gosec scan

on:
  pull_request:
    types: [opened, synchronize, reopened]

concurrency:
  group: ${{ github.workflow }}-${{ github.ref }}-scan-gosec
  cancel-in-progress: true

jobs:
  gosec:
    uses: BackOfficeAssoc/devops/.github/workflows/reusable_golang_gosec.yml@main
    with:
      gosec_exclude_folders_list: "-exclude-dir=/client/client-mock -exclude-dir=/progress/client/client-mock -exclude-dir=/fixtures"
      repo_name: catalog