name: publish image

on:
  push:
    branches:
      - develop
    paths-ignore:
      # don't publish new docker image if these are the only files that changed
      - 'README.md'
      - '.gitignore'
      - '**_moq.go'
      - '**_test.go'
      - 'mock.go'
      - '**/testdata/**'
      - 'open-api/**'
      - 'docs/**'

concurrency:
  group: ${{ github.workflow }}-${{ github.ref }}-deploy
  cancel-in-progress: false

jobs:
  publish-deploy:
    uses: BackOfficeAssoc/devops/.github/workflows/reusable_build_publish_deploy_golang.yml@main
    with:
      image_name: boaweb/catalog
      WORKLOAD_DIR_PATH: data-catalog/catalog
      CONTAINER_NAME: catalog
      go_version: '1.23'
    secrets: inherit
