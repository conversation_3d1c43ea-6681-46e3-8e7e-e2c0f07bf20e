name: Pull Request Validation build

on:
  pull_request:
    types: [opened, synchronize, reopened]
    
concurrency:
  group: ${{ github.workflow }}-${{ github.ref }}-build
  cancel-in-progress: true

jobs:
  build:
    runs-on: ubuntu-latest
    env:
      GOPRIVATE: github.com/BackOfficeAssoc/*

    steps:
      - name: Check out repository code
        uses: actions/checkout@v4
      - name: Build Docker Image
        uses: BackOfficeAssoc/devops/.github/actions/build_golang_image@main
        with:
          go_version: "1.23"
          skp_ci_github_token: ${{ secrets.SKP_CI_GITHUB_TOKEN }}
