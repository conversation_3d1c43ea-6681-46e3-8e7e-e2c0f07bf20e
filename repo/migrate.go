package repo

import (
	"context"
	"embed"
	"io"
	"io/fs"

	"github.com/BackOfficeAssoc/pkg/migrator"
)

var t = templates{
	{
		TemporalSchemaTemplate: temporalSQLTemplate,
		SchemaVersion:          1,
		Table:                  "entities",
		ColumnComparison:       "(NEW.id, NEW.tenant_id, NEW.meta_version, NEW.context_id, NEW.model_name, NEW.name, NEW.entity_type, NEW.consumption_type, NEW.description, NEW.active, NEW.ordinal_position, NEW.tags, NEW.base_type, NEW.ref, NEW.mapped_entity, NEW.properties, NEW.custom_properties) IS NOT DISTINCT FROM (OLD.id, OLD.tenant_id, OLD.meta_version, OLD.context_id, OLD.model_name, OLD.name, OLD.entity_type, OLD.consumption_type, OLD.description, OLD.active, OLD.ordinal_position, OLD.tags, OLD.base_type, OLD.ref, OLD.mapped_entity, OLD.properties, OLD.custom_properties)",
	},
	{
		TemporalSchemaTemplate: temporalSQLTemplate,
		SchemaVersion:          1,
		Table:                  "elements",
		ColumnComparison:       "(NEW.id, NEW.tenant_id, NEW.meta_version, NEW.context_id, NEW.parent_id, NEW.model_name, NEW.name, NEW.element_type, NEW.consumption_type, NEW.description, NEW.active, NEW.ordinal_position, NEW.tags, NEW.data_type, NEW.is_required, NEW.is_array, NEW.default_value, NEW.precision, NEW.scale, NEW.size, NEW.enum, NEW.pattern, NEW.ref, NEW.mapped_element, NEW.properties, NEW.custom_properties) IS NOT DISTINCT FROM (OLD.id, OLD.tenant_id, OLD.meta_version, OLD.context_id, OLD.parent_id, OLD.model_name, OLD.name, OLD.element_type, OLD.consumption_type, OLD.description, OLD.active, OLD.ordinal_position, OLD.tags, OLD.data_type, OLD.is_required, OLD.is_array, OLD.default_value, OLD.precision, OLD.scale, OLD.size, OLD.enum, OLD.pattern, OLD.ref, OLD.mapped_element, OLD.properties, OLD.custom_properties)",
	},
	{
		TemporalSchemaTemplate: temporalSQLTemplate,
		SchemaVersion:          1,
		Table:                  "relationships",
		ColumnComparison:       "(NEW.id, NEW.tenant_id, NEW.meta_version, NEW.context_id, NEW.name, NEW.relation_type, NEW.description, NEW.active, NEW.ordinal_position, NEW.tags, NEW.source_ref, NEW.target_ref, NEW.source_id, NEW.target_id, NEW.properties, NEW.custom_properties) IS NOT DISTINCT FROM (OLD.id, OLD.tenant_id, OLD.meta_version, OLD.context_id, OLD.name, OLD.relation_type, OLD.description, OLD.active, OLD.ordinal_position, OLD.tags, OLD.source_ref, OLD.target_ref, OLD.source_id, OLD.target_id, OLD.properties, OLD.custom_properties)",
	},
}

//go:embed schema/*
var schemaFiles embed.FS

func (r *Repository) SchemaFiles() []string {
	files, err := fs.Glob(schemaFiles, "schema/*/*.sql")
	if err != nil {
		// Panic because failing to list files from the embedded FS will only
		// occur at startup time, and would likely indicate a programming error.
		panic(err)
	}
	return append(files, t.fileNames()...)
}

// Test files returns schema migration files, along with test files.
func (r *Repository) TestFiles() []string {
	// Get the schema files
	files := r.SchemaFiles()
	// Get the test (seed) data
	testFiles, err := fs.Glob(schemaFiles, "schema/*/*.test")
	if err != nil {
		panic(err)
	}
	return append(files, testFiles...)
}

func (r *Repository) SchemaReadFile(path string) ([]byte, error) {
	if tplt, ok := t.get(path); ok {
		return tplt.generateSQL(), nil
	}

	f, err := schemaFiles.Open(path)
	if err != nil {
		return nil, err
	}
	defer f.Close()
	return io.ReadAll(f)
}

// Migrate applies migrations as needed to bring the database up to the version
// required by catalog.
func (r *Repository) Migrate(ctx context.Context, flags ...migrator.Flags) error {
	m := migrator.New(r.DB, Schema, r.SchemaReadFile, r.SchemaFiles)
	for _, flag := range flags {
		m = m.WithFlags(flag)
	}
	return m.MigrateDB(ctx)
}
