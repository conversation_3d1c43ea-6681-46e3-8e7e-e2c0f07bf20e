package repo_test

import (
	"context"
	"encoding/json"

	"github.com/BackOfficeAssoc/catalog/repo/dto"
)

// Helper function to create json.RawMessage from map
func toRawMessage(m map[string]interface{}) json.RawMessage {
	if m == nil {
		return json.RawMessage("{}")
	}
	data, _ := json.Marshal(m)
	return json.RawMessage(data)
}

var (
	CreateRelationshipTestFixtures = map[string]dto.ExtendedRelationship{
		"pk_reference": {
			Name:            "PK_Reference",
			Type:            "PK_REF",
			Description:     "Primary key reference",
			Active:          true,
			Source:          dto.Reference{Ref: "#element/n1.entity1.pk.element1"},
			Target:          dto.Reference{Ref: "#element/n1.entity1.element1"},
			Properties:      toRawMessage(map[string]interface{}{}),
			CreatedAt:       "2025-04-07 12:38:27.191 +0530",
			ContextID:       "ctx_333_a",
			TenantID:        "tnt_111",
			ID:              "rel_333",
			MetaVersion:     "meta_1",
			OrdinalPosition: 1,
			CreatedBy:       "user_333_a",
			VersionID:       "version_333_a",
			BranchID:        "branch_111_a",
		},
		"idx_reference": {
			Name:        "IDX_Reference",
			Type:        "IDX_REF",
			Description: "Index reference",
			Active:      true,
			Source:      dto.Reference{Ref: "#element/n1.entity1.idx.element1"},
			Target:      dto.Reference{Ref: "#element/n1.entity1.element1"},
			Properties: toRawMessage(map[string]interface{}{
				"is_ascending":       true,
				"is_included_column": false,
			}),
			CreatedAt:       "2025-04-07 12:38:27.191 +0530",
			ContextID:       "ctx_333_a",
			TenantID:        "tnt_111",
			ID:              "rel_334",
			MetaVersion:     "meta_1",
			OrdinalPosition: 1,
			CreatedBy:       "user_333_a",
			VersionID:       "version_333_a",
			BranchID:        "branch_111_a",
		},
		"fk_ref_source": {
			Name:            "FK_Ref_Source",
			Type:            "FK_REF_SOURCE",
			Description:     "Foreign key source reference",
			Active:          true,
			Source:          dto.Reference{Ref: "#element/n1.entity1.fk.element1"},
			Target:          dto.Reference{Ref: "#element/n1.entity1.element1"},
			Properties:      toRawMessage(map[string]interface{}{}),
			CreatedAt:       "2025-04-07 12:38:27.191 +0530",
			ContextID:       "ctx_333_a",
			TenantID:        "tnt_111",
			ID:              "rel_336",
			MetaVersion:     "meta_1",
			OrdinalPosition: 1,
			CreatedBy:       "user_333_a",
			VersionID:       "version_333_a",
			BranchID:        "branch_111_a",
		},
		"fk_ref_target": {
			Name:            "FK_Ref_Target",
			Type:            "FK_REF_TARGET",
			Description:     "Foreign key target reference",
			Active:          true,
			Source:          dto.Reference{Ref: "#element/n1.entity1.fk.element1"},
			Target:          dto.Reference{Ref: "#element/n1.entity2.element1"},
			Properties:      toRawMessage(map[string]interface{}{}),
			CreatedAt:       "2025-04-07 12:38:27.191 +0530",
			ContextID:       "ctx_333_a",
			TenantID:        "tnt_111",
			ID:              "rel_337",
			MetaVersion:     "meta_1",
			OrdinalPosition: 1,
			CreatedBy:       "user_333_a",
			VersionID:       "version_333_a",
			BranchID:        "branch_111_a",
		},
	}
)

func (s *Suite) TestCreateRelationship() {
	var (
		ctx = context.Background()
	)

	// Skip this test for now as it requires a mock for the ID generator
	s.T().Skip("Skipping test as it requires a mock for the ID generator")

	testCases := []struct {
		name string
		err  error
	}{
		{"pk_reference", nil},
		{"idx_reference", nil},
		{"fk_ref_source", nil},
		{"fk_ref_target", nil},
	}

	for _, tt := range testCases {
		s.Run(tt.name, func() {
			req := CreateRelationshipTestFixtures[tt.name]
			var createRelationships dto.Relationships
			createRelationships = append(createRelationships, req)

			err := s.Repository.CreateRelationships(ctx, nil, createRelationships)
			s.Require().ErrorIs(err, tt.err)
			if tt.err != nil {
				return // to the next test
			}
			s.NoError(err, "create resulted in an unexpected error")

			// Query to find the relationship
			tenantID := req.TenantID
			id := req.ID
			relationships, err := s.Repository.FindRelationships(ctx, dto.FindRelationshipQuery{
				TenantID: tenantID,
				ID:       &id,
			})

			s.NoError(err, "find resulted in an unexpected error")
			s.Len(relationships, 1, "find resulted in unexpected number of results")
		})
	}
}

func (s *Suite) TestDeleteRelationships() {
	var (
		ctx = context.Background()
	)

	testCases := []struct {
		TenantID string
		ID       string
	}{
		{"tnt_111", "rel_333"}, // PK_REF
		{"tnt_111", "rel_334"}, // IDX_REF
		{"tnt_111", "rel_336"}, // FK_REF_SOURCE
		{"tnt_111", "rel_337"}, // FK_REF_TARGET
	}

	for _, tt := range testCases {
		s.Run(tt.TenantID, func() {
			deleteRelationshipParams := dto.DeleteRelationshipParams{
				TenantID: tt.TenantID,
				ID:       tt.ID,
			}

			rowsAffected, err := s.Repository.DeleteRelationships(ctx, nil, deleteRelationshipParams)
			s.NoError(err, "unexpected error from Delete Relationship")
			s.Equal(int64(0), *&rowsAffected.RelationshipsDeleted, "unexpected rowsAffected returned from Delete Relationship")
		})
	}
}
