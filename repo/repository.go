// Package repo defines a repository interface.
package repo

import (
	"context"
	"database/sql"
	"errors"
	"fmt"

	"github.com/jmoiron/sqlx"
	"github.com/lib/pq"

	"github.com/BackOfficeAssoc/catalog/models"
	"github.com/BackOfficeAssoc/catalog/repo/dto"
	pgrepo "github.com/BackOfficeAssoc/pkg/pg-repo"
	"github.com/BackOfficeAssoc/pkg/probe"
)

const Schema = "catalog"

func Connect(connStr string) (*sqlx.DB, error) {
	db, err := pgrepo.ConnectToDB(connStr, probe.Logger())
	if err != nil {
		return nil, err
	}
	return db, err
}

type SampleStore interface {
	CreateSample(request dto.CreateSampleInput, createdBy, tenantID string) (id string, err error)
	FindSamples(query dto.SampleQuery) ([]dto.Sample, error)
	FetchSample(id string, tenantID *string) (dto.Sample, error)
	UpdateSample(id string, request dto.UpdateSampleInput, tenantID *string) error
	DeleteSample(id string, tenantID *string) error
}

type DataModelStore interface {
	CreateEntities(ctx context.Context, tx *sql.Tx, entities dto.Entities) error
	// Updated to use dto.FindEntityElementsQuery from repo/dto/datamodel.go
	FindEntityElements(ctx context.Context, query dto.FindEntityElementsQuery) (dto.EntityElements, error)
	CreateEntityElements(ctx context.Context, tx *sql.Tx, parentID string, entityElements dto.EntityElements) error
	DeleteEntityElements(ctx context.Context, tx *sql.Tx, params dto.DeleteElementParams) (*dto.ExecutionElementDeleteResult, error)

	// Relationship functions
	FindRelationships(ctx context.Context, query dto.FindRelationshipQuery) (dto.Relationships, error)
	CreateRelationships(ctx context.Context, tx *sql.Tx, relationships dto.Relationships) error
	DeleteRelationships(ctx context.Context, tx *sql.Tx, params dto.DeleteRelationshipParams) (*dto.ExecutionRelationshipDeleteResult, error)

	CreateBranch(ctx context.Context, tx *sql.Tx, branch dto.Branch) (string, error)
	CreateVersion(ctx context.Context, tx *sql.Tx, version dto.Version) (string, error)
	GenerateBranchAndVersionID(ctx context.Context, tenant_id string, context_id string, user_id string, tx *sql.Tx) (string, string)

	NavigateDataModel(ctx context.Context, tenantID string, query models.NavigateDataModelQuery) (entities dto.Entities, entityElements dto.EntityElements, relationships dto.Relationships, nextToken string, err error)
}

type Repo interface {
	SampleStore
	DataModelStore
}

type Repository struct {
	*pgrepo.Repository

	// to be removed
	data map[string]dto.Sample
}

func New(db *sqlx.DB) *Repository {
	if db == nil {
		return &Repository{
			data: map[string]dto.Sample{
				"foo": {
					ID:        "foo",
					CreatedBy: "pcp_1",
					Name:      "Foo",
					TenantID:  "tnt_1",
					Type:      "Foo",
				},
				"bar": {
					ID:        "bar",
					CreatedBy: "pcp_1",
					Name:      "Bar",
					TenantID:  "tnt_1",
					Type:      "Bar",
				},
				"baz": {
					ID:        "baz",
					CreatedBy: "pcp_2",
					Name:      "Baz",
					TenantID:  "tnt_2",
					Type:      "Bar",
				},
			},
		}
	}
	return &Repository{Repository: pgrepo.New(db), data: make(map[string]dto.Sample)}
}

func IsNotUniqueError(err error) bool {
	for ; err != nil; err = errors.Unwrap(err) {
		if pqErr, ok := err.(*pq.Error); ok && pqErr.Code == pgrepo.ErrNotUnique {
			return true
		}
	}
	return false
}

// createPartitions will create the underlying partitions for the main tables if
// they do not already exist.
func createPartitions(ctx context.Context, db *sqlx.DB, tntID string) error {
	ctx, span := probe.StartSpan(ctx, "repo.createPartitions")
	defer span.End()

	// Ensure that the tenant ID is safe to use in the table names.
	if !validTenantID(tntID) {
		return fmt.Errorf("%w: '%s'", ErrBadTenant, tntID)
	}

	// Create entities parition, if it does not already exist for the tenant.
	_, err := db.ExecContext(ctx, fmt.Sprintf(`CREATE TABLE IF NOT EXISTS entities_%[1]s PARTITION OF entities FOR VALUES IN ('%[1]s');`, tntID))
	if err != nil {
		return fmt.Errorf("failed to create partition on entities for tenant '%s': %w", tntID, err)
	}
	_, err = db.ExecContext(ctx, fmt.Sprintf(`CREATE TABLE IF NOT EXISTS entities_history_%[1]s PARTITION OF entities_history FOR VALUES IN ('%[1]s');`, tntID))
	if err != nil {
		return fmt.Errorf("failed to create partition on entities_history for tenant '%s': %w", tntID, err)
	}

	// Create the elements partition for this tenant, if it does not already exist.
	_, err = db.ExecContext(ctx, fmt.Sprintf(`CREATE TABLE IF NOT EXISTS elements_%[1]s PARTITION OF elements FOR VALUES IN ('%[1]s');`, tntID))
	if err != nil {
		return fmt.Errorf("failed to create partition on elements for tenant '%s': %w", tntID, err)
	}
	_, err = db.ExecContext(ctx, fmt.Sprintf(`CREATE TABLE IF NOT EXISTS elements_history_%[1]s PARTITION OF elements_history FOR VALUES IN ('%[1]s');`, tntID))
	if err != nil {
		return fmt.Errorf("failed to create partition on elements_history for tenant '%s': %w", tntID, err)
	}

	// Create relationships parition, if it does not already exist for the tenant.
	_, err = db.ExecContext(ctx, fmt.Sprintf(`CREATE TABLE IF NOT EXISTS relationships_%[1]s PARTITION OF relationships FOR VALUES IN ('%[1]s');`, tntID))
	if err != nil {
		return fmt.Errorf("failed to create partition on relationships for tenant '%s': %w", tntID, err)
	}
	_, err = db.ExecContext(ctx, fmt.Sprintf(`CREATE TABLE IF NOT EXISTS relationships_history_%[1]s PARTITION OF relationships_history FOR VALUES IN ('%[1]s');`, tntID))
	if err != nil {
		return fmt.Errorf("failed to create partition on relationships_history for tenant '%s': %w", tntID, err)
	}

	return nil
}

// RepositoryInterface has been removed as its role is covered by the Repo interface
// (which embeds DataModelStore and SampleStore) and the direct implementation
// by the Repository struct.
