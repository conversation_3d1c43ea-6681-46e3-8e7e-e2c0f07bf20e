package repo

import (
	"context"
	"database/sql"
	"fmt"
	"time"

	"github.com/BackOfficeAssoc/catalog/repo/dto"
	"github.com/BackOfficeAssoc/pkg/crunch"
)

const (
	sqlFindVersions = `
	SELECT id, tenant_id, context_id, branch_id, version_tag, started_at, completed_at, import_id, created_at, created_by
	FROM %[1]s v
		%[2]s --where
		%[3]s --order by
		%[4]s --limit
	`
	sqlInsertVersions = `
	INSERT INTO %[1]s (id, tenant_id, context_id, branch_id, version_tag, started_at, completed_at, import_id, created_at, created_by)
	VALUES ($1, $2, $3, $4, $5, $6, $7, $8, DEFAULT, $9)
	RETURNING id, created_at
	`
	sqlDeleteVersions = `DELETE FROM %[1]s WHERE tenant_id = $1 AND id = $2`
)

func getVersionsTableName(tenantID string) string {
	return fmt.Sprintf("%s.versions", Schema)
}

// FindVersions retrieves versions from the database based on the provided query
func (r *Repository) FindVersions(ctx context.Context, query dto.FindVersionQuery) (dto.Versions, error) {
	var versions []dto.Version

	if err := query.Validate(); err != nil {
		return nil, fmt.Errorf("query validation failed: %w", err)
	}

	sql := fmt.Sprintf(sqlFindVersions, getVersionsTableName(""), query.Where(), query.OrderBy(), query.Limit())
	if err := r.NamedInSelect(ctx, &versions, sql, query, nil); err != nil {
		return nil, fmt.Errorf("find versions statement failed: %w", err)
	}
	return versions, nil
}

func (r *Repository) CreateVersion(ctx context.Context, tx *sql.Tx, version dto.Version) (string, error) {
	var err error
	if tx == nil {
		tx, err = r.DB.BeginTx(ctx, nil)
		if err != nil {
			return "", fmt.Errorf("begin transaction failed: %w", err)
		}
		defer tx.Rollback()
	}

	sqlInsertStmt := fmt.Sprintf(sqlInsertVersions, getVersionsTableName(""))
	stmt, err := tx.PrepareContext(ctx, sqlInsertStmt)
	if err != nil {
		return "", fmt.Errorf("failed to prepare statement: %w", err)
	}
	defer stmt.Close()

	version.StartedAt = time.Now()
	version.CompletedAt = time.Now().Add(time.Second + 5)
	// Generate ID using crunch package with "ver" prefix
	id, err := crunch.Generate("ver")
	if err != nil {
		return "", fmt.Errorf("failed to generate ID: %w", err)
	}
	var CreatedAt time.Time
	err = stmt.QueryRowContext(ctx,
		id,
		version.TenantID,
		version.ContextID,
		version.BranchID,
		version.VersionTag,
		version.StartedAt,
		version.CompletedAt,
		version.ImportID,
		// created_at is handled by DEFAULT in the SQL statement
		version.CreatedBy,
	).Scan(&version.ID, &CreatedAt)
	defer stmt.Close()

	if err != nil {
		if err == sql.ErrNoRows {
			return "", fmt.Errorf("no rows returned: %w", err)
		}
		return "", fmt.Errorf("insert version statement failed: %w", err)
	}
	version.CreatedAt = CreatedAt
	if err := tx.Commit(); err != nil {
		return "", fmt.Errorf("commit transaction failed: %w", err)
	}
	return id, nil
}

func (r *Repository) DeleteVersion(ctx context.Context, tx *sql.Tx, params dto.DeleteVersionParams) error {
	var err error
	if tx == nil {
		tx, err = r.DB.BeginTx(ctx, nil)
		if err != nil {
			return fmt.Errorf("begin transaction failed: %w", err)
		}
		defer tx.Rollback()
	}

	sqlDeleteStmt := fmt.Sprintf(sqlDeleteVersions, getVersionsTableName(""))
	stmt, err := tx.PrepareContext(ctx, sqlDeleteStmt)
	if err != nil {
		return fmt.Errorf("failed to prepare statement: %w", err)
	}
	defer stmt.Close()

	_, err = stmt.ExecContext(ctx, params.TenantID, params.ID)
	if err != nil {
		return fmt.Errorf("failed to delete version: %w", err)
	}

	if err := tx.Commit(); err != nil {
		return fmt.Errorf("commit transaction failed: %w", err)
	}
	return nil
}
