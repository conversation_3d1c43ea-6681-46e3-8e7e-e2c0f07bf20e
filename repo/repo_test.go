package repo_test

import (
	"testing"

	"github.com/stretchr/testify/suite"

	"github.com/BackOfficeAssoc/catalog/repo"
	testpg "github.com/BackOfficeAssoc/pkg/test-pg"
)

type Suite struct {
	*repo.Repository
	testpg.TestSuite
}

func (s *Suite) SetupTest() {
	s.TestSuite.SetupTest()
	s.Repository = repo.New(s.DB)
}

func TestRepo(t *testing.T) {
	if testing.Short() {
		t.Skip("Skipping database tests in short mode")
	}
	s := new(Suite)
	getFiles := s.Repository.SchemaFiles()
	s.SetMigrator(repo.Schema, s.Repository.SchemaReadFile, func() []string { return getFiles })
	suite.Run(t, s)
}
