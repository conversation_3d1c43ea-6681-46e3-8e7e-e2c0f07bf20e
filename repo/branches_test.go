package repo_test

import (
	"context"
	"time"

	dto "github.com/BackOfficeAssoc/catalog/repo/dto"
)

var (
	CreateBranchTestFixtures = map[string]dto.Branch{
		"branch_1": {
			ID:             "branch_1",
			TenantID:       "tenant_1",
			Name:           "Branch 1",
			FromBranchID:   nil,
			FromVersionID:  nil,
			FromVersionTag: nil,
			CreatedAt:      time.Now(),
			CreatedBy:      "user_1",
			ModifiedAt:     nil,
			ModifiedBy:     nil,
			DeletedAt:      nil,
			DeletedBy:      nil,
		},
		"branch_default": {
			TenantID: "tenant_1",
		},
	}
)

func (s *Suite) TestCreateBranch() {
	var (
		ctx = context.Background()
	)

	testCases := []struct {
		name string
		err  error
	}{
		{"branch_default", nil},
		{"branch_default", nil},
	}

	for _, tt := range testCases {
		s.Run(tt.name, func() {
			req := CreateBranchTestFixtures[tt.name]
			_, err := s.Repository.CreateBranch(ctx, nil, req)
			if err != nil {
				s.T().Errorf("CreateBranch() error = %v", err)
			}
		})
	}
}

func (s *Suite) TestDeleteBranch() {
	var (
		ctx = context.Background()
	)

	testCases := []struct {
		name string
		err  error
	}{
		{"branch_1", nil},
	}

	for _, tt := range testCases {
		s.Run(tt.name, func() {
			req := CreateBranchTestFixtures[tt.name]
			err := s.Repository.DeleteBranch(ctx, nil, dto.DeleteBranchParams{
				TenantID: req.TenantID,
				ID:       req.ID})
			if err != nil {
				s.T().Errorf("DeleteBranch() error = %v", err)
			}
		})
	}
}
