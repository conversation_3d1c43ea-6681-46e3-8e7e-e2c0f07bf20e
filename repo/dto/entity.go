package dto

import (
	"encoding/json"
	"time"

	"github.com/BackOfficeAssoc/catalog/models"
)

type Entity struct {
	TenantID         string          `db:"tenant_id"`
	ID               string          `db:"id"`
	MetaVersion      string          `db:"meta_version"`
	ContextID        string          `db:"context_id"`
	Type             string          `db:"entity_type"`
	ConsumptionType  string          `db:"consumption_type"`
	OrdinalPosition  int             `db:"ordinal_position"`
	BaseType         string          `db:"base_type"`
	Ref              string          `db:"ref"`
	MappedEntity     *string         `db:"mapped_entity"`
	CreatedAt        string          `db:"created_at"`
	CreatedBy        string          `db:"created_by"`
	VersionID        string          `db:"version_id"`
	BranchID         string          `db:"branch_id"`
	ModelName        string          `db:"model_name"`
	Name             string          `db:"name"`
	Description      string          `db:"description,omitempty"`
	Active           bool            `db:"active"`
	Properties       *PropertiesMap  `db:"properties"`        // Changed to pointer with custom type
	CustomProperties *PropertiesMap  `db:"custom_properties"` // Changed to pointer with custom type
	Tags             []string        `db:"tags"`
	EntityElements   []EntityElement `db:"entity_elements"`
}

// PropertiesMap is a custom type to handle JSON properties from the database
type PropertiesMap map[string]interface{}

// Scan implements the sql.Scanner interface for PropertiesMap
func (p *PropertiesMap) Scan(value interface{}) error {
	if value == nil {
		*p = make(PropertiesMap)
		return nil
	}

	switch v := value.(type) {
	case []byte:
		if len(v) == 0 {
			*p = make(PropertiesMap)
			return nil
		}
		return json.Unmarshal(v, p)
	case string:
		if v == "" {
			*p = make(PropertiesMap)
			return nil
		}
		return json.Unmarshal([]byte(v), p)
	default:
		// Handle any other types that might come from the database
		*p = make(PropertiesMap)
		return nil
	}
}

type Entities []Entity

// FromModel converts a models.Entity to dto.Entity
func (e *Entity) FromModel(m models.Entity) {
	e.ModelName = m.ModelName
	e.Name = m.Name
	e.Type = m.Type
	e.ConsumptionType = m.ConsumptionType
	e.Description = m.Description
	e.Active = m.Active
	e.Properties = (*PropertiesMap)(&m.Properties)
	e.CustomProperties = (*PropertiesMap)(&m.CustomProperties)
	e.Tags = m.Tags
	e.TenantID = m.TenantID
	e.ID = m.ID
	e.MetaVersion = m.MetaVersion
	e.ContextID = m.ContextID
	e.Type = m.Type // Set Type from Type for backward compatibility
	e.OrdinalPosition = m.OrdinalPosition
	e.BaseType = m.BaseType
	e.Ref = m.Ref
	e.MappedEntity = m.MappedEntity
	e.CreatedAt = m.CreatedAt
	e.CreatedBy = m.CreatedBy
	e.VersionID = m.VersionID
	e.BranchID = m.BranchID

	// Convert entity elements
	e.EntityElements = make([]EntityElement, len(m.EntityElements))
	for i, element := range m.EntityElements {
		var entityElement EntityElement
		entityElement.FromModel(element)
		e.EntityElements[i] = entityElement
	}
}

// ToModel converts a dto.Entity to models.Entity
func (e Entity) ToModel() models.Entity {
	model := models.Entity{
		ModelName:       e.ModelName,
		Name:            e.Name,
		Type:            e.Type,
		ConsumptionType: e.ConsumptionType,
		Description:     e.Description,
		Active:          e.Active,
		Tags:            e.Tags,
		TenantID:        e.TenantID,
		ID:              e.ID,
		MetaVersion:     e.MetaVersion,
		ContextID:       e.ContextID,
		OrdinalPosition: e.OrdinalPosition,
		BaseType:        e.BaseType,
		Ref:             e.Ref,
		MappedEntity:    e.MappedEntity,
		CreatedAt:       e.CreatedAt,
		CreatedBy:       e.CreatedBy,
		VersionID:       e.VersionID,
		BranchID:        e.BranchID,
	}

	if e.Properties != nil {
		model.Properties = (map[string]interface{})(*e.Properties)
	} else {
		model.Properties = make(map[string]interface{})
	}

	if e.CustomProperties != nil {
		model.CustomProperties = (map[string]interface{})(*e.CustomProperties)
	} else {
		model.CustomProperties = make(map[string]interface{})
	}

	// Convert entity elements
	model.EntityElements = make([]models.EntityElement, len(e.EntityElements))
	for i, element := range e.EntityElements {
		model.EntityElements[i] = *element.ToModel()
	}

	return model
}

type FindEntityQuery struct {
	TenantID  string  `db:"tenant_id"`
	ID        *string `db:"id"`
	Name      *string `db:"name"`
	ContextID *string `db:"context_id"` // Added for navigation support

	After *time.Time `db:"after"`
	First *int       `db:"first"`
}

func (q *FindEntityQuery) FromModel(m models.FindEntityQuery, namespace string) error {
	if m.After != nil {
		after, err := cursorToTime(m.After)
		if err != nil {
			return err
		}
		q.After = after
	}

	q.TenantID = m.TenantID
	q.Name = m.Name
	q.ID = m.ID
	q.First = m.First

	return q.Validate()
}

func (q *FindEntityQuery) Validate() error {
	if q.TenantID == "" {
		return ErrNoTenant
	}

	return nil
}

func (q FindEntityQuery) Limit() string {
	if q.First == nil {
		return ""
	}
	return "LIMIT :first "
}

func (q FindEntityQuery) OrderBy() string {
	return "ORDER BY created_at DESC "
}

func (q FindEntityQuery) Where() string {
	where := "WHERE 1=1 "
	where += q.tenantFilter()
	where += q.afterFilter()
	where += q.idFilter()
	where += q.nameFilter()
	where += q.contextFilter()

	return where
}

func (q FindEntityQuery) afterFilter() string {
	if q.After == nil {
		return ""
	}
	return "AND e.created_at < :after "
}

func (q FindEntityQuery) tenantFilter() string {
	if q.TenantID == "" {
		return ""
	}
	return "AND tenant_id=:tenant_id "
}

func (q FindEntityQuery) idFilter() string {
	if q.ID == nil {
		return ""
	}
	return "AND id=:id "
}

func (q FindEntityQuery) nameFilter() string {
	if q.Name == nil {
		return ""
	}
	return "AND name=:name "
}

func (q FindEntityQuery) contextFilter() string {
	if q.ContextID == nil {
		return ""
	}
	return "AND context_id=:context_id "
}

type DeleteEntityParams struct {
	TenantID string `db:"tenant_id"`
	ID       string `db:"id"`
}

type ExecutionEntityDeleteResult struct {
	EntitiesDeleted int64
}
