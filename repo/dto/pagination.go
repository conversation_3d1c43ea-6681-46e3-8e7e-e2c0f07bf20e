package dto

import (
	"encoding/base64"
	"fmt"
	"time"
)

func timeToCursor(t time.Time) string {
	timeString := t.Format(time.RFC3339)
	return base64.StdEncoding.EncodeToString([]byte(timeString))
}

func cursorToTime(cursor *string) (*time.Time, error) {
	if cursor == nil || *cursor == "" {
		return nil, nil
	}
	b, err := base64.StdEncoding.DecodeString(*cursor)
	if err != nil {
		return nil, fmt.Errorf("invalid cursor %q", *cursor)
	}
	timeString := string(b)

	tim, err := time.Parse(time.RFC3339, timeString)
	if err != nil {
		return nil, fmt.Errorf("could not get valid value from cursor %q", *cursor)
	}
	return &tim, nil
}
