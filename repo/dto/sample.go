package dto

import "github.com/BackOfficeAssoc/catalog/models"

type Sample struct {
	ID        string
	CreatedBy string
	Name      string
	TenantID  string
	Type      string
}

func (dto *Sample) ToModel() models.Sample {
	m := models.Sample{
		ID:        dto.ID,
		CreatedBy: dto.CreatedBy,
		Name:      dto.Name,
		TenantID:  dto.TenantID,
	}
	m.Type.Parse(dto.Type)
	return m
}

type SampleQuery struct {
	CreatedBy *string
	Name      *string
	TenantID  *string
	Type      *string
}

func (q *SampleQuery) FromModel(m models.SampleQuery) {
	q.CreatedBy = m.CreatedBy
	q.Name = m.Name
	q.TenantID = m.TenantID
	q.Type = m.Type
}

type CreateSampleInput struct {
	Name string
	Type string
}

func (dto *CreateSampleInput) FromModel(m models.CreateSampleInput) {
	dto.Name = m.Name
	dto.Type = m.Type.String()
}

type UpdateSampleInput struct {
	Name string
}

func (dto *UpdateSampleInput) FromModel(m models.UpdateSampleInput) {
	dto.Name = m.Name
}
