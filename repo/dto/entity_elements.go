package dto

import (
	"fmt"
	"strings"

	"github.com/BackOfficeAssoc/catalog/models"
)

type EntityElement struct {
	TenantID         string         `db:"tenant_id"`
	ID               string         `db:"id"`
	MetaVersion      string         `db:"meta_version"`
	ContextID        string         `db:"context_id"`
	ParentID         string         `db:"parent_id"`
	ModelName        string         `db:"model_name"`
	Name             string         `db:"name"`
	Type             string         `db:"element_type"`
	ConsumptionType  string         `db:"consumption_type"`
	Description      string         `db:"description"`
	Active           bool           `db:"active"`
	OrdinalPosition  int            `db:"ordinal_position"`
	DataType         string         `db:"data_type"`
	DefaultValue     string         `db:"default_value"`
	Precision        int            `db:"precision"`
	Scale            int            `db:"scale"`
	MaxLength        int            `db:"size"`
	IsRequired       bool           `db:"is_required"`
	IsArray          bool           `db:"is_array"`
	Ref              string         `db:"ref"`
	MappedElement    string         `db:"mapped_element"`
	CreatedAt        string         `db:"created_at"`
	CreatedBy        string         `db:"created_by"`
	VersionID        string         `db:"version_id"`
	BranchID         string         `db:"branch_id"`
	Tags             []string       `db:"tags"`
	Properties       *PropertiesMap `db:"properties"`        // Changed to pointer with PropertiesMap type
	CustomProperties *PropertiesMap `db:"custom_properties"` // Changed to pointer with PropertiesMap type
}

type EntityElements []EntityElement

func (e *EntityElement) FromModel(m models.EntityElement) {
	e.Active = m.Active
	e.ConsumptionType = m.ConsumptionType
	e.DataType = m.DataType
	e.Description = m.Description
	e.OrdinalPosition = m.OrdinalPosition
	e.IsArray = m.IsArray
	e.IsRequired = m.IsRequired
	e.MaxLength = m.MaxLength
	e.ModelName = m.ModelName
	e.Name = m.Name
	e.IsRequired = m.IsRequired
	e.Precision = m.Precision
	if m.Properties != nil {
		props := PropertiesMap(m.Properties)
		e.Properties = &props
	}
	e.Scale = m.Scale
	e.Tags = m.Tags
	e.Type = m.Type
	if m.CustomProperties != nil {
		customProps := PropertiesMap(m.CustomProperties)
		e.CustomProperties = &customProps
	}
	e.DefaultValue = m.DefaultValue
	e.TenantID = m.TenantID
	e.ID = m.ID
	e.MetaVersion = m.MetaVersion
	e.ContextID = m.ContextID
	e.CreatedBy = m.CreatedBy
	e.VersionID = m.VersionID
	e.BranchID = m.BranchID
}

func (r EntityElement) ToModel() *models.EntityElement {
	e := models.EntityElement{
		ModelName:       r.ModelName,
		Name:            r.Name,
		Type:            r.Type,
		ConsumptionType: r.ConsumptionType,
		Active:          r.Active,
		OrdinalPosition: r.OrdinalPosition,
		DataType:        r.DataType,
		Tags:            r.Tags,
		Description:     r.Description,
		DefaultValue:    r.DefaultValue,
		Precision:       r.Precision,
		Scale:           r.Scale,
		MaxLength:       r.MaxLength,
		IsRequired:      r.IsRequired,
		IsArray:         r.IsArray,
		TenantID:        r.TenantID,
		ID:              r.ID,
		MetaVersion:     r.MetaVersion,
		ContextID:       r.ContextID,
		ParentID:        r.ParentID,
		Ref:             r.Ref,
		MappedElement:   r.MappedElement,
		CreatedAt:       r.CreatedAt,
		CreatedBy:       r.CreatedBy,
		VersionID:       r.VersionID,
		BranchID:        r.BranchID,
	}

	if r.Properties != nil {
		e.Properties = map[string]interface{}(*r.Properties)
	}
	if r.CustomProperties != nil {
		e.CustomProperties = map[string]interface{}(*r.CustomProperties)
	}

	return &e
}

// FindElementQuery represents a query to find elements
type FindElementQuery struct {
	TenantID  string  `form:"tenant_id" url:"tenant_id,omitempty" db:"tenant_id"`
	ID        *string `form:"id" url:"id,omitempty" db:"id"`
	ParentID  *string `form:"parent_id" url:"parent_id,omitempty" db:"parent_id"`
	Name      *string `form:"name" url:"name,omitempty" db:"name"`
	Type      *string `form:"type" url:"type,omitempty" db:"element_type"`
	CreatedBy *string `form:"created_by" url:"created_by,omitempty" db:"created_by"`

	After *string `form:"after" url:"after,omitempty"`
	First *int    `form:"first" url:"first,omitempty"`
}

// Validate validates the query parameters
func (q FindElementQuery) Validate() error {
	if q.TenantID == "" {
		return fmt.Errorf("tenant_id is required")
	}
	return nil
}

// Where builds the WHERE clause for the SQL query
func (q FindElementQuery) Where() string {
	var clauses []string

	if q.TenantID != "" {
		clauses = append(clauses, "tenant_id = :tenant_id")
	}
	if q.ID != nil {
		clauses = append(clauses, "id = :id")
	}
	if q.ParentID != nil {
		clauses = append(clauses, "parent_id = :parent_id")
	}
	if q.Name != nil {
		clauses = append(clauses, "name = :name")
	}
	if q.Type != nil {
		clauses = append(clauses, "element_type = :type")
	}
	if q.CreatedBy != nil {
		clauses = append(clauses, "created_by = :created_by")
	}
	if q.After != nil {
		clauses = append(clauses, "(name, id) > (:after_name, :after_id)")
	}

	if len(clauses) == 0 {
		return ""
	}

	return "WHERE " + strings.Join(clauses, " AND ")
}

// OrderBy builds the ORDER BY clause for the SQL query
func (q FindElementQuery) OrderBy() string {
	return "ORDER BY name ASC, id ASC"
}

// Limit builds the LIMIT clause for the SQL query
func (q FindElementQuery) Limit() string {
	if q.First != nil {
		return fmt.Sprintf("LIMIT %d", *q.First+1)
	}
	return ""
}

// DeleteElementParams represents parameters for deleting an element
type DeleteElementParams struct {
	TenantID string `json:"tenant_id"`
	ID       string `json:"id"`
}

// ExecutionElementDeleteResult represents the result of a delete operation
type ExecutionElementDeleteResult struct {
	ElementsDeleted int64 `json:"elements_deleted"`
}
