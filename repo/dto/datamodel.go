package dto

import (
	"fmt"
	"strings"

	"github.com/BackOfficeAssoc/catalog/models"
)

// Reference represents a reference to an entity or entity element
type Reference struct {
	Ref string `db:"ref"` // Changed from $ref to ref
}

// CreateDataModelInput represents the input for creating a data model
type CreateDataModelInput struct {
	MetaSchemaVersion string                 `db:"meta_schema_version"`
	ContextID         string                 `db:"context_id"`
	Namespace         string                 `db:"namespace"`
	CurrentVersion    string                 `db:"current_version"`
	Entities          []Entity               `db:"entities"`
	Relationships     []ExtendedRelationship `db:"relationships"`
}

// FromModel converts a model.Datamodel to dto.CreateDataModelInput
// FromModel converts a model.Datamodel to dto.CreateDataModelInput
func (dto *CreateDataModelInput) FromModel(m models.DataModel) {
	dto.MetaSchemaVersion = m.MetaSchemaVersion
	dto.Namespace = m.Namespace
	dto.CurrentVersion = m.CurrentVersion

	// Convert entities
	dto.Entities = make([]Entity, len(m.Entities))
	for i, entity := range m.Entities {
		var dtoEntity Entity
		dtoEntity.FromModel(entity)
		dto.Entities[i] = dtoEntity
	}

	// Convert relationships
	dto.Relationships = make([]ExtendedRelationship, len(m.Relationships))
	for i, rel := range m.Relationships {
		var dtoRel ExtendedRelationship
		dtoRel.FromModel(rel)
		dto.Relationships[i] = dtoRel
	}
}

// DataModelResponse represents the response for data model operations
type DataModel struct {
	MetaSchemaVersion string `db:"meta_schema_version"`
	ContextID         string `db:"context_id"`
	Namespace         string `db:"namespace"`
	CurrentVersion    string `db:"current_version"`
	VersionID         string `db:"version_id"`
	CreatedBy         string `db:"created_by"`
	CreatedAt         string `db:"created_at"`
}

// ToModel converts a dto.DataModelResponse to models.DataModelResponse
func (dto DataModel) ToModel() models.DataModel {
	return models.DataModel{
		MetaSchemaVersion: dto.MetaSchemaVersion,
		Namespace:         dto.Namespace,
		CurrentVersion:    dto.CurrentVersion,
		// VersionID:               dto.VersionID,
		// CreatedBy:               dto.CreatedBy,
		// CreatedAt:               dto.CreatedAt,
	}
}

// FindEntitiesQuery represents the parameters for finding entities. and retrieves all fields from the database
type FindEntitiesQuery struct {
	TenantID    string   `form:"tenant_id" url:"tenant_id,omitempty" db:"tenant_id"`
	ContextID   string   `form:"context_id" url:"context_id,omitempty" db:"context_id"`
	Types       []string `form:"types" url:"types,omitempty" db:"entity_type"`
	Filter      *string  `form:"filter" url:"filter,omitempty"`
	First       *int     `form:"first" url:"first,omitempty"`
	Offset      *string  `form:"offset" url:"offset,omitempty"`
	Sort        *string  `form:"sort" url:"sort,omitempty"`
	IsRootLevel bool     `form:"is_root_level" url:"is_root_level,omitempty" db:"-"`

	FilterPattern *string `db:"filter_pattern"`
}

// Validate checks if the query is valid
func (q *FindEntitiesQuery) Validate() error {
	if q.TenantID == "" {
		return fmt.Errorf("TenantID is required for finding entities")
	}
	// ContextID can be empty for root level entities, so no validation here for it.
	if q.Filter != nil && *q.Filter != "" {
		pattern := "%" + strings.TrimSpace(*q.Filter) + "%"
		q.FilterPattern = &pattern
	}
	// Convert Types to uppercase for case-insensitive matching
	if len(q.Types) > 0 {
		for i, t := range q.Types {
			q.Types[i] = strings.ToUpper(strings.TrimSpace(t))
		}
	}
	return nil
}

// Where builds the WHERE clause for the SQL query
func (q FindEntitiesQuery) Where() string {
	var conditions []string
	if !q.IsRootLevel && q.ContextID != "" {
		conditions = append(conditions, "context_id = :context_id")
	}
	if len(q.Types) > 0 {
		// Use UPPER on the column for case-insensitive matching
		conditions = append(conditions, "UPPER(entity_type) IN (:entity_type)")
	}
	if q.FilterPattern != nil && *q.FilterPattern != "" {
		conditions = append(conditions, "(name ILIKE :filter_pattern OR description ILIKE :filter_pattern)")
	}
	if q.Offset != nil && *q.Offset != "" {
		conditions = append(conditions, "id > :offset")
	}
	if len(conditions) == 0 {
		return "WHERE 1=1"
	}
	return "WHERE " + strings.Join(conditions, " AND ")
}

// OrderBy builds the ORDER BY clause for the SQL query
func (q FindEntitiesQuery) OrderBy() string {
	if q.Sort != nil && *q.Sort != "" {
		safeSort := SanitizeSortString(*q.Sort)
		if safeSort != "" {
			return "ORDER BY " + safeSort
		}
	}
	return "ORDER BY name ASC, id ASC" // Default sort
}

// Limit builds the LIMIT clause for the SQL query
func (q FindEntitiesQuery) Limit() string {
	if q.First != nil && *q.First > 0 {
		return fmt.Sprintf("LIMIT %d", *q.First+1)
	}
	return ""
}

// FindEntityElementsParams represents the parameters for finding entity elements. and also retrieves the elements from the database
type FindEntityElementsQuery struct {
	TenantID  string   `form:"tenant_id" url:"tenant_id,omitempty" db:"tenant_id"`
	ParentID  string   `form:"parent_id" url:"parent_id,omitempty" db:"parent_id"`
	ContextID *string  `form:"context_id" url:"context_id,omitempty" db:"context_id"`
	Types     []string `form:"types" url:"types,omitempty" db:"element_type"`
	Filter    *string  `form:"filter" url:"filter,omitempty"`
	First     *int     `form:"first" url:"first,omitempty"`
	Offset    *string  `form:"offset" url:"offset,omitempty"`
	Sort      *string  `form:"sort" url:"sort,omitempty"`

	NameExact           *string `form:"nameExact" url:"nameExact,omitempty" db:"name_exact"`
	NameContains        *string `form:"nameContains" url:"nameContains,omitempty"`
	DescriptionContains *string `form:"descriptionContains" url:"descriptionContains,omitempty"`

	FilterPattern              *string `db:"filter_pattern"`
	NameContainsPattern        *string `db:"name_contains_pattern"`
	DescriptionContainsPattern *string `db:"description_contains_pattern"`
}

// Validate checks if the query is valid
func (q *FindEntityElementsQuery) Validate() error {
	if q.TenantID == "" {
		return fmt.Errorf("TenantID is required for finding entity elements")
	}
	if q.ParentID == "" {
		return fmt.Errorf("ParentID is required for finding entity elements")
	}
	if q.Filter != nil && *q.Filter != "" {
		pattern := "%" + strings.TrimSpace(*q.Filter) + "%"
		q.FilterPattern = &pattern
	}
	if q.NameContains != nil && *q.NameContains != "" {
		pattern := "%" + strings.TrimSpace(*q.NameContains) + "%"
		q.NameContainsPattern = &pattern
	}
	if q.DescriptionContains != nil && *q.DescriptionContains != "" {
		// For to_tsquery, the pattern is different. It's usually space/AND/OR separated terms.
		// We'll assume simple space separation implies AND for now.
		// Example: "term1 term2" becomes "term1 & term2" for to_tsquery.
		// A more robust solution might involve plainto_tsquery or websearch_to_tsquery.
		// For simplicity here, we'll pass the raw string and expect the user to format it if needed,
		// or use a simpler ILIKE if full-text capabilities aren't strictly required by "textFilter" here.
		// Given the schema has a GIN FTS index, we should aim for that.
		// Let's prepare the pattern for to_tsquery, replacing spaces with '&'.
		tsQueryPattern := strings.ReplaceAll(strings.TrimSpace(*q.DescriptionContains), " ", " & ")
		q.DescriptionContainsPattern = &tsQueryPattern
	}
	// Convert Types to uppercase for case-insensitive matching
	if len(q.Types) > 0 {
		for i, t := range q.Types {
			q.Types[i] = strings.ToUpper(strings.TrimSpace(t))
		}
	}
	return nil
}

// Where builds the WHERE clause for the SQL query
func (q FindEntityElementsQuery) Where() string {
	var conditions []string
	conditions = append(conditions, "parent_id = :parent_id")

	if q.ContextID != nil && *q.ContextID != "" {
		conditions = append(conditions, "context_id = :context_id")
	}
	if len(q.Types) > 0 {
		conditions = append(conditions, "UPPER(element_type) IN (:element_type)")
	}
	if q.FilterPattern != nil && *q.FilterPattern != "" {
		conditions = append(conditions, "(name ILIKE :filter_pattern OR description ILIKE :filter_pattern)")
	}
	if q.NameExact != nil && *q.NameExact != "" {
		conditions = append(conditions, "name = :name_exact")
	}
	if q.NameContainsPattern != nil && *q.NameContainsPattern != "" {
		conditions = append(conditions, "name ILIKE :name_contains_pattern")
	}
	if q.DescriptionContainsPattern != nil && *q.DescriptionContainsPattern != "" {
		// Ensure the column name 'description' matches your DB schema for elements table
		conditions = append(conditions, "to_tsvector('english', description) @@ to_tsquery('english', :description_contains_pattern)")
	}
	if q.Offset != nil && *q.Offset != "" {
		conditions = append(conditions, "id > :offset")
	}

	if len(conditions) == 0 {
		return "WHERE 1=1" // Fallback,
	}
	return "WHERE " + strings.Join(conditions, " AND ")
}

// OrderBy builds the ORDER BY clause for the SQL query
func (q FindEntityElementsQuery) OrderBy() string {
	if q.Sort != nil && *q.Sort != "" {
		safeSort := SanitizeSortString(*q.Sort)
		if safeSort != "" {
			return "ORDER BY " + safeSort
		}
	}
	return "ORDER BY name ASC, id ASC"
}

func (q FindEntityElementsQuery) Limit() string {
	if q.First != nil && *q.First > 0 {
		return fmt.Sprintf("LIMIT %d", *q.First+1) // +1 to check for next page
	}
	return "" // No limit
}
