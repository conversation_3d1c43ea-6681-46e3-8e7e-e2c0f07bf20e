package dto

import (
	"time"

	"github.com/BackOfficeAssoc/catalog/models"
)

// Branch represents the branch entity.
type Branch struct {
	ID             string     `db:"id"`
	TenantID       string     `db:"tenant_id"`
	Name           string     `db:"name"`
	FromBranchID   *string    `db:"from_branch_id,omitempty"`
	FromVersionID  *string    `db:"from_version_id,omitempty"`
	FromVersionTag *string    `db:"from_version_tag,omitempty"`
	CreatedAt      time.Time  `db:"created_at"`
	CreatedBy      string     `db:"created_by"`
	ModifiedAt     *time.Time `db:"modified_at,omitempty"`
	ModifiedBy     *string    `db:"modified_by,omitempty"`
	DeletedAt      *time.Time `db:"deleted_at,omitempty"`
	DeletedBy      *string    `db:"deleted_by,omitempty"`
}

type Branches []Branch

func (b *Branch) FromModel(m models.Branch) {
	b.ID = m.ID
	b.TenantID = m.TenantID
	b.Name = m.Name
	b.FromBranchID = m.FromBranchID
	b.FromVersionID = m.FromVersionID
	b.FromVersionTag = m.FromVersionTag
	b.CreatedAt = m.CreatedAt
	b.CreatedBy = m.CreatedBy
	b.ModifiedAt = m.ModifiedAt
	b.ModifiedBy = m.ModifiedBy
	b.DeletedAt = m.DeletedAt
	b.DeletedBy = m.DeletedBy
}
func (b *Branch) ToModel() models.Branch {
	return models.Branch{
		ID:             b.ID,
		TenantID:       b.TenantID,
		Name:           b.Name,
		FromBranchID:   b.FromBranchID,
		FromVersionID:  b.FromVersionID,
		FromVersionTag: b.FromVersionTag,
		CreatedAt:      b.CreatedAt,
		CreatedBy:      b.CreatedBy,
		ModifiedAt:     b.ModifiedAt,
		ModifiedBy:     b.ModifiedBy,
		DeletedAt:      b.DeletedAt,
		DeletedBy:      b.DeletedBy,
	}
}

type FindBranchQuery struct {
	TenantID *string `db:"tenant_id"`
	ID       *string `db:"id"`
	Name     *string `db:"name"`

	After *time.Time `db:"after"`
	First *int       `db:"first"`
}

func (q *FindBranchQuery) FromModel(m models.FindBranchQuery) error {
	if m.After != nil {
		after, err := cursorToTime(m.After)
		if err != nil {
			return err
		}
		q.After = after
	}
	q.TenantID = m.TenantID
	q.Name = m.Name
	q.ID = m.ID
	q.First = m.First
	return q.Validate()
}

func (q *FindBranchQuery) Validate() error {
	if q.TenantID == nil {
		return ErrNoTenant
	}
	return nil
}
func (q FindBranchQuery) Limit() string {
	if q.First == nil {
		return ""
	}
	return "LIMIT :first "
}
func (q FindBranchQuery) OrderBy() string {
	return "ORDER BY created_at DESC "
}
func (q FindBranchQuery) Where() string {
	where := "WHERE 1=1 "
	where += q.tenantFilter()
	where += q.afterFilter()
	where += q.idFilter()
	where += q.nameFilter()

	return where
}
func (q FindBranchQuery) afterFilter() string {
	if q.After == nil {
		return ""
	}
	return "AND created_at < :after "
}
func (q FindBranchQuery) tenantFilter() string {
	if q.TenantID == nil {
		return ""
	}
	return "AND tenant_id = :tenant_id "
}
func (q FindBranchQuery) idFilter() string {
	if q.ID == nil {
		return ""
	}
	return "AND id = :id "
}
func (q FindBranchQuery) nameFilter() string {
	if q.Name == nil {
		return ""
	}
	return "AND name = :name "
}

type DeleteBranchParams struct {
	TenantID string `db:"tenant_id"`
	ID       string `db:"id"`
}
type ExecutionBranchDeleteResult struct {
	BranchesDeleted int64
}
