package repo

import (
	"context"
	"database/sql"
	"fmt"

	"github.com/BackOfficeAssoc/catalog/repo/dto"
)

func (r Repository) GenerateBranchAndVersionID(ctx context.Context, tenant_id string, context_id string, user_id string, tx *sql.Tx) (string, string) {
	branchID, err := r.CreateBranch(ctx, tx, dto.Branch{TenantID: tenant_id, CreatedBy: user_id})
	if err != nil {
		fmt.Printf("Error Branch ID =%s\n", err)
	}
	versionID, err := r.CreateVersion(ctx, tx, dto.Version{TenantID: tenant_id, ContextID: context_id, BranchID: branchID, CreatedBy: user_id})
	if err != nil {
		fmt.Printf("Error Version ID =%s\n", err)
	}
	return branchID, versionID
}
