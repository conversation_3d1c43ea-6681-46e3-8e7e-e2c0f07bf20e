package repo

import (
	"context"
	"database/sql"
	"encoding/json"
	"fmt"

	"github.com/BackOfficeAssoc/catalog/repo/dto"
	"github.com/BackOfficeAssoc/pkg/crunch"
	"github.com/BackOfficeAssoc/pkg/probe"
)

const (
	sqlFindEntityElements = `
	SELECT tenant_id, id, meta_version, context_id, parent_id, model_name, name, element_type, consumption_type, description, active, ordinal_position,
	 data_type, is_required, is_array, default_value, precision, scale, size, ref, mapped_element, created_at, created_by, version_id, branch_id,
	 properties, custom_properties
	 FROM %[1]s
		%[2]s --where
		%[3]s --order by
		%[4]s --limit
	`

	sqlInsertEntityElements = `
    INSERT INTO %[1]s (tenant_id, id, meta_version, context_id, parent_id, model_name, name, element_type, consumption_type, description, active, ordinal_position,
  data_type, is_required, is_array, default_value, precision, scale, size, ref, mapped_element, created_at, created_by, version_id, branch_id,
  properties, custom_properties)
 VALUES ($1, $2, $3, $4, $5, $6, $7, $8, $9, $10, $11, $12, $13, $14, $15, $16, $17, $18, $19, $20, $21, DEFAULT, $22, $23, $24, $25, $26)
     RETURNING id, created_at
  	`

	sqlDeleteEntityElements = `DELETE FROM %[1]s WHERE tenant_id = $1 AND id = $2`
)

func getElementsTableName(tenantID string) string {
	return fmt.Sprintf("%s.elements_%s", Schema, tenantID)
}

// FindEntityElements retrieves entity elements from the database based on the provided query
func (r *Repository) FindEntityElements(ctx context.Context, query dto.FindEntityElementsQuery) (dto.EntityElements, error) {
	var entityElements []dto.EntityElement

	if err := query.Validate(); err != nil {
		return nil, fmt.Errorf("query validation failed: %w", err)
	}

	sql := fmt.Sprintf(sqlFindEntityElements,
		getElementsTableName(query.TenantID),
		query.Where(),
		query.OrderBy(),
		query.Limit(),
	)
	if err := r.NamedInSelect(ctx, &entityElements, sql, query, nil); err != nil {
		return nil, fmt.Errorf("find entity elements statement failed: %w", err)
	}
	return entityElements, nil
}

// FindEntityElementsByTypes has been removed. and is now part of  FindEntityElements function

// CreateEntityElements inserts entity elements into the database
func (r *Repository) CreateEntityElements(ctx context.Context, tx *sql.Tx, parentID string, entityElements dto.EntityElements) error {
	var err error
	if tx == nil {
		tx, err = r.DB.BeginTx(ctx, nil)
		if err != nil {
			return fmt.Errorf("failed to begin transaction: %w", err)
		}
	}

	defer tx.Rollback()

	// Prepare batch insert
	sqlInsertStmt := fmt.Sprintf(sqlInsertEntityElements, getElementsTableName(entityElements[0].TenantID))
	stmt, err := tx.PrepareContext(ctx, sqlInsertStmt)
	if err != nil {
		return fmt.Errorf("failed to prepare statement: %w", err)
	}
	defer stmt.Close()

	for i := range entityElements {
		id, err := crunch.Generate("ele")
		if err != nil {
			return fmt.Errorf("failed to generate ID: %w", err)
		}

		var CreatedAt string
		// Convert Properties and CustomProperties to JSON
		propertiesJSON, err := json.Marshal(entityElements[i].Properties)
		if err != nil {
			return fmt.Errorf("failed to marshal properties to JSON: %w", err)
		}

		customPropertiesJSON, err := json.Marshal(entityElements[i].CustomProperties)
		if err != nil {
			return fmt.Errorf("failed to marshal custom properties to JSON: %w", err)
		}

		// Let the database handle the timestamp with DEFAULT NOW() in the SQL statement
		err = stmt.QueryRowContext(ctx,
			entityElements[i].TenantID,
			id, // Use generated ID instead of entityElements[i].ID
			entityElements[i].MetaVersion,
			entityElements[i].ContextID,
			parentID, // Set the parent ID to link to the entity
			entityElements[i].ModelName,
			entityElements[i].Name,
			entityElements[i].Type,
			entityElements[i].ConsumptionType,
			entityElements[i].Description,
			entityElements[i].Active,
			entityElements[i].OrdinalPosition,
			entityElements[i].DataType,
			entityElements[i].IsRequired,
			entityElements[i].IsArray,
			entityElements[i].DefaultValue,
			entityElements[i].Precision,
			entityElements[i].Scale,
			entityElements[i].MaxLength,
			entityElements[i].Ref,
			entityElements[i].MappedElement,
			// created_at is handled by DEFAULT in the SQL statement
			entityElements[i].CreatedBy,
			entityElements[i].VersionID,
			entityElements[i].BranchID,
			propertiesJSON,       // Use JSON instead of map
			customPropertiesJSON, // Use JSON instead of map
		).Scan(&id, &CreatedAt)
		if err != nil {
			probe.Load(ctx).WithError(err).Emit(ProbeCreateEntityElementError)
			return fmt.Errorf("failed to insert entity element: %w", err)
		}
		entityElements[i].ID = id
		entityElements[i].CreatedAt = CreatedAt
	}

	if err := tx.Commit(); err != nil {
		return fmt.Errorf("failed to commit transaction: %w", err)
	}

	return nil
}

// DeleteEntityElements deletes entity elements from the database
func (r *Repository) DeleteEntityElements(ctx context.Context, tx *sql.Tx, params dto.DeleteElementParams) (*dto.ExecutionElementDeleteResult, error) {
	var err error
	if tx == nil {
		tx, err = r.DB.BeginTx(ctx, nil)
		if err != nil {
			return nil, fmt.Errorf("failed to begin transaction: %w", err)
		}
	}
	defer tx.Rollback()

	sqlDeleteStmt := fmt.Sprintf(sqlDeleteEntityElements, getElementsTableName(params.TenantID))
	entityElementsRes, err := tx.ExecContext(ctx, sqlDeleteStmt, params.TenantID, params.ID)
	if err != nil {
		probe.Load(ctx).WithError(err).Emit(ProbeDeleteEntityElementError)
		return nil, fmt.Errorf("delete from entity elements statement failed: %w", err)
	}
	rowsAffected, err := entityElementsRes.RowsAffected()
	if err != nil {
		return nil, fmt.Errorf("failed to get rows affected: %w", err)
	}

	if err := tx.Commit(); err != nil {
		return nil, fmt.Errorf("failed to commit transaction: %w", err)
	}

	return &dto.ExecutionElementDeleteResult{
		ElementsDeleted: rowsAffected,
	}, nil
}
