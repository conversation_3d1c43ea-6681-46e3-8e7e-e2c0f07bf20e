// Package sample implements the Repository interface.
// We define a simple set of examples that interact with and modify a set of
// cached repo.Sample objects.
package repo

import (
	"strings"

	"github.com/BackOfficeAssoc/catalog/repo/dto"
)

func (r *Repository) CreateSample(request dto.CreateSampleInput, createdBy, tenantID string) (string, error) {
	id := strings.ToLower(request.Name)
	if _, ok := r.data[id]; ok {
		return "", ErrConflict
	}
	r.data[id] = dto.Sample{
		ID:        id,
		CreatedBy: createdBy,
		Name:      request.Name,
		Type:      request.Type,
		TenantID:  tenantID,
	}
	return id, nil
}

func (r *Repository) FindSamples(query dto.SampleQuery) ([]dto.Sample, error) {
	var samples []dto.Sample
	for _, sample := range r.data {
		match := true
		match = match && (query.CreatedBy == nil || *query.CreatedBy == sample.CreatedBy)
		match = match && (query.Name == nil || *query.Name == sample.Name)
		match = match && (query.TenantID == nil || *query.TenantID == sample.TenantID)
		match = match && (query.Type == nil || *query.Type == sample.Type)
		if match {
			samples = append(samples, sample)
		}
	}
	return samples, nil
}

func (r *Repository) FetchSample(id string, tenantID *string) (dto.Sample, error) {
	s, ok := r.data[id]
	if !ok {
		return dto.Sample{}, ErrNotFound
	}
	if tenantID != nil && *tenantID != s.TenantID {
		return dto.Sample{}, ErrNotFound
	}
	return s, nil
}

func (r *Repository) UpdateSample(id string, request dto.UpdateSampleInput, tenantID *string) error {
	s, ok := r.data[id]
	if !ok {
		return ErrNotFound
	}
	if tenantID != nil && *tenantID != s.TenantID {
		return ErrNotFound
	}
	s.Name = request.Name
	r.data[id] = s
	return nil
}

func (r *Repository) DeleteSample(id string, tenantID *string) error {
	s, ok := r.data[id]
	if !ok {
		return nil
	}
	if tenantID != nil && *tenantID != s.TenantID {
		return ErrNotFound
	}
	delete(r.data, id)
	return nil
}
