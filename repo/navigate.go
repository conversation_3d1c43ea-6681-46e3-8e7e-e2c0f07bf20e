package repo

import (
	"context"
	"fmt"

	"github.com/BackOfficeAssoc/catalog/models"
	"github.com/BackOfficeAssoc/catalog/repo/dto"
	"github.com/BackOfficeAssoc/pkg/probe"
)

const (
	MinimumPageSize = 1
	MaximumPageSize = 100
	DefaultPageSize = MaximumPageSize
)

// buildNavigationQueries determines whether to fetch entities or elements based on the query.
func buildNavigationQueries(tenantID string, query models.NavigateDataModelQuery) (*dto.FindEntitiesQuery, *dto.FindEntityElementsQuery, error) {
	// Helper function to get a pointer to a string if it's not empty
	ptrStr := func(s string) *string {
		if s == "" {
			return nil
		}
		return &s
	}
	// Helper function to get a pointer to an int
	ptrInt := func(i int) *int {
		return &i
	}

	// Check if we are fetching elements for a parent entity
	if query.ParentID != "" {
		// Fetching elements for a parent entity
		elementsQuery := &dto.FindEntityElementsQuery{
			TenantID:            tenantID,
			ParentID:            query.ParentID,
			ContextID:           ptrStr(query.ContextID),
			Types:               query.Types,
			Filter:              ptrStr(query.Filter),
			First:               ptrInt(query.First),
			Offset:              ptrStr(query.Offset),
			Sort:                ptrStr(query.Sort),
			NameExact:           ptrStr(query.NameExact),
			NameContains:        ptrStr(query.NameContains),
			DescriptionContains: ptrStr(query.DescriptionContains),
		}
		return nil, elementsQuery, nil
	} else if query.ContextID != "" {
		// Fetching entities within a specific context
		entitiesQuery := &dto.FindEntitiesQuery{
			TenantID:            tenantID, // Added TenantID
			ContextID:           query.ContextID,
			Types:               query.Types,
			Filter:              ptrStr(query.Filter),
			First:               ptrInt(query.First),
			Offset:              ptrStr(query.Offset),
			Sort:                ptrStr(query.Sort),
			NameExact:           ptrStr(query.NameExact),
			NameContains:        ptrStr(query.NameContains),
			DescriptionContains: ptrStr(query.DescriptionContains),
			IsRootLevel:         false,
		}
		return entitiesQuery, nil, nil
	} else if query.ContextID == "" && query.ParentID == "" {
		// Fetching root-level entities (no context_id or parent_id)
		entitiesQuery := &dto.FindEntitiesQuery{
			TenantID:            tenantID, // Added TenantID
			ContextID:           "",       // Explicitly empty
			Types:               query.Types,
			Filter:              ptrStr(query.Filter),
			First:               ptrInt(query.First),
			Offset:              ptrStr(query.Offset),
			Sort:                ptrStr(query.Sort),
			NameExact:           ptrStr(query.NameExact),
			NameContains:        ptrStr(query.NameContains),
			DescriptionContains: ptrStr(query.DescriptionContains),
			IsRootLevel:         true,
		}
		return entitiesQuery, nil, nil
	}

	// If none of the above, it's an invalid state or a case not handled for navigation
	return nil, nil, fmt.Errorf("invalid navigation query: unable to determine target for query %v", query)
}

// NavigateDataModel fetches entities, entity elements, and relationships
// based on the navigation query parameters.
func (r *Repository) NavigateDataModel(
	ctx context.Context,
	tenantID string,
	navQuery models.NavigateDataModelQuery,
) (dto.Entities, dto.EntityElements, dto.Relationships, string, error) {
	ctx, span := probe.StartSpan(ctx, "repo.NavigateDataModel")
	defer span.End()

	// Set default pagination and clamp
	if navQuery.First <= 0 {
		navQuery.First = DefaultPageSize
	}
	navQuery.First = clamp(MinimumPageSize, navQuery.First, MaximumPageSize)

	// Ensure tenant partitions exist
	if err := createPartitions(ctx, r.DB, tenantID); err != nil {
		return nil, nil, nil, "", fmt.Errorf("failed to ensure partitions for tenant %s: %w", tenantID, err)
	}

	var entitiesResult dto.Entities
	var elementsResult dto.EntityElements
	var relationshipsResult dto.Relationships
	var nextToken string
	var err error

	// Get entity or element parameters based on the query
	// Pass tenantID to buildNavigationQueries
	entityParams, elementParams, buildErr := buildNavigationQueries(tenantID, navQuery)
	if buildErr != nil {
		return nil, nil, nil, "", fmt.Errorf("failed to build navigation query: %w", buildErr)
	}

	// Process entity queries first
	if entityParams != nil {
		entitiesResult, err = r.FindEntities(ctx, *entityParams)
		if err != nil {
			return nil, nil, nil, "", err
		}

		// Extract entity reference strings to fetch related relationships
		var entityRefs []string
		if len(entitiesResult) > 0 {
			for _, entity := range entitiesResult {
				if entity.Ref != "" {
					entityRefs = append(entityRefs, entity.Ref)
				} else if entity.ModelName != "" {
					entityRefs = append(entityRefs, entity.ModelName)
				}
				// If entity.ID is also used in source_ref/target_ref, which is not currently the case need to talk to Rashmy about that.
				// entityRefs = append(entityRefs, entity.ID)
			}
		}

		// Always attempt to fetch relationships if entityParams is not empty.
		relationshipQuery := dto.FindRelationshipQuery{
			TenantID:    tenantID,
			ContextID:   &navQuery.ContextID,
			Types:       navQuery.Types,
			RelatedRefs: entityRefs, // if any ref is found so we will get the associated relationship
		}
		relationshipsResult, err = r.FindRelationships(ctx, relationshipQuery)
		if err != nil {
			return entitiesResult, nil, nil, "", fmt.Errorf("failed to fetch relationships for entities: %w", err)
		}

		// Calculate next token for entities (pagination)
		if entityParams.First != nil && len(entitiesResult) == *entityParams.First+1 {
			entitiesResult = entitiesResult[:len(entitiesResult)-1] // Trim the extra one
			// Basic offset pagination for next token
			page := 0
			if entityParams.Offset != nil && *entityParams.Offset != "" {
				fmt.Sscanf(*entityParams.Offset, "%d", &page)
			}
			nextToken = fmt.Sprintf("%d", page+1)
		}

	} else if elementParams != nil {
		elementsResult, err = r.FindEntityElements(ctx, *elementParams)
		if err != nil {
			return nil, nil, nil, "", err
		}

		// Extract element reference strings to fetch related relationships
		var elementRefs []string
		if len(elementsResult) > 0 {
			for _, element := range elementsResult {
				if element.Ref != "" { // Prefer .Ref
					elementRefs = append(elementRefs, element.Ref)
				} else if element.ModelName != "" {
					elementRefs = append(elementRefs, element.ModelName)
				}
			}
		}

		// same for the elements
		relationshipQuery := dto.FindRelationshipQuery{
			TenantID:    tenantID,
			ContextID:   &navQuery.ContextID,
			Types:       navQuery.Types,
			RelatedRefs: elementRefs,
		}
		if elementParams.ContextID != nil && *elementParams.ContextID != "" {
			relationshipQuery.ContextID = elementParams.ContextID // elementParams.ContextID is already *string
		}

		relationshipsResult, err = r.FindRelationships(ctx, relationshipQuery)
		if err != nil {
			return nil, elementsResult, nil, "", fmt.Errorf("failed to fetch relationships for elements: %w", err)
		}

		// Calculate next token for elements (pagination)
		// elementParams.First and elementParams.Offset are pointers
		if elementParams.First != nil && len(elementsResult) == *elementParams.First+1 {
			elementsResult = elementsResult[:len(elementsResult)-1]
			page := 0
			if elementParams.Offset != nil && *elementParams.Offset != "" {
				fmt.Sscanf(*elementParams.Offset, "%d", &page)
			}
			nextToken = fmt.Sprintf("%d", page+1)
		}
	} else {
		return nil, nil, nil, "", fmt.Errorf("invalid query: no entity or element parameters derived")
	}

	return entitiesResult, elementsResult, relationshipsResult, nextToken, nil
}

// clamp is a utility for clamping a value within a range.
func clamp(low, value, high int) int {
	if value < low {
		return low
	}
	if value > high {
		return high
	}
	return value
}
