package repo

import (
	"context"
	"database/sql"
	"fmt"
	"strconv"
	"time"

	"github.com/BackOfficeAssoc/catalog/repo/dto"
	"github.com/BackOfficeAssoc/pkg/crunch"
)

const (
	sqlFindBranches = `
	SELECT id, tenant_id, name, from_branch_id, from_version_id, from_version_tag, created_at, created_by
	FROM %[1]s b
		%[2]s --where
		%[3]s --order by
		%[4]s --limit
	`
	sqlInsertBranches = `
	INSERT INTO %[1]s (id, tenant_id, name, from_branch_id, from_version_id, from_version_tag, created_at, created_by)
	VALUES ($1, $2, $3, $4, $5, $6, DEFAULT, $7)
	RETURNING id, created_at
	`
	sqlDeleteBranches = `DELETE FROM %[1]s WHERE tenant_id = $1 AND id = $2`
)

func getBranchTableName(tenantID string) string {
	return fmt.Sprintf("%s.branches", Schema)
}

// FindBranches retrieves branches from the database based on the provided query
func (r *Repository) FindBranches(ctx context.Context, query dto.FindBranchQuery) (dto.Branches, error) {
	var branches []dto.Branch
	if err := query.Validate(); err != nil {
		return nil, fmt.Errorf("query validation failed: %w", err)
	}
	sql := fmt.Sprintf(sqlFindBranches, getBranchTableName(""), query.Where(), query.OrderBy(), query.Limit())
	if err := r.NamedInSelect(ctx, &branches, sql, query, nil); err != nil {
		return nil, fmt.Errorf("find branches statement failed: %w", err)
	}
	return branches, nil
}

func (r *Repository) validateSetDefaultBranch(ctx context.Context, branch dto.Branch) (dto.Branch, error) {
	if branch.Name == "" {
		branch.Name = "default"
	}
	branchQuery := dto.FindBranchQuery{TenantID: &branch.TenantID, Name: &branch.Name}

	// Check if a branch with the same name already exists
	branches, err := r.FindBranches(ctx, branchQuery)
	if err != nil {
		// Return the original branch with an error instead of trying to access branches[0]
		return branch, fmt.Errorf("failed to find branches: %w", err)
	}

	if len(branches) > 0 {
		branch.ID = branches[0].ID
		branch.Name = branch.Name + "_" + strconv.Itoa(len(branches)+1)
	}

	return branch, nil
}

// CreateBranch inserts a branch into the database
func (r *Repository) CreateBranch(ctx context.Context, tx *sql.Tx, inputBranch dto.Branch) (string, error) {
	branch, err := r.validateSetDefaultBranch(ctx, inputBranch)
	if err != nil {
		return "", fmt.Errorf("failed to find branches: %w", err)
	}
	if branch.ID != "" {
		return branch.ID, nil
	}

	if tx == nil {
		tx, err = r.DB.BeginTx(ctx, nil)
		if err != nil {
			return "", fmt.Errorf("begin transaction failed: %w", err)
		}
		defer tx.Rollback()
	}

	sqlInsertStmt := fmt.Sprintf(sqlInsertBranches, getBranchTableName(""))
	stmt, err := tx.PrepareContext(ctx, sqlInsertStmt)
	if err != nil {
		return "", fmt.Errorf("failed to prepare statement: %w", err)
	}
	defer stmt.Close()

	// Generate ID using crunch package with "brh" prefix
	id, err := crunch.Generate("brh")
	if err != nil {
		return "", fmt.Errorf("failed to generate ID: %w", err)
	}
	var CreatedAt time.Time
	err = stmt.QueryRowContext(ctx,
		id,
		branch.TenantID,
		branch.Name,
		branch.FromBranchID,
		branch.FromVersionID,
		branch.FromVersionTag,
		// created_at is handled by DEFAULT in the SQL statement
		branch.CreatedBy,
	).Scan(&branch.ID, &CreatedAt)
	defer stmt.Close()

	if err != nil {
		return "", fmt.Errorf("failed to insert branch: %w", err)
	}
	inputBranch.ID = branch.ID
	inputBranch.CreatedAt = CreatedAt
	if err := tx.Commit(); err != nil {
		return "", fmt.Errorf("commit transaction failed: %w", err)
	}
	return branch.ID, nil
}

func (r *Repository) DeleteBranch(ctx context.Context, tx *sql.Tx, params dto.DeleteBranchParams) error {
	var err error
	if tx == nil {
		tx, err = r.DB.BeginTx(ctx, nil)
		if err != nil {
			return fmt.Errorf("begin transaction failed: %w", err)
		}
		defer tx.Rollback()
	}

	sqlDeleteStmt := fmt.Sprintf(sqlDeleteBranches, getBranchTableName(""))
	stmt, err := tx.PrepareContext(ctx, sqlDeleteStmt)
	if err != nil {
		return fmt.Errorf("failed to prepare statement: %w", err)
	}
	defer stmt.Close()

	_, err = stmt.ExecContext(ctx, params.TenantID, params.ID)
	if err != nil {
		return fmt.Errorf("failed to delete branch: %w", err)
	}

	if err := tx.Commit(); err != nil {
		return fmt.Errorf("commit transaction failed: %w", err)
	}
	return nil
}
