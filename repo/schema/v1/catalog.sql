-- Enable required PostgreSQL extensions
CREATE EXTENSION IF NOT EXISTS pg_trgm;

--
-- TABLE: branches
-- All tenants share the same branches table. The branch_id is unique across all tenants.
--
CREATE TABLE branches (
    id TEXT,
    tenant_id TEXT NOT NULL,
    name TEXT,
    UNIQUE (tenant_id, id),

    -- populated if branch was created from another branch
    from_branch_id TEXT,
    from_version_id TEXT,
    from_version_tag TEXT,

    -- audit properties
    created_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT now(),
    created_by text NOT NULL,
    modified_at TIMESTAMP NULL,
    modified_by text NULL,
    deleted_at TIMESTAMP NULL,
    deleted_by text NULL
);

CREATE INDEX branches_id_idx ON branches(id);
CREATE INDEX branches_tenant_id_idx ON branches(tenant_id);
CREATE INDEX branches_name_exact_idx ON branches(name);
CREATE INDEX branches_from_branch_id_idx ON branches(from_branch_id);

--
-- TABLE: versions
-- All tenants share the same versions table. The version_id is unique across all tenants.
--
CREATE TABLE versions (
    -- represents the primary key and also the version_id
    id TEXT,
    tenant_id TEXT NOT NULL,
    UNIQUE (tenant_id, id),

    context_id TEXT NOT NULL,

    branch_id TEXT,
    version_tag TEXT,

    -- start and end time within which the data model was injested and pruned
    started_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    completed_at TIMESTAMP WITH TIME ZONE,

    -- populated if the data model injest was through batch injestion
    -- it will be systemscan_id if the injest was from a scan or public api
    -- it is maintained only for references back to the source from which model was imported
    import_id TEXT,

    -- audit properties
    created_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT now(),
    created_by text NOT NULL,
    deleted_at TIMESTAMP NULL,
    deleted_by text NULL
);

CREATE INDEX versions_id_idx ON versions(id);
CREATE INDEX versions_tenant_id_idx ON versions(tenant_id);
CREATE INDEX versions_context_id_idx ON versions(context_id);
CREATE INDEX versions_branch_id_idx ON versions(branch_id);

--
-- TABLE: entities
--
-- Note that if columns are added or removed from these tables, they need to be
-- updated in the versioning comparisons temporal_tables.sql (which check for
-- the equality of each column by name).
--

CREATE TABLE entities (
    id TEXT NOT NULL,
    tenant_id TEXT NOT NULL,
    meta_version TEXT NOT NULL,
    context_id TEXT NOT NULL,

    -- the lowercased name of entity. used for cross reference
    model_name TEXT NOT NULL,

    -- properties
    name TEXT,
    entity_type TEXT,
    description TEXT,
    active BOOLEAN,
    ordinal_position INT,
    tags TEXT[],

    -- noramlized usage of entity within within our system
    -- e.g. all table, view, virtual view, rule entity types are all seen as dataset
    consumption_type TEXT,

    -- inheritance, cross referenced entity, mapped entity
    base_type TEXT,
    ref TEXT,
    mapped_entity TEXT,

    -- type specific properties
    properties JSONB,

    -- custom properties
    custom_properties JSONB,

    -- audit properties
    created_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT now(),
    created_by text NOT NULL,
    modified_at TIMESTAMP NULL,
    modified_by text NULL,
    deleted_at TIMESTAMP NULL,
    deleted_by text NULL,

    -- versiong and branching information
    version_id TEXT,
    branch_id TEXT NOT NULL,

    -- validity range for temporal table
    sys_period tstzrange NOT NULL DEFAULT tstzrange(current_timestamp, null),

    UNIQUE (tenant_id, id, branch_id)
) PARTITION BY LIST(tenant_id);

CREATE INDEX entity_id_idx ON entities(id);
CREATE INDEX entity_type_idx ON entities(entity_type);
CREATE INDEX entity_tenant_id_idx ON entities(tenant_id);
CREATE INDEX entity_context_id_idx ON entities(context_id);
CREATE INDEX entity_model_name_idx ON entities(model_name);
CREATE INDEX entity_consumption_type_idx ON entities(consumption_type);
CREATE INDEX entity_ref_idx ON entities(ref);
CREATE INDEX entity_tags_exact_idx ON entities(tags);
CREATE INDEX entity_sys_period_idx ON entities USING GIST (sys_period);

-- composite index to sort on a unique key (for pagination)
CREATE INDEX entity_name_id_idx ON entities (name ASC, id ASC);

-- Index for exact name matches (case-sensitive)
CREATE INDEX entity_name_exact_idx ON entities(name);

-- Index for case-insensitive substring matches on name (for nameContains ILIKE)
CREATE INDEX entity_name_gin_trgm_idx ON entities USING GIN (name gin_trgm_ops);

-- Index for full-text search on description (for descriptionContains)
CREATE INDEX entity_description_fts_idx ON entities USING GIN (to_tsvector('english', description));

-- history table for entity
CREATE TABLE entities_history (LIKE entities) PARTITION BY LIST(tenant_id);

-- same indexes as on entity for the history table
CREATE INDEX history_entity_id_idx ON entities_history(id);
CREATE INDEX history_entity_type_idx ON entities_history(entity_type);
CREATE INDEX history_entity_tenant_id_idx ON entities_history(tenant_id);
CREATE INDEX history_entity_context_id_idx ON entities_history(context_id);
CREATE INDEX history_entity_model_name_idx ON entities_history(model_name);
CREATE INDEX history_entity_consumption_type_idx ON entities_history(consumption_type);
CREATE INDEX history_entity_ref_idx ON entities_history(ref);
CREATE INDEX history_entity_tags_exact_idx ON entities_history(tags);
CREATE INDEX history_entity_sys_period_idx ON entities_history USING GIST (sys_period);
CREATE INDEX history_entity_name_id_idx ON entities_history (name ASC, id ASC);

-- view combining the entity and entity history tables.
CREATE VIEW entity_and_history AS
    SELECT * FROM entities
  UNION ALL
    SELECT * FROM entities_history;

--
-- TABLE: elements
--
-- Note that if columns are added or removed from these tables, they need to be
-- updated in the versioning comparisons temporal_tables.sql (which check for
-- the equality of each column by name).
--

CREATE TABLE elements (
    id TEXT NOT NULL,
    tenant_id TEXT NOT NULL,
    meta_version TEXT NOT NULL,
    context_id TEXT NOT NULL,
    parent_id TEXT NOT NULL,

    -- the lowercased {entity_name}.{element_name} used for cross reference
    model_name TEXT NOT NULL,

    -- properties
    name TEXT,
    element_type TEXT,
    description TEXT,
    active BOOLEAN,
    ordinal_position INT,
    tags TEXT[],

    -- noramlized usage of entity within within our system
    -- e.g. all column, property element types are all seen as field
    -- e.g. primary-key, odata-key element types are all seen as key
    consumption_type TEXT,

    -- standard properties
    data_type TEXT,
    is_required BOOLEAN,
    is_array BOOLEAN,
    default_value TEXT,
    precision TEXT,
    scale INT,
    size TEXT,
    enum TEXT[],
    pattern TEXT,

    -- cross referenced model element, mapped model element
    ref TEXT,
    mapped_element TEXT,

    -- type specific properties
    properties JSONB,

    -- custom properties
    custom_properties JSONB,

    -- audit properties
    created_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT now(),
    created_by text NOT NULL,
    modified_at TIMESTAMP NULL,
    modified_by text NULL,
    deleted_at TIMESTAMP NULL,
    deleted_by text NULL,

    -- versiong and branching information
    version_id TEXT,
    branch_id TEXT NOT NULL,

    -- validity range for temporal table
    sys_period tstzrange NOT NULL DEFAULT tstzrange(current_timestamp, null),

    UNIQUE (tenant_id, id, branch_id)
) PARTITION BY LIST(tenant_id);

CREATE INDEX element_id_idx ON elements(id);
CREATE INDEX element_type_idx ON elements(element_type);
CREATE INDEX element_tenant_id_idx ON elements(tenant_id);
CREATE INDEX element_context_id_idx ON elements(context_id);
CREATE INDEX element_parent_id_idx ON elements(parent_id);
-- Index for exact name matches (case-sensitive)
CREATE INDEX element_name_exact_idx ON elements(name);
-- Index for case-insensitive substring matches on name (for nameContains ILIKE)
CREATE INDEX element_name_gin_trgm_idx ON elements USING GIN (name gin_trgm_ops);
-- Index for full-text search on description (for descriptionContains)
CREATE INDEX element_description_fts_idx ON elements USING GIN (to_tsvector('english', description));
CREATE INDEX element_model_name_idx ON elements(model_name);
CREATE INDEX element_consumption_type_idx ON elements(consumption_type);
CREATE INDEX element_ref_idx ON elements(ref);
CREATE INDEX element_tags_exact_idx ON elements(tags);
CREATE INDEX element_sys_period_idx ON elements USING GIST (sys_period);

-- composite index to sort on a unique key (for pagination)
CREATE INDEX element_name_id_idx ON elements (name ASC, id ASC);


-- history table for element
CREATE TABLE elements_history (LIKE elements) PARTITION BY LIST(tenant_id);

-- same indexes as on element for the history table
CREATE INDEX history_element_id_idx ON elements_history(id);
CREATE INDEX history_element_type_idx ON elements_history(element_type);
CREATE INDEX history_element_tenant_id_idx ON elements_history(tenant_id);
CREATE INDEX history_element_context_id_idx ON elements_history(context_id);
CREATE INDEX history_element_parent_id_idx ON elements_history(parent_id);
CREATE INDEX history_element_model_name_idx ON elements_history(model_name);
CREATE INDEX history_consumption_type_idx ON elements_history(consumption_type);
CREATE INDEX history_element_ref_idx ON elements_history(ref);
CREATE INDEX history_element_tags_exact_idx ON elements_history(tags);
CREATE INDEX history_element_sys_period_idx ON elements_history USING GIST (sys_period);
CREATE INDEX history_element_name_id_idx ON elements_history (name ASC, id ASC);

-- view combining the element and element history tables.
CREATE VIEW element_and_history AS
    SELECT * FROM elements
  UNION ALL
    SELECT * FROM elements_history;


--
-- TABLE: relationships
--
-- Note that if columns are added or removed from these tables, they need to be
-- updated in the versioning comparisons temporal_tables.sql (which check for
-- the equality of each column by name).
--

CREATE TABLE relationships (
    id TEXT NOT NULL,
    tenant_id TEXT NOT NULL,
    meta_version TEXT NOT NULL,
    context_id TEXT NOT NULL,

    -- properties
    name TEXT,
    relation_type TEXT,
    description TEXT,
    active BOOLEAN,
    ordinal_position INT,
    tags TEXT[],

    -- standard properties
    source_ref TEXT,
    target_ref TEXT,
    source_id TEXT,
    target_id TEXT,

    -- type specific properties
    properties JSONB,

    -- custom properties
    custom_properties JSONB,

    -- audit properties
    created_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT now(),
    created_by text NOT NULL,
    modified_at TIMESTAMP NULL,
    modified_by text NULL,
    deleted_at TIMESTAMP NULL,
    deleted_by text NULL,

    -- versiong and branching information
    version_id TEXT,
    branch_id TEXT NOT NULL,

    -- validity range for temporal table
    sys_period tstzrange NOT NULL DEFAULT tstzrange(current_timestamp, null),

    UNIQUE (tenant_id, id, branch_id)
) PARTITION BY LIST(tenant_id);

CREATE INDEX relationship_id_idx ON relationships(id);
CREATE INDEX relationship_type_idx ON relationships(relation_type);
CREATE INDEX relationship_tenant_id_idx ON relationships(tenant_id);
CREATE INDEX relationship_context_id_idx ON relationships(context_id);
CREATE INDEX relationship_source_id_idx ON relationships(source_id);
CREATE INDEX relationship_target_id_idx ON relationships(target_id);
CREATE INDEX relationship_tags_exact_idx ON relationships(tags);

-- composite index
CREATE INDEX relationship_sys_period_idx ON relationships USING GIST (sys_period);
-- composite index to sort on a unique key (for pagination)
CREATE INDEX relationship_name_id_idx ON relationships (name ASC, id ASC);

-- history table for relationship
CREATE TABLE relationships_history (LIKE relationships) PARTITION BY LIST(tenant_id);

-- same indexes as on relationship for the history table
CREATE INDEX history_relationship_id_idx ON relationships_history(id);
CREATE INDEX history_relationship_type_idx ON relationships_history(relation_type);
CREATE INDEX history_relationship_tenant_id_idx ON relationships_history(tenant_id);
CREATE INDEX history_relationship_context_id_idx ON relationships_history(context_id);
CREATE INDEX history_relationship_source_id_idx ON relationships_history(source_id);
CREATE INDEX history_relationship_target_id_idx ON relationships_history(target_id);
CREATE INDEX history_relationship_tags_exact_idx ON relationships_history(tags);
CREATE INDEX history_relationship_sys_period_idx ON relationships_history USING GIST (sys_period);
CREATE INDEX history_relationship_name_id_idx ON relationships_history (name ASC, id ASC);

-- view combining the relationship and relationship history tables.
CREATE VIEW relationship_and_history AS
    SELECT * FROM relationships
  UNION ALL
    SELECT * FROM relationships_history;
