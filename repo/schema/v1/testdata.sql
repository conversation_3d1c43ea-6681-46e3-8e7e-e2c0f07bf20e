--- Seed data

--- versions
INSERT INTO catalog.branches (tenant_id, id, name, from_branch_id, from_version_id, from_version_tag, created_at, created_by)
VALUES
('tnt_111', 'branch_111_a', 'default', NULL, NULL, NULL, NOW(), 'user_111_a'),
('tnt_222', 'branch_222_a', 'default', NULL, NULL, NULL, NOW(), 'user_222_a');


--- versions
INSERT INTO catalog.versions (tenant_id, id, context_id, branch_id, version_tag, started_at, completed_at, import_id, created_at, created_by)
VALUES
('tnt_111', 'version_111_a', 'ctx_111_a', 'branch_111_a', '', '2022-01-11 13:00:00.000000+00', '2022-01-11 13:10:00.000000+00', '', NOW(), 'user_111_a'),
('tnt_222', 'version_222_a', 'ctx_222_a', 'branch_222_a', '', '2022-01-12 13:00:00.000000+00', '2022-01-12 13:10:00.000000+00', '', NOW(), 'user_222_a');


-- partitions for tnt_111
CREATE TABLE catalog.entities_tnt_111 PARTITION OF catalog.entities FOR VALUES IN ('tnt_111');
CREATE TABLE catalog.elements_tnt_111 PARTITION OF catalog.elements FOR VALUES IN ('tnt_111');
CREATE TABLE catalog.relationships_tnt_111 PARTITION OF catalog.relationships FOR VALUES IN ('tnt_111');
CREATE TABLE catalog.entities_history_tnt_111 PARTITION OF catalog.entities_history FOR VALUES IN ('tnt_111');
CREATE TABLE catalog.elements_history_tnt_111 PARTITION OF catalog.elements_history FOR VALUES IN ('tnt_111');
CREATE TABLE catalog.relationships_history_tnt_111 PARTITION OF catalog.relationships_history FOR VALUES IN ('tnt_111');

-- partitions for tnt_222
CREATE TABLE catalog.entities_tnt_222 PARTITION OF catalog.entities FOR VALUES IN ('tnt_222');
CREATE TABLE catalog.elements_tnt_222 PARTITION OF catalog.elements FOR VALUES IN ('tnt_222');
CREATE TABLE catalog.relationships_tnt_222 PARTITION OF catalog.relationships FOR VALUES IN ('tnt_222');
CREATE TABLE catalog.entities_history_tnt_222 PARTITION OF catalog.entities_history FOR VALUES IN ('tnt_222');
CREATE TABLE catalog.elements_history_tnt_222 PARTITION OF catalog.elements_history FOR VALUES IN ('tnt_222');
CREATE TABLE catalog.relationships_history_tnt_222 PARTITION OF catalog.relationships_history FOR VALUES IN ('tnt_222');

--- version_111_a for tnt_111
INSERT INTO entities (tenant_id, id, meta_version, context_id, model_name, name, entity_type, consumption_type, description, active, ordinal_position, tags, base_type, ref, mapped_entity, properties, custom_properties, created_at, created_by, version_id, branch_id)
VALUES
('tnt_111', 'ent_111_a', 'meta_1', 'ctx_111_a', 'ns111.dbo.table1', 'table1', 'TABLE', 'dataset', 'desc', TRUE, 1, ARRAY['tag1'], 'base_1', 'ref_1', 'mapped_1', '{"key": "value"}', '{"custom_key": "custom_value"}', NOW(), 'user_111_a', 'version_111_a', 'branch_111_a'),
('tnt_111', 'ent_111_b', 'meta_1', 'ctx_111_a', 'ns111.dbo.table2', 'table2', 'TABLE', 'dataset', 'desc', TRUE, 2, ARRAY['tag1'], 'base_1', 'ref_1', 'mapped_1', '{"key": "value"}', '{"custom_key": "custom_value"}', NOW(), 'user_111_a', 'version_111_a', 'branch_111_a');

INSERT INTO elements (tenant_id, id, meta_version, context_id, parent_id, model_name, name, element_type, consumption_type, description, active, ordinal_position, tags, data_type, ref, mapped_element, created_at, created_by, version_id, branch_id)
VALUES
('tnt_111', 'ele_111_a', 'meta_1', 'ctx_111_a', 'ent_111_a', 'ns111.dbo.table1.column1', 'column1', 'COLUMN', 'field', 'desc', TRUE, 1, ARRAY['tag1'], 'string', 'ref_1', 'mapped_1', NOW(), 'user_111_a', 'version_111_a', 'branch_111_a'),
('tnt_111', 'ele_111_b', 'meta_1', 'ctx_111_a', 'ent_111_a', 'ns111.dbo.table1.pk.column1', 'pk_column1', 'PRIMARY-KEY', 'key', 'desc', TRUE, 2, ARRAY['tag1'], NULL, NULL, NULL, NOW(), 'user_111_a', 'version_111_a', 'branch_111_a'),
('tnt_111', 'ele_111_c', 'meta_1', 'ctx_111_a', 'ent_111_b', 'ns111.dbo.table2.column1', 'column1', 'COLUMN', 'field', 'desc', TRUE, 1, ARRAY['tag1'], 'string', 'ref_1', 'mapped_1', NOW(), 'user_111_a', 'version_111_a', 'branch_111_a');

INSERT INTO relationships (tenant_id, id, meta_version, context_id, name, relation_type, description, active, ordinal_position, tags, source_ref, target_ref, source_id, target_id, created_at, created_by, version_id, branch_id)
VALUES
('tnt_111', 'rel_111_a', 'meta_1', 'ctx_111_a', 'pk_column1_column1', 'PK_REF_ELEMENT', 'desc', TRUE, 1, ARRAY['tag1'], 'ns111.dbo.table1.pk.column1', 'ns111.dbo.table1.column1', 'ele_111_b', 'ele_111_a', NOW(), 'user_111_a', 'version_111_a', 'branch_111_a');


UPDATE catalog.entities SET sys_period = '["2022-01-11 13:01:23.586116+00",)' WHERE version_id = 'version_111_a';
UPDATE catalog.elements SET sys_period = '["2022-01-11 13:01:23.586116+00",)' WHERE version_id = 'version_111_a';
UPDATE catalog.relationships SET sys_period = '["2022-01-11 13:01:23.586116+00",)' WHERE version_id = 'version_111_a';

--- version_222_a for tnt_222
INSERT INTO entities (tenant_id, id, meta_version, context_id, model_name, name, entity_type, consumption_type, description, active, ordinal_position, tags, base_type, ref, mapped_entity, properties, custom_properties, created_at, created_by, version_id, branch_id)
VALUES
('tnt_222', 'ent_222_a', 'meta_1', 'ctx_222_a', 'ns222.dbo.table1', 'table1', 'TABLE', 'dataset', 'desc', TRUE, 1, ARRAY['tag1'], 'base_1', 'ref_1', 'mapped_1', '{"key": "value"}', '{"custom_key": "custom_value"}', NOW(), 'user_222_a', 'version_222_a', 'branch_222_a'),
('tnt_222', 'ent_222_b', 'meta_1', 'ctx_222_a', 'ns222.dbo.table2', 'table2', 'TABLE', 'dataset', 'desc', TRUE, 2, ARRAY['tag1'], 'base_1', 'ref_1', 'mapped_1', '{"key": "value"}', '{"custom_key": "custom_value"}', NOW(), 'user_222_a', 'version_222_a', 'branch_222_a');

INSERT INTO elements (tenant_id, id, meta_version, context_id, parent_id, model_name, name, element_type, consumption_type, description, active, ordinal_position, tags, data_type, ref, mapped_element, created_at, created_by, version_id, branch_id)
VALUES
('tnt_222', 'ele_222_a', 'meta_1', 'ctx_222_a', 'ent_222_a', 'ns222.dbo.table1.column1', 'column1', 'COLUMN', 'field', 'desc', TRUE, 1, ARRAY['tag1'], 'string', 'ref_1', 'mapped_1', NOW(), 'user_222_a', 'version_222_a', 'branch_222_a'),
('tnt_222', 'ele_222_b', 'meta_1', 'ctx_222_a', 'ent_222_a', 'ns222.dbo.table1.column2', 'column2', 'COLUMN', 'field', 'desc', TRUE, 1, ARRAY['tag1'], 'string', 'ref_1', 'mapped_1', NOW(), 'user_222_a', 'version_222_a', 'branch_222_a'),
('tnt_222', 'ele_222_c', 'meta_1', 'ctx_222_a', 'ent_222_a', 'ns222.dbo.table1.idx.column1.column2', 'IDX_column1_column2', 'ordinal_position', 'ordinal_position', 'desc', TRUE, 2, ARRAY['tag1'], NULL, NULL, NULL, NOW(), 'user_222_a', 'version_222_a', 'branch_222_a'),
('tnt_222', 'ele_222_d', 'meta_1', 'ctx_222_a', 'ent_222_b', 'ns222.dbo.table2.column1', 'column1', 'COLUMN', 'field', 'desc', TRUE, 1, ARRAY['tag1'], 'string', 'ref_1', 'mapped_1', NOW(), 'user_222_a', 'version_222_a', 'branch_222_a');

INSERT INTO relationships (tenant_id, id, meta_version, context_id, name, relation_type, description, active, ordinal_position, tags, source_ref, target_ref, source_id, target_id, created_at, created_by, version_id, branch_id)
VALUES
('tnt_222', 'rel_222_a', 'meta_1', 'ctx_222_a', 'idx_dbo_table1_column1_column2_column_1', 'IDX_REF_ELEMENT', 'desc', TRUE, 1, ARRAY['tag1'], 'ns222.dbo.table1.idx.column1.column2', 'ns111.dbo.table1.column1', 'ele_222_c', 'ele_222_a', NOW(), 'user_222_a', 'version_222_a', 'branch_222_a'),
('tnt_222', 'rel_222_b', 'meta_1', 'ctx_222_a', 'idx_dbo_table1_column1_column2_column_2', 'IDX_REF_ELEMENT', 'desc', TRUE, 1, ARRAY['tag1'], 'ns222.dbo.table1.idx.column1.column2', 'ns111.dbo.table1.column2', 'ele_222_c', 'ele_222_b', NOW(), 'user_222_a', 'version_222_a', 'branch_222_a');


UPDATE catalog.entities SET sys_period = '["2022-01-12 13:02:23.586116+00",)' WHERE version_id = 'version_222_a';
UPDATE catalog.elements SET sys_period = '["2022-01-12 13:02:23.586116+00",)' WHERE version_id = 'version_222_a';
UPDATE catalog.relationships SET sys_period = '["2022-01-12 13:02:23.586116+00",)' WHERE version_id = 'version_222_a';
