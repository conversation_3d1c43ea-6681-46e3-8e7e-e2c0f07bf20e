// Code generated by moq; DO NOT EDIT.
// github.com/matryer/moq

package fixtures

import (
	"context"
	"database/sql"
	"github.com/BackOfficeAssoc/catalog/models"
	"github.com/BackOfficeAssoc/catalog/repo"
	dto "github.com/BackOfficeAssoc/catalog/repo/dto"
	"sync"
)

// Ensure, that RepoMock does implement repo.Repo.
// If this is not the case, regenerate this file with moq.
var _ repo.Repo = &RepoMock{}

// RepoMock is a mock implementation of repo.Repo.
//
//	func TestSomethingThatUsesRepo(t *testing.T) {
//
//		// make and configure a mocked repo.Repo
//		mockedRepo := &RepoMock{
//			CreateBranchFunc: func(ctx context.Context, tx *sql.Tx, branch dto.Branch) (string, error) {
//				panic("mock out the CreateBranch method")
//			},
//			CreateEntitiesFunc: func(ctx context.Context, tx *sql.Tx, entities dto.Entities) error {
//				panic("mock out the CreateEntities method")
//			},
//			CreateEntityElementsFunc: func(ctx context.Context, tx *sql.Tx, parentID string, entityElements dto.EntityElements) error {
//				panic("mock out the CreateEntityElements method")
//			},
//			CreateRelationshipsFunc: func(ctx context.Context, tx *sql.Tx, relationships dto.Relationships) error {
//				panic("mock out the CreateRelationships method")
//			},
//			CreateSampleFunc: func(request dto.CreateSampleInput, createdBy string, tenantID string) (string, error) {
//				panic("mock out the CreateSample method")
//			},
//			CreateVersionFunc: func(ctx context.Context, tx *sql.Tx, version dto.Version) (string, error) {
//				panic("mock out the CreateVersion method")
//			},
//			DeleteEntityElementsFunc: func(ctx context.Context, tx *sql.Tx, params dto.DeleteElementParams) (*dto.ExecutionElementDeleteResult, error) {
//				panic("mock out the DeleteEntityElements method")
//			},
//			DeleteRelationshipsFunc: func(ctx context.Context, tx *sql.Tx, params dto.DeleteRelationshipParams) (*dto.ExecutionRelationshipDeleteResult, error) {
//				panic("mock out the DeleteRelationships method")
//			},
//			DeleteSampleFunc: func(id string, tenantID *string) error {
//				panic("mock out the DeleteSample method")
//			},
//			FetchSampleFunc: func(id string, tenantID *string) (dto.Sample, error) {
//				panic("mock out the FetchSample method")
//			},
//			FindEntityElementsFunc: func(ctx context.Context, query dto.FindEntityElementsQuery) (dto.EntityElements, error) {
//				panic("mock out the FindEntityElements method")
//			},
//			FindRelationshipsFunc: func(ctx context.Context, query dto.FindRelationshipQuery) (dto.Relationships, error) {
//				panic("mock out the FindRelationships method")
//			},
//			FindSamplesFunc: func(query dto.SampleQuery) ([]dto.Sample, error) {
//				panic("mock out the FindSamples method")
//			},
//			GenerateBranchAndVersionIDFunc: func(ctx context.Context, tenant_id string, context_id string, user_id string, tx *sql.Tx) (string, string) {
//				panic("mock out the GenerateBranchAndVersionID method")
//			},
//			NavigateDataModelFunc: func(ctx context.Context, tenantID string, query models.NavigateDataModelQuery) (dto.Entities, dto.EntityElements, dto.Relationships, string, error) {
//				panic("mock out the NavigateDataModel method")
//			},
//			UpdateSampleFunc: func(id string, request dto.UpdateSampleInput, tenantID *string) error {
//				panic("mock out the UpdateSample method")
//			},
//		}
//
//		// use mockedRepo in code that requires repo.Repo
//		// and then make assertions.
//
//	}
type RepoMock struct {
	// CreateBranchFunc mocks the CreateBranch method.
	CreateBranchFunc func(ctx context.Context, tx *sql.Tx, branch dto.Branch) (string, error)

	// CreateEntitiesFunc mocks the CreateEntities method.
	CreateEntitiesFunc func(ctx context.Context, tx *sql.Tx, entities dto.Entities) error

	// CreateEntityElementsFunc mocks the CreateEntityElements method.
	CreateEntityElementsFunc func(ctx context.Context, tx *sql.Tx, parentID string, entityElements dto.EntityElements) error

	// CreateRelationshipsFunc mocks the CreateRelationships method.
	CreateRelationshipsFunc func(ctx context.Context, tx *sql.Tx, relationships dto.Relationships) error

	// CreateSampleFunc mocks the CreateSample method.
	CreateSampleFunc func(request dto.CreateSampleInput, createdBy string, tenantID string) (string, error)

	// CreateVersionFunc mocks the CreateVersion method.
	CreateVersionFunc func(ctx context.Context, tx *sql.Tx, version dto.Version) (string, error)

	// DeleteEntityElementsFunc mocks the DeleteEntityElements method.
	DeleteEntityElementsFunc func(ctx context.Context, tx *sql.Tx, params dto.DeleteElementParams) (*dto.ExecutionElementDeleteResult, error)

	// DeleteRelationshipsFunc mocks the DeleteRelationships method.
	DeleteRelationshipsFunc func(ctx context.Context, tx *sql.Tx, params dto.DeleteRelationshipParams) (*dto.ExecutionRelationshipDeleteResult, error)

	// DeleteSampleFunc mocks the DeleteSample method.
	DeleteSampleFunc func(id string, tenantID *string) error

	// FetchSampleFunc mocks the FetchSample method.
	FetchSampleFunc func(id string, tenantID *string) (dto.Sample, error)

	// FindEntityElementsFunc mocks the FindEntityElements method.
	FindEntityElementsFunc func(ctx context.Context, query dto.FindEntityElementsQuery) (dto.EntityElements, error)

	// FindRelationshipsFunc mocks the FindRelationships method.
	FindRelationshipsFunc func(ctx context.Context, query dto.FindRelationshipQuery) (dto.Relationships, error)

	// FindSamplesFunc mocks the FindSamples method.
	FindSamplesFunc func(query dto.SampleQuery) ([]dto.Sample, error)

	// GenerateBranchAndVersionIDFunc mocks the GenerateBranchAndVersionID method.
	GenerateBranchAndVersionIDFunc func(ctx context.Context, tenant_id string, context_id string, user_id string, tx *sql.Tx) (string, string)

	// NavigateDataModelFunc mocks the NavigateDataModel method.
	NavigateDataModelFunc func(ctx context.Context, tenantID string, query models.NavigateDataModelQuery) (dto.Entities, dto.EntityElements, dto.Relationships, string, error)

	// UpdateSampleFunc mocks the UpdateSample method.
	UpdateSampleFunc func(id string, request dto.UpdateSampleInput, tenantID *string) error

	// calls tracks calls to the methods.
	calls struct {
		// CreateBranch holds details about calls to the CreateBranch method.
		CreateBranch []struct {
			// Ctx is the ctx argument value.
			Ctx context.Context
			// Tx is the tx argument value.
			Tx *sql.Tx
			// Branch is the branch argument value.
			Branch dto.Branch
		}
		// CreateEntities holds details about calls to the CreateEntities method.
		CreateEntities []struct {
			// Ctx is the ctx argument value.
			Ctx context.Context
			// Tx is the tx argument value.
			Tx *sql.Tx
			// Entities is the entities argument value.
			Entities dto.Entities
		}
		// CreateEntityElements holds details about calls to the CreateEntityElements method.
		CreateEntityElements []struct {
			// Ctx is the ctx argument value.
			Ctx context.Context
			// Tx is the tx argument value.
			Tx *sql.Tx
			// ParentID is the parentID argument value.
			ParentID string
			// EntityElements is the entityElements argument value.
			EntityElements dto.EntityElements
		}
		// CreateRelationships holds details about calls to the CreateRelationships method.
		CreateRelationships []struct {
			// Ctx is the ctx argument value.
			Ctx context.Context
			// Tx is the tx argument value.
			Tx *sql.Tx
			// Relationships is the relationships argument value.
			Relationships dto.Relationships
		}
		// CreateSample holds details about calls to the CreateSample method.
		CreateSample []struct {
			// Request is the request argument value.
			Request dto.CreateSampleInput
			// CreatedBy is the createdBy argument value.
			CreatedBy string
			// TenantID is the tenantID argument value.
			TenantID string
		}
		// CreateVersion holds details about calls to the CreateVersion method.
		CreateVersion []struct {
			// Ctx is the ctx argument value.
			Ctx context.Context
			// Tx is the tx argument value.
			Tx *sql.Tx
			// Version is the version argument value.
			Version dto.Version
		}
		// DeleteEntityElements holds details about calls to the DeleteEntityElements method.
		DeleteEntityElements []struct {
			// Ctx is the ctx argument value.
			Ctx context.Context
			// Tx is the tx argument value.
			Tx *sql.Tx
			// Params is the params argument value.
			Params dto.DeleteElementParams
		}
		// DeleteRelationships holds details about calls to the DeleteRelationships method.
		DeleteRelationships []struct {
			// Ctx is the ctx argument value.
			Ctx context.Context
			// Tx is the tx argument value.
			Tx *sql.Tx
			// Params is the params argument value.
			Params dto.DeleteRelationshipParams
		}
		// DeleteSample holds details about calls to the DeleteSample method.
		DeleteSample []struct {
			// ID is the id argument value.
			ID string
			// TenantID is the tenantID argument value.
			TenantID *string
		}
		// FetchSample holds details about calls to the FetchSample method.
		FetchSample []struct {
			// ID is the id argument value.
			ID string
			// TenantID is the tenantID argument value.
			TenantID *string
		}
		// FindEntityElements holds details about calls to the FindEntityElements method.
		FindEntityElements []struct {
			// Ctx is the ctx argument value.
			Ctx context.Context
			// Query is the query argument value.
			Query dto.FindEntityElementsQuery
		}
		// FindRelationships holds details about calls to the FindRelationships method.
		FindRelationships []struct {
			// Ctx is the ctx argument value.
			Ctx context.Context
			// Query is the query argument value.
			Query dto.FindRelationshipQuery
		}
		// FindSamples holds details about calls to the FindSamples method.
		FindSamples []struct {
			// Query is the query argument value.
			Query dto.SampleQuery
		}
		// GenerateBranchAndVersionID holds details about calls to the GenerateBranchAndVersionID method.
		GenerateBranchAndVersionID []struct {
			// Ctx is the ctx argument value.
			Ctx context.Context
			// Tenant_id is the tenant_id argument value.
			Tenant_id string
			// Context_id is the context_id argument value.
			Context_id string
			// User_id is the user_id argument value.
			User_id string
			// Tx is the tx argument value.
			Tx *sql.Tx
		}
		// NavigateDataModel holds details about calls to the NavigateDataModel method.
		NavigateDataModel []struct {
			// Ctx is the ctx argument value.
			Ctx context.Context
			// TenantID is the tenantID argument value.
			TenantID string
			// Query is the query argument value.
			Query models.NavigateDataModelQuery
		}
		// UpdateSample holds details about calls to the UpdateSample method.
		UpdateSample []struct {
			// ID is the id argument value.
			ID string
			// Request is the request argument value.
			Request dto.UpdateSampleInput
			// TenantID is the tenantID argument value.
			TenantID *string
		}
	}
	lockCreateBranch               sync.RWMutex
	lockCreateEntities             sync.RWMutex
	lockCreateEntityElements       sync.RWMutex
	lockCreateRelationships        sync.RWMutex
	lockCreateSample               sync.RWMutex
	lockCreateVersion              sync.RWMutex
	lockDeleteEntityElements       sync.RWMutex
	lockDeleteRelationships        sync.RWMutex
	lockDeleteSample               sync.RWMutex
	lockFetchSample                sync.RWMutex
	lockFindEntityElements         sync.RWMutex
	lockFindRelationships          sync.RWMutex
	lockFindSamples                sync.RWMutex
	lockGenerateBranchAndVersionID sync.RWMutex
	lockNavigateDataModel          sync.RWMutex
	lockUpdateSample               sync.RWMutex
}

// CreateBranch calls CreateBranchFunc.
func (mock *RepoMock) CreateBranch(ctx context.Context, tx *sql.Tx, branch dto.Branch) (string, error) {
	if mock.CreateBranchFunc == nil {
		panic("RepoMock.CreateBranchFunc: method is nil but Repo.CreateBranch was just called")
	}
	callInfo := struct {
		Ctx    context.Context
		Tx     *sql.Tx
		Branch dto.Branch
	}{
		Ctx:    ctx,
		Tx:     tx,
		Branch: branch,
	}
	mock.lockCreateBranch.Lock()
	mock.calls.CreateBranch = append(mock.calls.CreateBranch, callInfo)
	mock.lockCreateBranch.Unlock()
	return mock.CreateBranchFunc(ctx, tx, branch)
}

// CreateBranchCalls gets all the calls that were made to CreateBranch.
// Check the length with:
//
//	len(mockedRepo.CreateBranchCalls())
func (mock *RepoMock) CreateBranchCalls() []struct {
	Ctx    context.Context
	Tx     *sql.Tx
	Branch dto.Branch
} {
	var calls []struct {
		Ctx    context.Context
		Tx     *sql.Tx
		Branch dto.Branch
	}
	mock.lockCreateBranch.RLock()
	calls = mock.calls.CreateBranch
	mock.lockCreateBranch.RUnlock()
	return calls
}

// CreateEntities calls CreateEntitiesFunc.
func (mock *RepoMock) CreateEntities(ctx context.Context, tx *sql.Tx, entities dto.Entities) error {
	if mock.CreateEntitiesFunc == nil {
		panic("RepoMock.CreateEntitiesFunc: method is nil but Repo.CreateEntities was just called")
	}
	callInfo := struct {
		Ctx      context.Context
		Tx       *sql.Tx
		Entities dto.Entities
	}{
		Ctx:      ctx,
		Tx:       tx,
		Entities: entities,
	}
	mock.lockCreateEntities.Lock()
	mock.calls.CreateEntities = append(mock.calls.CreateEntities, callInfo)
	mock.lockCreateEntities.Unlock()
	return mock.CreateEntitiesFunc(ctx, tx, entities)
}

// CreateEntitiesCalls gets all the calls that were made to CreateEntities.
// Check the length with:
//
//	len(mockedRepo.CreateEntitiesCalls())
func (mock *RepoMock) CreateEntitiesCalls() []struct {
	Ctx      context.Context
	Tx       *sql.Tx
	Entities dto.Entities
} {
	var calls []struct {
		Ctx      context.Context
		Tx       *sql.Tx
		Entities dto.Entities
	}
	mock.lockCreateEntities.RLock()
	calls = mock.calls.CreateEntities
	mock.lockCreateEntities.RUnlock()
	return calls
}

// CreateEntityElements calls CreateEntityElementsFunc.
func (mock *RepoMock) CreateEntityElements(ctx context.Context, tx *sql.Tx, parentID string, entityElements dto.EntityElements) error {
	if mock.CreateEntityElementsFunc == nil {
		panic("RepoMock.CreateEntityElementsFunc: method is nil but Repo.CreateEntityElements was just called")
	}
	callInfo := struct {
		Ctx            context.Context
		Tx             *sql.Tx
		ParentID       string
		EntityElements dto.EntityElements
	}{
		Ctx:            ctx,
		Tx:             tx,
		ParentID:       parentID,
		EntityElements: entityElements,
	}
	mock.lockCreateEntityElements.Lock()
	mock.calls.CreateEntityElements = append(mock.calls.CreateEntityElements, callInfo)
	mock.lockCreateEntityElements.Unlock()
	return mock.CreateEntityElementsFunc(ctx, tx, parentID, entityElements)
}

// CreateEntityElementsCalls gets all the calls that were made to CreateEntityElements.
// Check the length with:
//
//	len(mockedRepo.CreateEntityElementsCalls())
func (mock *RepoMock) CreateEntityElementsCalls() []struct {
	Ctx            context.Context
	Tx             *sql.Tx
	ParentID       string
	EntityElements dto.EntityElements
} {
	var calls []struct {
		Ctx            context.Context
		Tx             *sql.Tx
		ParentID       string
		EntityElements dto.EntityElements
	}
	mock.lockCreateEntityElements.RLock()
	calls = mock.calls.CreateEntityElements
	mock.lockCreateEntityElements.RUnlock()
	return calls
}

// CreateRelationships calls CreateRelationshipsFunc.
func (mock *RepoMock) CreateRelationships(ctx context.Context, tx *sql.Tx, relationships dto.Relationships) error {
	if mock.CreateRelationshipsFunc == nil {
		panic("RepoMock.CreateRelationshipsFunc: method is nil but Repo.CreateRelationships was just called")
	}
	callInfo := struct {
		Ctx           context.Context
		Tx            *sql.Tx
		Relationships dto.Relationships
	}{
		Ctx:           ctx,
		Tx:            tx,
		Relationships: relationships,
	}
	mock.lockCreateRelationships.Lock()
	mock.calls.CreateRelationships = append(mock.calls.CreateRelationships, callInfo)
	mock.lockCreateRelationships.Unlock()
	return mock.CreateRelationshipsFunc(ctx, tx, relationships)
}

// CreateRelationshipsCalls gets all the calls that were made to CreateRelationships.
// Check the length with:
//
//	len(mockedRepo.CreateRelationshipsCalls())
func (mock *RepoMock) CreateRelationshipsCalls() []struct {
	Ctx           context.Context
	Tx            *sql.Tx
	Relationships dto.Relationships
} {
	var calls []struct {
		Ctx           context.Context
		Tx            *sql.Tx
		Relationships dto.Relationships
	}
	mock.lockCreateRelationships.RLock()
	calls = mock.calls.CreateRelationships
	mock.lockCreateRelationships.RUnlock()
	return calls
}

// CreateSample calls CreateSampleFunc.
func (mock *RepoMock) CreateSample(request dto.CreateSampleInput, createdBy string, tenantID string) (string, error) {
	if mock.CreateSampleFunc == nil {
		panic("RepoMock.CreateSampleFunc: method is nil but Repo.CreateSample was just called")
	}
	callInfo := struct {
		Request   dto.CreateSampleInput
		CreatedBy string
		TenantID  string
	}{
		Request:   request,
		CreatedBy: createdBy,
		TenantID:  tenantID,
	}
	mock.lockCreateSample.Lock()
	mock.calls.CreateSample = append(mock.calls.CreateSample, callInfo)
	mock.lockCreateSample.Unlock()
	return mock.CreateSampleFunc(request, createdBy, tenantID)
}

// CreateSampleCalls gets all the calls that were made to CreateSample.
// Check the length with:
//
//	len(mockedRepo.CreateSampleCalls())
func (mock *RepoMock) CreateSampleCalls() []struct {
	Request   dto.CreateSampleInput
	CreatedBy string
	TenantID  string
} {
	var calls []struct {
		Request   dto.CreateSampleInput
		CreatedBy string
		TenantID  string
	}
	mock.lockCreateSample.RLock()
	calls = mock.calls.CreateSample
	mock.lockCreateSample.RUnlock()
	return calls
}

// CreateVersion calls CreateVersionFunc.
func (mock *RepoMock) CreateVersion(ctx context.Context, tx *sql.Tx, version dto.Version) (string, error) {
	if mock.CreateVersionFunc == nil {
		panic("RepoMock.CreateVersionFunc: method is nil but Repo.CreateVersion was just called")
	}
	callInfo := struct {
		Ctx     context.Context
		Tx      *sql.Tx
		Version dto.Version
	}{
		Ctx:     ctx,
		Tx:      tx,
		Version: version,
	}
	mock.lockCreateVersion.Lock()
	mock.calls.CreateVersion = append(mock.calls.CreateVersion, callInfo)
	mock.lockCreateVersion.Unlock()
	return mock.CreateVersionFunc(ctx, tx, version)
}

// CreateVersionCalls gets all the calls that were made to CreateVersion.
// Check the length with:
//
//	len(mockedRepo.CreateVersionCalls())
func (mock *RepoMock) CreateVersionCalls() []struct {
	Ctx     context.Context
	Tx      *sql.Tx
	Version dto.Version
} {
	var calls []struct {
		Ctx     context.Context
		Tx      *sql.Tx
		Version dto.Version
	}
	mock.lockCreateVersion.RLock()
	calls = mock.calls.CreateVersion
	mock.lockCreateVersion.RUnlock()
	return calls
}

// DeleteEntityElements calls DeleteEntityElementsFunc.
func (mock *RepoMock) DeleteEntityElements(ctx context.Context, tx *sql.Tx, params dto.DeleteElementParams) (*dto.ExecutionElementDeleteResult, error) {
	if mock.DeleteEntityElementsFunc == nil {
		panic("RepoMock.DeleteEntityElementsFunc: method is nil but Repo.DeleteEntityElements was just called")
	}
	callInfo := struct {
		Ctx    context.Context
		Tx     *sql.Tx
		Params dto.DeleteElementParams
	}{
		Ctx:    ctx,
		Tx:     tx,
		Params: params,
	}
	mock.lockDeleteEntityElements.Lock()
	mock.calls.DeleteEntityElements = append(mock.calls.DeleteEntityElements, callInfo)
	mock.lockDeleteEntityElements.Unlock()
	return mock.DeleteEntityElementsFunc(ctx, tx, params)
}

// DeleteEntityElementsCalls gets all the calls that were made to DeleteEntityElements.
// Check the length with:
//
//	len(mockedRepo.DeleteEntityElementsCalls())
func (mock *RepoMock) DeleteEntityElementsCalls() []struct {
	Ctx    context.Context
	Tx     *sql.Tx
	Params dto.DeleteElementParams
} {
	var calls []struct {
		Ctx    context.Context
		Tx     *sql.Tx
		Params dto.DeleteElementParams
	}
	mock.lockDeleteEntityElements.RLock()
	calls = mock.calls.DeleteEntityElements
	mock.lockDeleteEntityElements.RUnlock()
	return calls
}

// DeleteRelationships calls DeleteRelationshipsFunc.
func (mock *RepoMock) DeleteRelationships(ctx context.Context, tx *sql.Tx, params dto.DeleteRelationshipParams) (*dto.ExecutionRelationshipDeleteResult, error) {
	if mock.DeleteRelationshipsFunc == nil {
		panic("RepoMock.DeleteRelationshipsFunc: method is nil but Repo.DeleteRelationships was just called")
	}
	callInfo := struct {
		Ctx    context.Context
		Tx     *sql.Tx
		Params dto.DeleteRelationshipParams
	}{
		Ctx:    ctx,
		Tx:     tx,
		Params: params,
	}
	mock.lockDeleteRelationships.Lock()
	mock.calls.DeleteRelationships = append(mock.calls.DeleteRelationships, callInfo)
	mock.lockDeleteRelationships.Unlock()
	return mock.DeleteRelationshipsFunc(ctx, tx, params)
}

// DeleteRelationshipsCalls gets all the calls that were made to DeleteRelationships.
// Check the length with:
//
//	len(mockedRepo.DeleteRelationshipsCalls())
func (mock *RepoMock) DeleteRelationshipsCalls() []struct {
	Ctx    context.Context
	Tx     *sql.Tx
	Params dto.DeleteRelationshipParams
} {
	var calls []struct {
		Ctx    context.Context
		Tx     *sql.Tx
		Params dto.DeleteRelationshipParams
	}
	mock.lockDeleteRelationships.RLock()
	calls = mock.calls.DeleteRelationships
	mock.lockDeleteRelationships.RUnlock()
	return calls
}

// DeleteSample calls DeleteSampleFunc.
func (mock *RepoMock) DeleteSample(id string, tenantID *string) error {
	if mock.DeleteSampleFunc == nil {
		panic("RepoMock.DeleteSampleFunc: method is nil but Repo.DeleteSample was just called")
	}
	callInfo := struct {
		ID       string
		TenantID *string
	}{
		ID:       id,
		TenantID: tenantID,
	}
	mock.lockDeleteSample.Lock()
	mock.calls.DeleteSample = append(mock.calls.DeleteSample, callInfo)
	mock.lockDeleteSample.Unlock()
	return mock.DeleteSampleFunc(id, tenantID)
}

// DeleteSampleCalls gets all the calls that were made to DeleteSample.
// Check the length with:
//
//	len(mockedRepo.DeleteSampleCalls())
func (mock *RepoMock) DeleteSampleCalls() []struct {
	ID       string
	TenantID *string
} {
	var calls []struct {
		ID       string
		TenantID *string
	}
	mock.lockDeleteSample.RLock()
	calls = mock.calls.DeleteSample
	mock.lockDeleteSample.RUnlock()
	return calls
}

// FetchSample calls FetchSampleFunc.
func (mock *RepoMock) FetchSample(id string, tenantID *string) (dto.Sample, error) {
	if mock.FetchSampleFunc == nil {
		panic("RepoMock.FetchSampleFunc: method is nil but Repo.FetchSample was just called")
	}
	callInfo := struct {
		ID       string
		TenantID *string
	}{
		ID:       id,
		TenantID: tenantID,
	}
	mock.lockFetchSample.Lock()
	mock.calls.FetchSample = append(mock.calls.FetchSample, callInfo)
	mock.lockFetchSample.Unlock()
	return mock.FetchSampleFunc(id, tenantID)
}

// FetchSampleCalls gets all the calls that were made to FetchSample.
// Check the length with:
//
//	len(mockedRepo.FetchSampleCalls())
func (mock *RepoMock) FetchSampleCalls() []struct {
	ID       string
	TenantID *string
} {
	var calls []struct {
		ID       string
		TenantID *string
	}
	mock.lockFetchSample.RLock()
	calls = mock.calls.FetchSample
	mock.lockFetchSample.RUnlock()
	return calls
}

// FindEntityElements calls FindEntityElementsFunc.
func (mock *RepoMock) FindEntityElements(ctx context.Context, query dto.FindEntityElementsQuery) (dto.EntityElements, error) {
	if mock.FindEntityElementsFunc == nil {
		panic("RepoMock.FindEntityElementsFunc: method is nil but Repo.FindEntityElements was just called")
	}
	callInfo := struct {
		Ctx   context.Context
		Query dto.FindEntityElementsQuery
	}{
		Ctx:   ctx,
		Query: query,
	}
	mock.lockFindEntityElements.Lock()
	mock.calls.FindEntityElements = append(mock.calls.FindEntityElements, callInfo)
	mock.lockFindEntityElements.Unlock()
	return mock.FindEntityElementsFunc(ctx, query)
}

// FindEntityElementsCalls gets all the calls that were made to FindEntityElements.
// Check the length with:
//
//	len(mockedRepo.FindEntityElementsCalls())
func (mock *RepoMock) FindEntityElementsCalls() []struct {
	Ctx   context.Context
	Query dto.FindEntityElementsQuery
} {
	var calls []struct {
		Ctx   context.Context
		Query dto.FindEntityElementsQuery
	}
	mock.lockFindEntityElements.RLock()
	calls = mock.calls.FindEntityElements
	mock.lockFindEntityElements.RUnlock()
	return calls
}

// FindRelationships calls FindRelationshipsFunc.
func (mock *RepoMock) FindRelationships(ctx context.Context, query dto.FindRelationshipQuery) (dto.Relationships, error) {
	if mock.FindRelationshipsFunc == nil {
		panic("RepoMock.FindRelationshipsFunc: method is nil but Repo.FindRelationships was just called")
	}
	callInfo := struct {
		Ctx   context.Context
		Query dto.FindRelationshipQuery
	}{
		Ctx:   ctx,
		Query: query,
	}
	mock.lockFindRelationships.Lock()
	mock.calls.FindRelationships = append(mock.calls.FindRelationships, callInfo)
	mock.lockFindRelationships.Unlock()
	return mock.FindRelationshipsFunc(ctx, query)
}

// FindRelationshipsCalls gets all the calls that were made to FindRelationships.
// Check the length with:
//
//	len(mockedRepo.FindRelationshipsCalls())
func (mock *RepoMock) FindRelationshipsCalls() []struct {
	Ctx   context.Context
	Query dto.FindRelationshipQuery
} {
	var calls []struct {
		Ctx   context.Context
		Query dto.FindRelationshipQuery
	}
	mock.lockFindRelationships.RLock()
	calls = mock.calls.FindRelationships
	mock.lockFindRelationships.RUnlock()
	return calls
}

// FindSamples calls FindSamplesFunc.
func (mock *RepoMock) FindSamples(query dto.SampleQuery) ([]dto.Sample, error) {
	if mock.FindSamplesFunc == nil {
		panic("RepoMock.FindSamplesFunc: method is nil but Repo.FindSamples was just called")
	}
	callInfo := struct {
		Query dto.SampleQuery
	}{
		Query: query,
	}
	mock.lockFindSamples.Lock()
	mock.calls.FindSamples = append(mock.calls.FindSamples, callInfo)
	mock.lockFindSamples.Unlock()
	return mock.FindSamplesFunc(query)
}

// FindSamplesCalls gets all the calls that were made to FindSamples.
// Check the length with:
//
//	len(mockedRepo.FindSamplesCalls())
func (mock *RepoMock) FindSamplesCalls() []struct {
	Query dto.SampleQuery
} {
	var calls []struct {
		Query dto.SampleQuery
	}
	mock.lockFindSamples.RLock()
	calls = mock.calls.FindSamples
	mock.lockFindSamples.RUnlock()
	return calls
}

// GenerateBranchAndVersionID calls GenerateBranchAndVersionIDFunc.
func (mock *RepoMock) GenerateBranchAndVersionID(ctx context.Context, tenant_id string, context_id string, user_id string, tx *sql.Tx) (string, string) {
	if mock.GenerateBranchAndVersionIDFunc == nil {
		panic("RepoMock.GenerateBranchAndVersionIDFunc: method is nil but Repo.GenerateBranchAndVersionID was just called")
	}
	callInfo := struct {
		Ctx        context.Context
		Tenant_id  string
		Context_id string
		User_id    string
		Tx         *sql.Tx
	}{
		Ctx:        ctx,
		Tenant_id:  tenant_id,
		Context_id: context_id,
		User_id:    user_id,
		Tx:         tx,
	}
	mock.lockGenerateBranchAndVersionID.Lock()
	mock.calls.GenerateBranchAndVersionID = append(mock.calls.GenerateBranchAndVersionID, callInfo)
	mock.lockGenerateBranchAndVersionID.Unlock()
	return mock.GenerateBranchAndVersionIDFunc(ctx, tenant_id, context_id, user_id, tx)
}

// GenerateBranchAndVersionIDCalls gets all the calls that were made to GenerateBranchAndVersionID.
// Check the length with:
//
//	len(mockedRepo.GenerateBranchAndVersionIDCalls())
func (mock *RepoMock) GenerateBranchAndVersionIDCalls() []struct {
	Ctx        context.Context
	Tenant_id  string
	Context_id string
	User_id    string
	Tx         *sql.Tx
} {
	var calls []struct {
		Ctx        context.Context
		Tenant_id  string
		Context_id string
		User_id    string
		Tx         *sql.Tx
	}
	mock.lockGenerateBranchAndVersionID.RLock()
	calls = mock.calls.GenerateBranchAndVersionID
	mock.lockGenerateBranchAndVersionID.RUnlock()
	return calls
}

// NavigateDataModel calls NavigateDataModelFunc.
func (mock *RepoMock) NavigateDataModel(ctx context.Context, tenantID string, query models.NavigateDataModelQuery) (dto.Entities, dto.EntityElements, dto.Relationships, string, error) {
	if mock.NavigateDataModelFunc == nil {
		panic("RepoMock.NavigateDataModelFunc: method is nil but Repo.NavigateDataModel was just called")
	}
	callInfo := struct {
		Ctx      context.Context
		TenantID string
		Query    models.NavigateDataModelQuery
	}{
		Ctx:      ctx,
		TenantID: tenantID,
		Query:    query,
	}
	mock.lockNavigateDataModel.Lock()
	mock.calls.NavigateDataModel = append(mock.calls.NavigateDataModel, callInfo)
	mock.lockNavigateDataModel.Unlock()
	return mock.NavigateDataModelFunc(ctx, tenantID, query)
}

// NavigateDataModelCalls gets all the calls that were made to NavigateDataModel.
// Check the length with:
//
//	len(mockedRepo.NavigateDataModelCalls())
func (mock *RepoMock) NavigateDataModelCalls() []struct {
	Ctx      context.Context
	TenantID string
	Query    models.NavigateDataModelQuery
} {
	var calls []struct {
		Ctx      context.Context
		TenantID string
		Query    models.NavigateDataModelQuery
	}
	mock.lockNavigateDataModel.RLock()
	calls = mock.calls.NavigateDataModel
	mock.lockNavigateDataModel.RUnlock()
	return calls
}

// UpdateSample calls UpdateSampleFunc.
func (mock *RepoMock) UpdateSample(id string, request dto.UpdateSampleInput, tenantID *string) error {
	if mock.UpdateSampleFunc == nil {
		panic("RepoMock.UpdateSampleFunc: method is nil but Repo.UpdateSample was just called")
	}
	callInfo := struct {
		ID       string
		Request  dto.UpdateSampleInput
		TenantID *string
	}{
		ID:       id,
		Request:  request,
		TenantID: tenantID,
	}
	mock.lockUpdateSample.Lock()
	mock.calls.UpdateSample = append(mock.calls.UpdateSample, callInfo)
	mock.lockUpdateSample.Unlock()
	return mock.UpdateSampleFunc(id, request, tenantID)
}

// UpdateSampleCalls gets all the calls that were made to UpdateSample.
// Check the length with:
//
//	len(mockedRepo.UpdateSampleCalls())
func (mock *RepoMock) UpdateSampleCalls() []struct {
	ID       string
	Request  dto.UpdateSampleInput
	TenantID *string
} {
	var calls []struct {
		ID       string
		Request  dto.UpdateSampleInput
		TenantID *string
	}
	mock.lockUpdateSample.RLock()
	calls = mock.calls.UpdateSample
	mock.lockUpdateSample.RUnlock()
	return calls
}
